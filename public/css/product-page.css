/**
 * Product Page Styles
 *
 * Custom styles for the enhanced product page
 */

/* Image Gallery */
.product-image-container {
    overflow: hidden;
}

.main-product-image {
    transition: transform 0.3s ease-out;
}

.product-thumbnail {
    transition: all 0.2s ease-in-out;
}

.product-thumbnail:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Notifications */
.notification {
    opacity: 0;
    transform: translateY(1rem);
}

.notification.show {
    opacity: 1;
    transform: translateY(0);
}

/* Wishlist Button */
.wishlist-button svg {
    transition: all 0.2s ease;
}

.wishlist-button:hover svg {
    transform: scale(1.1);
}

/* Variant Selection */
.color-swatch {
    position: relative;
    transition: all 0.2s ease;
}

.color-swatch:hover {
    transform: scale(1.1);
}

.color-swatch-check {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.size-button, .attribute-button {
    transition: all 0.2s ease;
}

.size-button:hover, .attribute-button:hover {
    transform: translateY(-2px);
}

.variant-not-available {
    animation: fadeIn 0.3s ease-in-out;
}

/* Quantity Controls */
.quantity-btn {
    transition: all 0.2s ease;
}

.quantity-btn:hover {
    transform: scale(1.05);
}

/* Social Share Buttons */
.social-share-btn {
    transition: all 0.2s ease;
}

.social-share-btn:hover {
    transform: translateY(-2px);
}

/* Add to Cart Animation */
@keyframes addToCartSuccess {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.add-to-cart-success {
    animation: addToCartSuccess 0.5s ease;
}

/* Reviews Section */
.review-stars {
    display: inline-flex;
}

.write-review-button, .load-more-reviews-button {
    transition: all 0.2s ease;
}

.write-review-button:hover, .load-more-reviews-button:hover {
    transform: translateY(-1px);
}

/* Enhanced Mobile Optimizations */
@media (max-width: 768px) {
    .product-thumbnails {
        grid-template-columns: repeat(4, 1fr);
        gap: 0.5rem;
    }

    .product-thumbnails img {
        height: 3rem;
    }

    /* Touch-friendly controls */
    .color-swatch {
        min-width: 44px;
        min-height: 44px;
        margin: 0.25rem;
    }

    .size-button, .attribute-button {
        min-width: 44px;
        min-height: 44px;
        padding: 0.75rem;
    }

    .quantity-btn {
        min-width: 44px;
        min-height: 44px;
        font-size: 1.125rem;
    }

    .social-share-btn {
        min-width: 44px;
        min-height: 44px;
        padding: 0.75rem;
    }

    .wishlist-button {
        min-width: 44px;
        min-height: 44px;
        padding: 0.75rem;
    }

    /* Improved text readability */
    .product-price {
        font-size: 1.75rem;
    }

    .product-compare-price {
        font-size: 1.25rem;
    }

    /* Better spacing for mobile */
    .variant-attribute {
        margin-bottom: 1.5rem;
    }

    /* Disable hover effects on mobile */
    .product-thumbnail:hover,
    .color-swatch:hover,
    .size-button:hover,
    .attribute-button:hover,
    .quantity-btn:hover,
    .social-share-btn:hover,
    .wishlist-button:hover {
        transform: none;
    }
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
    .product-thumbnail {
        border-color: rgba(75, 85, 99, 0.5);
    }

    .notification {
        background-color: #1f2937;
        color: #f3f4f6;
    }
}

/* Animation for notifications */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translate3d(0, -20px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes fadeOutUp {
    from {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
    to {
        opacity: 0;
        transform: translate3d(0, -20px, 0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.animate-fade-in-down {
    animation: fadeInDown 0.3s ease forwards;
}

.animate-fade-out {
    animation: fadeOutUp 0.3s ease forwards;
}

/* Accessibility Improvements */

/* Focus indicators */
.color-swatch:focus,
.size-button:focus,
.attribute-button:focus,
.quantity-btn:focus,
.social-share-btn:focus,
.wishlist-button:focus,
.product-thumbnail:focus,
.main-product-image:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .color-swatch {
        border: 2px solid;
    }

    .size-button, .attribute-button {
        border: 2px solid;
    }

    .product-price {
        font-weight: bold;
    }

    .stock-status {
        font-weight: bold;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .main-product-image,
    .product-thumbnail,
    .color-swatch,
    .size-button,
    .attribute-button,
    .quantity-btn,
    .social-share-btn,
    .wishlist-button,
    .write-review-button,
    .load-more-reviews-button {
        transition: none;
    }

    .add-to-cart-success {
        animation: none;
    }

    .animate-fade-in-down,
    .animate-fade-out {
        animation: none;
    }
}

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Skip to content link */
.skip-to-content {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    z-index: 1000;
}

.skip-to-content:focus {
    top: 6px;
}

/* Enhanced notification accessibility */
.product-notification {
    role: alert;
    aria-live: polite;
}

.product-notification[data-type="error"] {
    aria-live: assertive;
}

/* Loading state accessibility */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Ensure sufficient color contrast */
.text-gray-500 {
    color: #6b7280; /* Ensure this meets WCAG AA standards */
}

.text-gray-600 {
    color: #4b5563; /* Ensure this meets WCAG AA standards */
}

/* Dark mode accessibility improvements */
@media (prefers-color-scheme: dark) {
    .color-swatch:focus,
    .size-button:focus,
    .attribute-button:focus,
    .quantity-btn:focus,
    .social-share-btn:focus,
    .wishlist-button:focus,
    .product-thumbnail:focus,
    .main-product-image:focus {
        outline-color: #60a5fa;
    }

    .text-gray-500 {
        color: #9ca3af; /* Better contrast in dark mode */
    }

    .text-gray-600 {
        color: #d1d5db; /* Better contrast in dark mode */
    }
}
