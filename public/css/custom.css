/* Custom CSS for modern, eye-friendly design */

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom focus styles for better accessibility */
*:focus {
    outline: 2px solid rgba(59, 130, 246, 0.5);
    outline-offset: 2px;
}

/* Improved typography for better readability */
body {
    font-feature-settings: "kern", "liga", "calt";
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    letter-spacing: -0.011em;
}

/* Reduced contrast for dark mode to be easier on the eyes */
.dark body {
    background-color: #121826;
    color: #e2e8f0;
}

.dark .prose {
    color: #e2e8f0;
}

.dark .prose h1,
.dark .prose h2,
.dark .prose h3,
.dark .prose h4 {
    color: #f8fafc;
}

.dark .prose strong {
    color: #f1f5f9;
}

/* Softer shadows for a more modern look */
.shadow-soft {
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.03);
}

.dark .shadow-soft {
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
}

/* Gradient text for headings */
.gradient-text {
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Animations for a more dynamic feel */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
    animation: slideUp 0.5s ease-in-out;
}

.animate-blob {
    animation: blob 7s infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes blob {
    0% {
        transform: translate(0px, 0px) scale(1);
    }
    33% {
        transform: translate(30px, -50px) scale(1.1);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
        transform: translate(0px, 0px) scale(1);
    }
}

/* Custom card styles */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Custom button styles */
.btn-primary {
    padding: 0.75rem 1.5rem;
    background-color: #2563eb;
    color: white;
    font-weight: 500;
    border-radius: 0.5rem;
    transition-property: all;
    transition-duration: 300ms;
    transform: scale(1);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.btn-primary:hover {
    background-color: #1d4ed8;
    transform: scale(1.05);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.btn-primary:focus {
    outline: none;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
    --tw-ring-color: rgba(59, 130, 246, 0.5);
    --tw-ring-offset-width: 2px;
}

.btn-secondary {
    padding: 0.75rem 1.5rem;
    background-color: transparent;
    border: 2px solid white;
    color: white;
    font-weight: 500;
    border-radius: 0.5rem;
    transition-property: all;
    transition-duration: 300ms;
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.btn-tertiary {
    padding: 0.75rem 1.5rem;
    background-color: white;
    color: #2563eb;
    font-weight: 500;
    border-radius: 0.5rem;
    transition-property: all;
    transition-duration: 300ms;
    transform: scale(1);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.btn-tertiary:hover {
    background-color: #eff6ff;
    transform: scale(1.05);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Custom form styles */
.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid #d1d5db;
    background-color: white;
    color: #111827;
    transition-property: all;
    transition-duration: 200ms;
}

.form-input:focus {
    outline: none;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
    --tw-ring-color: rgba(59, 130, 246, 0.5);
    border-color: transparent;
}

.dark .form-input {
    border-color: #4b5563;
    background-color: #374151;
    color: white;
}

.dark .form-input:focus {
    --tw-ring-color: rgba(96, 165, 250, 0.5);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.dark .form-label {
    color: #d1d5db;
}

/* Custom section styles */
.section-padding {
    padding-top: 4rem;
    padding-bottom: 4rem;
}

@media (min-width: 768px) {
    .section-padding {
        padding-top: 6rem;
        padding-bottom: 6rem;
    }
}

/* Grid pattern background */
.bg-grid-pattern {
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Custom scrollbar for better UX */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.dark ::-webkit-scrollbar-track {
    background: #1e293b;
}

::-webkit-scrollbar-thumb {
    background-color: #94a3b8;
    border-radius: 6px;
    border: 3px solid #f1f5f9;
}

.dark ::-webkit-scrollbar-thumb {
    background-color: #475569;
    border: 3px solid #1e293b;
}

::-webkit-scrollbar-thumb:hover {
    background-color: #64748b;
}

.dark ::-webkit-scrollbar-thumb:hover {
    background-color: #334155;
}

/* Authentication Page Specific Styles */

/* Enhanced backdrop blur for auth cards */
.auth-card {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
}

/* Enhanced form input styles for auth pages */
.auth-input {
    transition: all 0.2s ease-in-out;
}

.auth-input:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
}

/* Enhanced button styles for auth pages */
.auth-button {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    transition: all 0.2s ease-in-out;
}

.auth-button:hover {
    background: linear-gradient(135deg, #5855eb 0%, #7c3aed 100%);
    transform: translateY(-1px);
    box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
}

/* Loading states for auth forms */
.auth-loading {
    position: relative;
    overflow: hidden;
}

.auth-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Responsive improvements for auth pages */
@media (max-width: 640px) {
    .auth-card {
        margin: 1rem;
        border-radius: 1rem;
    }

    .auth-header {
        padding: 1.5rem 1rem;
    }
}

/* High contrast mode support for auth pages */
@media (prefers-contrast: high) {
    .auth-button {
        background: #000;
        color: #fff;
        border: 2px solid #000;
    }

    .auth-input {
        border: 2px solid #000;
    }
}

/* Reduced motion support for auth pages */
@media (prefers-reduced-motion: reduce) {
    .auth-loading::after {
        animation: none;
    }

    .auth-button:hover {
        transform: none;
    }

    .auth-input:focus {
        transform: none;
    }
}
