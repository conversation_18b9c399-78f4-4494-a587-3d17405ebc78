/**
 * Wishlist Management JavaScript
 * Handles adding, removing, and managing wishlist items
 */

// Wishlist utility object
const Wishlist = {
    // CSRF token for requests
    csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),

    // Initialize wishlist functionality
    init() {
        this.bindEvents();
        this.updateWishlistButtons();
    },

    // Bind event listeners
    bindEvents() {
        // Update wishlist button states on page load
        document.addEventListener('DOMContentLoaded', () => {
            this.updateWishlistButtons();
        });
    },

    // Enhanced fetch with CSRF protection
    async fetchWithCSRF(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            credentials: 'same-origin'
        };

        // Only set Content-Type to JSON if we're not sending FormData
        if (!(options.body instanceof FormData)) {
            defaultOptions.headers['Content-Type'] = 'application/json';
        }

        if (this.csrfToken) {
            defaultOptions.headers['X-CSRF-TOKEN'] = this.csrfToken;
        }

        const mergedOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const response = await fetch(url, mergedOptions);
            const data = await response.json();

            if (!response.ok) {
                // Handle validation errors (422) and other errors
                if (response.status === 422 && data.errors) {
                    const errorMessages = Object.values(data.errors).flat();
                    this.showNotification(errorMessages.join(', '), 'error');
                } else if (data.message) {
                    this.showNotification(data.message, 'error');
                } else {
                    this.showNotification(`Error: ${response.status} - ${response.statusText}`, 'error');
                }

                const error = new Error(`HTTP error! status: ${response.status}`);
                error.response = response;
                error.data = data;
                throw error;
            }

            return data;
        } catch (error) {

            // Only show generic error if we haven't already shown a specific one
            if (!error.response) {
                this.showNotification('Network error. Please check your connection and try again.', 'error');
            }

            throw error;
        }
    },

    // Show notification
    showNotification(message, type = 'info', duration = 5000) {
        // Get or create notification container
        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'fixed top-20 right-5 z-50 space-y-4 w-full max-w-sm pointer-events-none';
            document.body.appendChild(container);
        }

        // Remove existing notifications of the same type
        const existingNotifications = container.querySelectorAll(`.wishlist-notification[data-type="${type}"]`);
        existingNotifications.forEach(notification => notification.remove());

        const notification = document.createElement('div');
        notification.className = `wishlist-notification p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full pointer-events-auto`;
        notification.setAttribute('data-type', type);
        notification.setAttribute('role', 'alert');
        notification.setAttribute('aria-live', type === 'error' ? 'assertive' : 'polite');

        const typeClasses = {
            success: 'bg-green-500 text-white border-l-4 border-green-700',
            error: 'bg-red-500 text-white border-l-4 border-red-700',
            warning: 'bg-yellow-500 text-black border-l-4 border-yellow-700',
            info: 'bg-blue-500 text-white border-l-4 border-blue-700'
        };

        const typeIcons = {
            success: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>`,
            error: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                   </svg>`,
            warning: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                       <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                     </svg>`,
            info: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>`
        };

        notification.className += ` ${typeClasses[type] || typeClasses.info}`;
        notification.innerHTML = `
            <div class="flex items-start">
                <div class="flex-shrink-0 mr-3">
                    ${typeIcons[type] || typeIcons.info}
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium">${message}</p>
                </div>
                <button type="button" class="ml-2 flex-shrink-0 text-current hover:opacity-75 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 rounded" onclick="this.parentElement.parentElement.remove()">
                    <span class="sr-only">Close notification</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;

        container.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove
        if (duration > 0) {
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }, duration);
        }

        return notification;
    },

    // Show custom confirmation dialog
    showConfirmDialog(title, message, confirmText = 'Confirm', cancelText = 'Cancel') {
        return new Promise((resolve) => {
            // Create modal backdrop
            const backdrop = document.createElement('div');
            backdrop.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
            backdrop.style.backdropFilter = 'blur(4px)';

            // Create modal content
            const modal = document.createElement('div');
            modal.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-auto transform transition-all duration-300 scale-95 opacity-0';

            modal.innerHTML = `
                <div class="p-6">
                    <!-- Header -->
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 w-10 h-10 mx-auto bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- Title -->
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white text-center mb-2">
                        ${title}
                    </h3>

                    <!-- Message -->
                    <p class="text-sm text-gray-600 dark:text-gray-300 text-center mb-6">
                        ${message}
                    </p>

                    <!-- Buttons -->
                    <div class="flex space-x-3">
                        <button type="button" class="cancel-btn flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                            ${cancelText}
                        </button>
                        <button type="button" class="confirm-btn flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                            ${confirmText}
                        </button>
                    </div>
                </div>
            `;

            backdrop.appendChild(modal);
            document.body.appendChild(backdrop);

            // Animate in
            setTimeout(() => {
                modal.classList.remove('scale-95', 'opacity-0');
                modal.classList.add('scale-100', 'opacity-100');
            }, 10);

            // Handle button clicks
            const cancelBtn = modal.querySelector('.cancel-btn');
            const confirmBtn = modal.querySelector('.confirm-btn');

            const cleanup = () => {
                modal.classList.add('scale-95', 'opacity-0');
                setTimeout(() => {
                    if (backdrop.parentNode) {
                        backdrop.remove();
                    }
                }, 300);
            };

            cancelBtn.addEventListener('click', () => {
                cleanup();
                resolve(false);
            });

            confirmBtn.addEventListener('click', () => {
                cleanup();
                resolve(true);
            });

            // Close on backdrop click
            backdrop.addEventListener('click', (e) => {
                if (e.target === backdrop) {
                    cleanup();
                    resolve(false);
                }
            });

            // Close on Escape key
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    cleanup();
                    resolve(false);
                    document.removeEventListener('keydown', handleEscape);
                }
            };
            document.addEventListener('keydown', handleEscape);
        });
    },

    // Toggle wishlist status for a product
    async toggle(productId) {
        try {
            const data = await this.fetchWithCSRF('/wishlist/toggle', {
                method: 'POST',
                body: JSON.stringify({ product_id: productId })
            });

            if (data.success) {
                this.showNotification(data.message, 'success');
                this.updateWishlistButton(productId, data.in_wishlist);
                this.updateWishlistCount(data.wishlist_count);
                return data;
            }
        } catch (error) {
            return null;
        }
    },

    // Add product to wishlist
    async add(productId) {
        try {
            const data = await this.fetchWithCSRF('/wishlist', {
                method: 'POST',
                body: JSON.stringify({ product_id: productId })
            });

            if (data.success) {
                this.showNotification(data.message, 'success');
                this.updateWishlistButton(productId, true);
                this.updateWishlistCount(data.wishlist_count);
                return data;
            }
        } catch (error) {
            return null;
        }
    },

    // Remove product from wishlist
    async remove(productId) {
        try {
            const url = `/wishlist/${productId}`;
            const data = await this.fetchWithCSRF(url, {
                method: 'DELETE'
            });

            if (data && data.success) {
                this.showNotification(data.message, 'info');
                this.updateWishlistButton(productId, false);
                this.updateWishlistCount(data.wishlist_count);

                // Remove item from wishlist page if we're on it
                const wishlistItem = document.querySelector(`.wishlist-item[data-product-id="${productId}"]`);

                if (wishlistItem) {
                    wishlistItem.style.transition = 'opacity 0.3s ease';
                    wishlistItem.style.opacity = '0';
                    setTimeout(() => {
                        wishlistItem.remove();

                        // Check if wishlist is now empty
                        const remainingItems = document.querySelectorAll('.wishlist-item');
                        if (remainingItems.length === 0) {
                            location.reload(); // Reload to show empty state
                        }
                    }, 300);
                }

                return data;
            } else {
                if (data && data.message) {
                    this.showNotification(data.message, 'error');
                }
                return null;
            }
        } catch (error) {
            this.showNotification('Failed to remove item from wishlist. Please try again.', 'error');
            return null;
        }
    },

    // Clear entire wishlist
    async clear() {
        const confirmed = await this.showConfirmDialog(
            'Clear Wishlist',
            'Are you sure you want to remove all items from your wishlist? This action cannot be undone.',
            'Clear All',
            'Cancel'
        );

        if (!confirmed) {
            // Re-enable the clear button if user cancels
            this.enableClearButton();
            return;
        }

        try {
            const data = await this.fetchWithCSRF('/wishlist', {
                method: 'DELETE'
            });

            if (data.success) {
                this.showNotification(data.message, 'info');
                this.updateWishlistCount(0);

                // Reload page to show empty state
                setTimeout(() => {
                    location.reload();
                }, 1000);

                return data;
            }
        } catch (error) {
            return null;
        }
    },

    // Update wishlist button appearance
    updateWishlistButton(productId, inWishlist) {
        const buttons = document.querySelectorAll(`[data-product-id="${productId}"] .wishlist-button, .wishlist-button[data-product-id="${productId}"]`);

        buttons.forEach(button => {
            const icon = button.querySelector('svg');
            if (icon) {
                if (inWishlist) {
                    icon.classList.remove('text-gray-400');
                    icon.classList.add('text-red-500');
                    icon.setAttribute('fill', 'currentColor');
                } else {
                    icon.classList.remove('text-red-500');
                    icon.classList.add('text-gray-400');
                    icon.setAttribute('fill', 'none');
                }
            }
        });
    },

    // Update wishlist count in navigation
    updateWishlistCount(count) {
        const countElements = document.querySelectorAll('.wishlist-count');
        countElements.forEach(element => {
            element.textContent = count;
            if (count > 0) {
                element.classList.remove('hidden');
                element.style.display = 'inline-flex';
            } else {
                element.classList.add('hidden');
                element.style.display = 'none';
            }
        });
    },

    // Update all wishlist buttons on page load
    async updateWishlistButtons() {
        // This would typically be called on page load to set correct states
        // Implementation depends on how you want to handle initial state
    },

    // Enable clear button
    enableClearButton() {
        const clearButton = document.querySelector('.clear-wishlist-btn');
        if (clearButton) {
            clearButton.disabled = false;
            clearButton.style.opacity = '1';
        }
    },

    // Disable clear button
    disableClearButton() {
        const clearButton = document.querySelector('.clear-wishlist-btn');
        if (clearButton) {
            clearButton.disabled = true;
            clearButton.style.opacity = '0.5';
        }
    }
};

// Global functions for use in templates with error handling
window.toggleWishlist = function(productId) {
    if (!productId) {
        return Promise.reject('Product ID is required');
    }
    return Wishlist.toggle(productId);
};

window.addToWishlist = function(productId) {
    if (!productId) {
        return Promise.reject('Product ID is required');
    }
    return Wishlist.add(productId);
};

window.removeFromWishlist = function(productId) {
    if (!productId) {
        return Promise.reject('Product ID is required');
    }
    return Wishlist.remove(productId);
};

window.clearWishlist = function() {
    return Wishlist.clear();
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Ensure Wishlist object is available
    if (typeof Wishlist !== 'undefined') {
        Wishlist.init();
    }
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading, wait for DOMContentLoaded
} else {
    // DOM is already loaded
    if (typeof Wishlist !== 'undefined') {
        Wishlist.init();
    }
}
