/**
 * reCAPTCHA Invisible Implementation
 *
 * This script handles the invisible reCAPTCHA functionality for forms.
 * It automatically attaches to forms with the 'needs-validation' class.
 */

// Function to handle reCAPTCHA errors and display messages
function handleRecaptchaError(form, errorMessage) {
    console.error(errorMessage);

    // Re-enable the submit button if it was disabled
    const submitButton = form.querySelector('button[type="submit"]');
    if (submitButton) {
        submitButton.disabled = false;
        // Determine original text based on form ID
        if (form.id === 'register-form') {
            submitButton.innerHTML = submitButton.dataset.originalText || 'Create Account';
        } else if (form.id === 'login-form') {
            submitButton.innerHTML = submitButton.dataset.originalText || 'Sign in';
        } else {
            submitButton.innerHTML = submitButton.dataset.originalText || 'Submit';
        }
    }

    // Display an error message to the user
    const existingError = form.querySelector('.recaptcha-error');
    if (!existingError) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'text-sm text-red-600 mt-2 recaptcha-error';
        errorDiv.textContent = errorMessage;
        
        const recaptchaContainer = form.querySelector('.g-recaptcha');
        if (recaptchaContainer && recaptchaContainer.nextSibling) {
            recaptchaContainer.parentNode.insertBefore(errorDiv, recaptchaContainer.nextSibling);
        } else {
            form.appendChild(errorDiv);
        }
        
        setTimeout(() => errorDiv.remove(), 5000);
    }
    // Reset the reCAPTCHA widget if it exists
    if (typeof grecaptcha !== 'undefined' && grecaptcha.reset) {
        grecaptcha.reset();
    }
}

// Ensure reCAPTCHA is ready before executing
if (typeof grecaptcha !== 'undefined') {
    grecaptcha.ready(function() {
        // Attach event listener to all forms with the 'needs-validation' class
        document.querySelectorAll('form.needs-validation').forEach(function(form) {
            form.addEventListener('submit', function(event) {
                event.preventDefault(); // Prevent default form submission

                // Check form validity
                if (!form.checkValidity()) {
                    event.stopPropagation();
                    form.classList.add('was-validated');
                    return;
                }

                // Show loading state on button
                const submitButton = form.querySelector('button[type="submit"]');
                if (submitButton) {
                    submitButton.dataset.originalText = submitButton.innerHTML; // Store original text
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';
                }

                // Get the site key from the reCAPTCHA container
                const recaptchaContainer = form.querySelector('.g-recaptcha');
                const siteKey = recaptchaContainer ? recaptchaContainer.dataset.sitekey : null;

                if (!siteKey) {
                    handleRecaptchaError(form, 'reCAPTCHA site key not found in the HTML.');
                    return;
                }

                // Execute reCAPTCHA and handle the token
                grecaptcha.execute(siteKey, {action: 'submit'}).then(function(token) {
                    console.log('reCAPTCHA token received:', token);
                    // Create a hidden input for the reCAPTCHA response if it doesn't exist
                    let recaptchaInput = form.querySelector('input[name="g-recaptcha-response"]');
                    if (!recaptchaInput) {
                        recaptchaInput = document.createElement('input');
                        recaptchaInput.type = 'hidden';
                        recaptchaInput.name = 'g-recaptcha-response';
                        recaptchaInput.id = 'g-recaptcha-response';
                        form.appendChild(recaptchaInput);
                    }
                    recaptchaInput.value = token;
                    console.log('Submitting form with reCAPTCHA token');
                    form.submit();
                }).catch(function(error) {
                    handleRecaptchaError(form, 'reCAPTCHA execution failed: ' + error);
                });
            });
        });
    });
} else {
    console.error('grecaptcha is not defined. reCAPTCHA API might not be loaded.');
}
