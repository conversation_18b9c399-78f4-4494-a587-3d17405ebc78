/**
 * Enhanced Cart Functionality
 * Provides real-time cart updates, validation, and improved UX
 */

class EnhancedCart {
    constructor() {
        this.cartSummaryUrl = '/cart/summary';
        this.cartValidateUrl = '/cart/validate';
        this.cartRecommendationsUrl = '/cart/recommendations';
        this.updateDelay = 500; // Debounce delay for quantity updates
        this.validationInterval = 30000; // Validate cart every 30 seconds
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.startPeriodicValidation();
        this.loadCartSummary();
    }

    bindEvents() {
        // Quantity input changes with debouncing
        $(document).on('input', '.cart-quantity-input', (e) => {
            this.debounce(() => {
                this.updateQuantity(e.target);
            }, this.updateDelay)();
        });

        // Remove item buttons
        $(document).on('click', '.remove-cart-item', (e) => {
            e.preventDefault();
            this.removeItem(e.target);
        });

        // Clear cart button
        $(document).on('click', '.clear-cart-btn', (e) => {
            e.preventDefault();
            this.clearCart();
        });

        // Validate cart button
        $(document).on('click', '.validate-cart-btn', (e) => {
            e.preventDefault();
            this.validateCart();
        });

        // Load recommendations
        $(document).on('click', '.load-recommendations-btn', (e) => {
            e.preventDefault();
            this.loadRecommendations();
        });

        // Add recommended item to cart
        $(document).on('click', '.add-recommendation', (e) => {
            e.preventDefault();
            this.addRecommendationToCart(e.target);
        });
    }

    /**
     * Update item quantity with optimistic UI updates
     */
    updateQuantity(input) {
        const $input = $(input);
        const itemId = $input.data('item-id');
        const newQuantity = parseInt($input.val());
        const $row = $input.closest('tr');
        
        if (newQuantity < 1) {
            this.showNotification('Quantity must be at least 1', 'warning');
            $input.val(1);
            return;
        }

        // Show loading state
        this.showLoadingState($row);

        // Make AJAX request
        $.ajax({
            url: `/cart/items/${itemId}`,
            method: 'PATCH',
            data: {
                quantity: newQuantity,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: (response) => {
                if (response.success) {
                    this.updateCartDisplay(response.cart);
                    this.showNotification('Cart updated successfully', 'success');
                } else {
                    this.showNotification(response.message || 'Failed to update cart', 'error');
                    this.revertQuantityChange($input);
                }
            },
            error: (xhr) => {
                this.handleAjaxError(xhr);
                this.revertQuantityChange($input);
            },
            complete: () => {
                this.hideLoadingState($row);
            }
        });
    }

    /**
     * Remove item from cart
     */
    removeItem(button) {
        const $button = $(button);
        const itemId = $button.data('item-id');
        const $row = $button.closest('tr');
        
        if (!confirm('Are you sure you want to remove this item from your cart?')) {
            return;
        }

        this.showLoadingState($row);

        $.ajax({
            url: `/cart/items/${itemId}`,
            method: 'DELETE',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: (response) => {
                if (response.success) {
                    $row.fadeOut(300, () => {
                        $row.remove();
                        this.updateCartDisplay(response.cart);
                        this.checkEmptyCart();
                    });
                    this.showNotification('Item removed from cart', 'success');
                } else {
                    this.showNotification(response.message || 'Failed to remove item', 'error');
                }
            },
            error: (xhr) => {
                this.handleAjaxError(xhr);
            },
            complete: () => {
                this.hideLoadingState($row);
            }
        });
    }

    /**
     * Clear entire cart
     */
    clearCart() {
        if (!confirm('Are you sure you want to clear your entire cart?')) {
            return;
        }

        this.showGlobalLoading();

        $.ajax({
            url: '/cart',
            method: 'DELETE',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: (response) => {
                if (response.success) {
                    location.reload(); // Reload to show empty cart state
                } else {
                    this.showNotification(response.message || 'Failed to clear cart', 'error');
                }
            },
            error: (xhr) => {
                this.handleAjaxError(xhr);
            },
            complete: () => {
                this.hideGlobalLoading();
            }
        });
    }

    /**
     * Validate cart and show issues
     */
    validateCart() {
        this.showGlobalLoading();

        $.ajax({
            url: this.cartValidateUrl,
            method: 'GET',
            success: (response) => {
                if (response.success) {
                    if (response.valid) {
                        this.showNotification('Your cart is valid and ready for checkout!', 'success');
                    } else {
                        this.displayValidationIssues(response.issues, response.messages);
                    }
                }
            },
            error: (xhr) => {
                this.handleAjaxError(xhr);
            },
            complete: () => {
                this.hideGlobalLoading();
            }
        });
    }

    /**
     * Load cart recommendations
     */
    loadRecommendations() {
        const $container = $('.recommendations-container');
        $container.html('<div class="text-center py-4"><div class="spinner"></div> Loading recommendations...</div>');

        $.ajax({
            url: this.cartRecommendationsUrl,
            method: 'GET',
            success: (response) => {
                if (response.success && response.recommendations.length > 0) {
                    this.displayRecommendations(response.recommendations);
                } else {
                    $container.html('<p class="text-gray-500 text-center py-4">No recommendations available at this time.</p>');
                }
            },
            error: (xhr) => {
                $container.html('<p class="text-red-500 text-center py-4">Failed to load recommendations.</p>');
            }
        });
    }

    /**
     * Load cart summary for header/mini-cart
     */
    loadCartSummary() {
        $.ajax({
            url: this.cartSummaryUrl,
            method: 'GET',
            success: (response) => {
                if (response.success) {
                    this.updateHeaderCart(response.cart);
                }
            },
            error: (xhr) => {
                console.error('Failed to load cart summary:', xhr);
            }
        });
    }

    /**
     * Start periodic cart validation
     */
    startPeriodicValidation() {
        setInterval(() => {
            if ($('.cart-items-table').length > 0) {
                this.validateCartSilently();
            }
        }, this.validationInterval);
    }

    /**
     * Validate cart silently (no notifications)
     */
    validateCartSilently() {
        $.ajax({
            url: this.cartValidateUrl,
            method: 'GET',
            success: (response) => {
                if (response.success && !response.valid) {
                    // Show subtle notification about cart changes
                    this.showNotification('Your cart has been updated due to price or availability changes.', 'info');
                    // Optionally reload the page or update the display
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                }
            },
            error: (xhr) => {
                // Silently handle errors for background validation
                console.error('Background cart validation failed:', xhr);
            }
        });
    }

    /**
     * Display validation issues
     */
    displayValidationIssues(issues, messages) {
        let html = '<div class="validation-issues mb-4">';
        html += '<h3 class="text-lg font-semibold text-red-600 mb-2">Cart Issues Found:</h3>';
        
        messages.forEach(message => {
            const alertClass = message.type === 'error' ? 'bg-red-50 border-red-500 text-red-700' :
                              message.type === 'warning' ? 'bg-yellow-50 border-yellow-500 text-yellow-700' :
                              'bg-blue-50 border-blue-500 text-blue-700';
            
            html += `<div class="alert ${alertClass} p-3 rounded border-l-4 mb-2">
                        ${message.message}
                     </div>`;
        });
        
        html += '</div>';
        
        $('.cart-validation-messages').html(html);
    }

    /**
     * Display recommendations
     */
    displayRecommendations(recommendations) {
        let html = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">';
        
        recommendations.forEach(product => {
            const variant = product.variants[0];
            const imageUrl = product.media && product.media.length > 0 ? 
                            product.media[0].original_url : '/images/placeholder.jpg';
            
            html += `
                <div class="recommendation-item border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <img src="${imageUrl}" alt="${product.name}" class="w-full h-32 object-cover rounded mb-2">
                    <h4 class="font-semibold text-sm mb-1">${product.name}</h4>
                    <p class="text-gray-600 text-sm mb-2">$${variant.price}</p>
                    <button class="add-recommendation bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600 transition-colors"
                            data-variant-id="${variant.id}">
                        Add to Cart
                    </button>
                </div>
            `;
        });
        
        html += '</div>';
        $('.recommendations-container').html(html);
    }

    /**
     * Utility functions
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    showNotification(message, type = 'info') {
        // Implementation depends on your notification system
        // This is a basic example
        const alertClass = type === 'error' ? 'alert-danger' :
                          type === 'warning' ? 'alert-warning' :
                          type === 'success' ? 'alert-success' : 'alert-info';
        
        const notification = $(`
            <div class="notification ${alertClass} fixed top-4 right-4 p-4 rounded shadow-lg z-50 max-w-sm">
                ${message}
                <button class="close-notification float-right ml-2">&times;</button>
            </div>
        `);
        
        $('body').append(notification);
        
        setTimeout(() => {
            notification.fadeOut(() => notification.remove());
        }, 5000);
    }

    showLoadingState($element) {
        $element.addClass('loading').find('button').prop('disabled', true);
    }

    hideLoadingState($element) {
        $element.removeClass('loading').find('button').prop('disabled', false);
    }

    showGlobalLoading() {
        $('body').addClass('loading');
    }

    hideGlobalLoading() {
        $('body').removeClass('loading');
    }

    handleAjaxError(xhr) {
        let message = 'An error occurred. Please try again.';
        
        if (xhr.responseJSON && xhr.responseJSON.message) {
            message = xhr.responseJSON.message;
        } else if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
            message = Object.values(xhr.responseJSON.errors).flat().join(', ');
        }
        
        this.showNotification(message, 'error');
    }
}

// Initialize enhanced cart when DOM is ready
$(document).ready(() => {
    window.enhancedCart = new EnhancedCart();
});

// Close notification handler
$(document).on('click', '.close-notification', function() {
    $(this).closest('.notification').fadeOut(() => $(this).remove());
});
