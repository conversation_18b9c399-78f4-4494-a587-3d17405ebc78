<?php

/**
 * Basic cart functionality test
 */

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 Basic Cart Functionality Test\n";
echo "================================\n\n";

try {
    // Test service instantiation
    $cartService = app(\App\Services\OptimizedCartService::class);
    $validationService = app(\App\Services\CartValidationService::class);
    
    echo "✅ Services instantiated successfully\n";
    
    // Test cache functionality
    \Illuminate\Support\Facades\Cache::put('test_cart_cache', 'working', 60);
    $cacheValue = \Illuminate\Support\Facades\Cache::get('test_cart_cache');
    
    if ($cacheValue === 'working') {
        echo "✅ Cache is working\n";
    } else {
        echo "❌ Cache is not working\n";
    }
    
    // Test database connection
    $dbConnection = \Illuminate\Support\Facades\DB::connection();
    $dbConnection->getPdo();
    echo "✅ Database connection working\n";
    
    // Test if we can query basic models
    $userCount = \App\Models\User::count();
    $productCount = \App\Models\Product::count();
    $cartCount = \App\Models\Cart::count();
    
    echo "✅ Database queries working:\n";
    echo "   - Users: {$userCount}\n";
    echo "   - Products: {$productCount}\n";
    echo "   - Carts: {$cartCount}\n";
    
    // Test cart summary with empty cart
    $emptySummary = $cartService->getCartSummary('non-existent-cart-id');
    
    if ($emptySummary['total_items'] === 0 && $emptySummary['subtotal'] === 0) {
        echo "✅ Empty cart summary working\n";
    } else {
        echo "❌ Empty cart summary not working\n";
        var_dump($emptySummary);
    }
    
    // Test validation with empty array
    $emptyValidation = $validationService->validateCart(new \App\Models\Cart());
    
    if ($emptyValidation['valid'] === true) {
        echo "✅ Empty cart validation working\n";
    } else {
        echo "❌ Empty cart validation not working\n";
        var_dump($emptyValidation);
    }
    
    echo "\n🎉 Basic functionality tests completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
