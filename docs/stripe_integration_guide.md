# 🚀 Stripe Integration Complete Guide

## 📋 Prerequisites

### What You Need
- [ ] Stripe Account ([stripe.com](https://stripe.com))
- [ ] Laravel Application (✅ Already setup)
- [ ] SSL Certificate (for production)
- [ ] Domain/Server (for webhooks)

### Required Information
- [ ] Business Information
- [ ] Bank Account Details
- [ ] Tax Information (for live mode)

---

## 🔧 Step-by-Step Setup

### Step 1: Stripe Dashboard Setup

#### 1.1 Create Stripe Account
1. Go to [dashboard.stripe.com](https://dashboard.stripe.com)
2. Sign up or log in
3. Complete business verification (for live mode)

#### 1.2 Get API Keys
1. Navigate to **Developers** → **API Keys**
2. Copy your keys:
   ```
   Publishable key: pk_test_... (for frontend)
   Secret key: sk_test_... (for backend)
   ```

#### 1.3 Configure Webhooks
1. Go to **Developers** → **Webhooks**
2. Click **"Add endpoint"**
3. **Endpoint URL**: `https://yourdomain.com/webhooks/stripe`
4. **Events to send**:
   ```
   ✅ payment_intent.succeeded
   ✅ payment_intent.payment_failed
   ✅ payment_intent.canceled
   ✅ charge.succeeded
   ✅ charge.failed
   ✅ charge.refunded
   ```
5. **Copy Webhook Secret**: `whsec_...`

### Step 2: Environment Configuration

#### 2.1 Update .env File
```env
# Stripe Configuration
STRIPE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_CURRENCY=usd

# Optional Settings
STRIPE_STATEMENT_DESCRIPTOR=YourBusiness
```

#### 2.2 Clear Configuration Cache
```bash
php artisan config:clear
php artisan cache:clear
```

### Step 3: Test Your Integration

#### 3.1 Run Test Script
```bash
php tests/stripe-test.php
```

#### 3.2 Expected Output
```
✅ Test publishable key found
✅ Test secret key found
✅ Webhook secret configured
✅ Successfully connected to Stripe
✅ Payment Intent created successfully
✅ Webhook endpoint is accessible
```

---

## 🧪 Testing

### Test Card Numbers

#### Successful Payments
```
Card Number: 4242 4242 4242 4242
Expiry: Any future date (e.g., 12/25)
CVC: Any 3 digits (e.g., 123)
ZIP: Any 5 digits (e.g., 12345)
```

#### Failed Payments
```
Declined: 4000 0000 0000 0002
Insufficient Funds: 4000 0000 0000 9995
Expired Card: 4000 0000 0000 0069
Incorrect CVC: 4000 0000 0000 0127
```

#### Special Cases
```
3D Secure: 4000 0000 0000 3220
International: 4000 0000 0000 0036
```

### Testing Process
1. **Navigate to payment page**
2. **Use test card numbers** above
3. **Complete payment flow**
4. **Check Stripe Dashboard** for transactions
5. **Verify webhook delivery** in Dashboard

---

## 🔍 Troubleshooting

### Common Issues

#### Issue: "Invalid API Key"
**Solution:**
```bash
# Check .env file
grep STRIPE_ .env

# Clear cache
php artisan config:clear
```

#### Issue: "Webhook signature verification failed"
**Solution:**
1. Check webhook secret in Stripe Dashboard
2. Update `STRIPE_WEBHOOK_SECRET` in .env
3. Ensure endpoint URL is correct

#### Issue: "Payment form not loading"
**Solution:**
1. Check browser console for errors
2. Verify Stripe publishable key
3. Check network connectivity

#### Issue: "Currency mismatch"
**Solution:**
```env
# Match your Stripe account currency
STRIPE_CURRENCY=eur  # or usd, gbp, etc.
```

### Debug Commands
```bash
# Check configuration
php artisan config:show payment-gateways.stripe

# Test webhook endpoint
curl -X POST https://yourdomain.com/webhooks/stripe \
  -H "Content-Type: application/json" \
  -H "Stripe-Signature: test" \
  -d '{"test": true}'

# Check logs
tail -f storage/logs/laravel.log
```

---

## 🚀 Going Live (Production)

### Step 1: Business Verification
1. **Complete Stripe onboarding**
2. **Provide required documents**
3. **Verify bank account**
4. **Set up tax information**

### Step 2: Get Live API Keys
1. **Activate live mode** in Stripe Dashboard
2. **Get live API keys**:
   ```
   pk_live_... (publishable)
   sk_live_... (secret)
   ```

### Step 3: Update Production Environment
```env
# Production .env
STRIPE_KEY=pk_live_your_live_publishable_key
STRIPE_SECRET=sk_live_your_live_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_live_webhook_secret
```

### Step 4: Update Webhooks
1. **Create new webhook** for production domain
2. **Use same events** as test mode
3. **Update webhook secret** in production .env

### Step 5: Security Checklist
- [ ] SSL certificate installed
- [ ] Webhook signature verification enabled
- [ ] API keys secured (not in version control)
- [ ] Error logging configured
- [ ] Monitoring alerts set up

---

## 📊 Monitoring & Maintenance

### Stripe Dashboard Monitoring
1. **Payments** → Monitor transaction volume
2. **Disputes** → Handle chargebacks
3. **Webhooks** → Check delivery status
4. **Logs** → Review API requests

### Application Monitoring
```bash
# Monitor webhook logs
tail -f storage/logs/laravel.log | grep stripe

# Check payment status
php artisan payments:status

# Retry failed webhooks
php artisan webhooks:retry
```

### Regular Maintenance
- [ ] **Monthly**: Review failed payments
- [ ] **Quarterly**: Update API versions
- [ ] **Annually**: Renew SSL certificates
- [ ] **As needed**: Handle disputes

---

## 🆘 Support Resources

### Documentation
- [Stripe API Documentation](https://stripe.com/docs/api)
- [Stripe Testing Guide](https://stripe.com/docs/testing)
- [Webhook Best Practices](https://stripe.com/docs/webhooks/best-practices)

### Support Channels
- **Stripe Support**: [support.stripe.com](https://support.stripe.com)
- **Community**: [Stack Overflow](https://stackoverflow.com/questions/tagged/stripe-payments)
- **Status Page**: [status.stripe.com](https://status.stripe.com)

### Emergency Contacts
```
Stripe Support: Available 24/7
Phone: Available in Dashboard
Email: Available in Dashboard
```

---

## ✅ Quick Reference

### Essential URLs
```
Dashboard: https://dashboard.stripe.com
API Docs: https://stripe.com/docs/api
Testing: https://stripe.com/docs/testing
Webhooks: https://dashboard.stripe.com/webhooks
```

### Key Files in Your Project
```
Payment Form: resources/views/checkout/payment/stripe.blade.php
Webhook Handler: app/Http/Controllers/WebhookController.php
Configuration: config/payment-gateways.php
Environment: .env
Test Script: tests/stripe-test.php
```

### Quick Commands
```bash
# Test integration
php tests/stripe-test.php

# Clear cache
php artisan config:clear

# Check logs
tail -f storage/logs/laravel.log

# Run tests
php artisan test --filter=Stripe
```

---

**🎉 Congratulations! Your Stripe integration is production-ready!**
