# reCAPTCHA Implementation Guide

This document explains how to use the reCAPTCHA implementation in your Laravel application.

## Prerequisites

1. Register your site at [Google reCAPTCHA Admin](https://www.google.com/recaptcha/admin)
2. Choose "reCAPTCHA v2" > "Invisible reCAPTCHA badge"
3. Add your domain to the domain list
4. Get your Site Key and Secret Key

## Installation

1. The package is already installed via Composer:
   ```bash
   composer require anhskohbo/no-captcha
   ```

2. Publish the configuration file:
   ```bash
   php artisan vendor:publish --provider="Anhskohbo\NoCaptcha\NoCaptchaServiceProvider"
   ```

3. Add your reCAPTCHA keys to your `.env` file:
   ```
   NOCAPTCHA_SECRET=your_secret_key
   NOCAPTCHA_SITEKEY=your_site_key
   ```

## Usage

### 1. Basic Usage in Forms

Add the reCAPTCHA widget to your form using the Blade component:

```blade
<form id="contact-form" method="POST" action="/contact" class="needs-validation" novalidate>
    @csrf
    
    <!-- Your form fields here -->
    
    <!-- Add reCAPTCHA -->
    <x-recaptcha formId="contact-form" />
    
    <button type="submit" class="btn btn-primary">Submit</button>
</form>
```

### 2. Using the Validation Rule

In your form request or controller, add the reCAPTCHA validation rule:

```php
use App\Rules\Recaptcha;

public function rules()
{
    return [
        // Other validation rules
        'g-recaptcha-response' => ['required', new Recaptcha],
    ];
}
```

### 3. Customizing the Error Message

You can customize the error message by passing it to the Recaptcha rule:

```php
'g-recaptcha-response' => ['required', new Recaptcha('Please verify that you are not a robot.')],
```

### 4. Manual Verification

If you need to verify the reCAPTCHA response manually:

```php
use App\Helpers\RecaptchaHelper;

if (!RecaptchaHelper::verify($request->input('g-recaptcha-response'))) {
    // reCAPTCHA verification failed
    return back()->withErrors(['captcha' => 'reCAPTCHA verification failed.']);
}
```

## Configuration

You can customize the reCAPTCHA behavior by modifying the `config/captcha.php` file. Here are the available options:

```php
return [
    'secret' => env('NOCAPTCHA_SECRET'),
    'sitekey' => env('NOCAPTCHA_SITEKEY'),
    'version' => 'invisible', // v2, invisible, v3
    'options' => [
        'timeout' => 30,
        'verify_https' => true,
        'curl_timeout' => 10,
    ],
    'attributes' => [
        'data-theme' => 'light',
        'data-size' => 'invisible',
        'data-badge' => 'bottomright', // bottomright, bottomleft, inline
        'data-callback' => 'onRecaptchaSuccess',
        'data-expired-callback' => 'onRecaptchaExpired',
    ],
    'threshold' => 0.5, // For v3 only
];
```

## Testing

In your tests, you can disable reCAPTCHA validation by adding this to your test:

```php
// In your test setup
Config::set('captcha.secret', null);
```

Or mock the reCAPTCHA verification:

```php
use Anhskohbo\NoCaptcha\NoCaptcha;

// Mock a successful verification
$this->mock(NoCaptcha::class, function ($mock) {
    $mock->shouldReceive('verifyResponse')
        ->once()
        ->andReturn((object)['success' => true]);
});
```

## Troubleshooting

### reCAPTCHA not working in local development

Make sure to:
1. Add `localhost` to your reCAPTCHA domain list in the Google reCAPTCHA admin console
2. Or disable reCAPTCHA in local environment by setting `NOCAPTCHA_SECRET` to null in your `.env` file

### reCAPTCHA badge is visible

The badge is hidden by default using CSS. If you want to show it, override the CSS in your stylesheet:

```css
.grecaptcha-badge { 
    visibility: visible !important; 
    opacity: 1 !important; 
}
```

## Security Considerations

1. Always verify the reCAPTCHA response on the server side
2. Keep your Secret Key secure and never expose it in client-side code
3. Monitor your reCAPTCHA analytics in the Google reCAPTCHA admin console
4. Consider implementing rate limiting to prevent abuse
