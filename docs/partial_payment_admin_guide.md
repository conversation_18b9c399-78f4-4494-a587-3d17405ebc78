# Partial Payment Administration Guide

## Overview
The partial payment feature allows customers to pay a portion of their order total, with the remaining balance due within a specified grace period. This feature is **disabled by default** and must be explicitly enabled by administrators.

## Accessing Settings
1. Log in to the admin panel
2. Navigate to **Settings** in the admin menu
3. Click on the **Payments** tab
4. Locate the **Partial Payments** section

## Configuration Options

### 1. Enable Partial Payments
- **Default**: Disabled (OFF)
- **Description**: Master switch to allow or disallow partial payments
- **When Disabled**: All partial payment attempts are rejected and marked as failed
- **When Enabled**: Partial payments are accepted based on minimum percentage requirements

### 2. Minimum Payment Percentage
- **Default**: 50%
- **Range**: 1% - 99%
- **Description**: Minimum percentage of total order amount required for partial payments
- **Example**: For a $100 order with 50% minimum, customer must pay at least $50

### 3. Grace Period (Days)
- **Default**: 7 days
- **Range**: 1 - 365 days
- **Description**: Number of days customers have to complete remaining payment
- **Example**: With 7 days grace period, remaining payment is due within one week

## Security Features

### Automatic Validation
- **Currency Verification**: Ensures payment currency matches order currency
- **Amount Verification**: Validates captured amount against expected amount
- **Percentage Validation**: Rejects payments below minimum percentage
- **Overpayment Detection**: Flags overpayments for manual review

### Order Status Management
- **Partial Payment Received**: Order status → "Awaiting Payment"
- **Payment Rejected**: Order status → "Payment Failed"
- **Full Payment**: Order status → "Processing"

## Example Scenarios

### Scenario 1: Partial Payments Disabled (Default)
- Customer attempts to pay $50 of $100 order
- **Result**: Payment rejected, order remains pending
- **Message**: "Partial payments are not enabled. Full payment required."

### Scenario 2: Partial Payments Enabled (50% minimum)
- Customer pays $60 of $100 order (60%)
- **Result**: Payment accepted, order status → "Awaiting Payment"
- **Due Date**: Remaining $40 due within grace period

### Scenario 3: Below Minimum Percentage
- Customer pays $30 of $100 order (30%) with 50% minimum
- **Result**: Payment rejected
- **Message**: "Partial payment below minimum requirement. At least 50% ($50.00) required."

## Best Practices

### When to Enable Partial Payments
✅ **Consider enabling if:**
- You sell high-value items
- Your customers frequently request payment plans
- You have processes to handle partial fulfillment
- You can manage follow-up for remaining payments

❌ **Avoid enabling if:**
- You have low-margin products
- Your fulfillment process can't handle partial payments
- You lack resources for payment follow-up
- You prefer simple, complete transactions

### Recommended Settings
- **Conservative**: 70% minimum, 7 days grace period
- **Moderate**: 50% minimum, 14 days grace period
- **Flexible**: 30% minimum, 30 days grace period

### Monitoring and Management
1. **Regular Review**: Check orders with "Awaiting Payment" status
2. **Follow-up Process**: Establish procedures for contacting customers with outstanding balances
3. **Grace Period Monitoring**: Track orders approaching due dates
4. **Overpayment Review**: Handle flagged overpayments promptly

## Technical Notes

### Database Changes
- New payment status: `partial`
- Enhanced payment metadata tracking
- Automatic order notes with payment details

### Logging
- All partial payment attempts are logged
- Security events for rejected payments
- Overpayment detection alerts

### Integration
- Works with PayPal payment gateway
- Webhook validation included
- Real-time amount verification

## Troubleshooting

### Common Issues
1. **Settings not saving**: Check form validation errors
2. **Payments still rejected**: Verify settings are enabled and saved
3. **Percentage calculations**: Ensure minimum percentage is reasonable

### Support
For technical issues or questions about partial payment configuration, refer to the development team or system administrator.
