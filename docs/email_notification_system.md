# Email Notification System Documentation

## Overview

A comprehensive email notification system has been implemented for the Laravel application to automatically send professional email notifications to customers throughout the order lifecycle. The system includes email preferences management, unsubscribe functionality, and robust error handling.

## Features Implemented

### 1. Order Lifecycle Email Notifications

The system automatically sends emails for the following events:

- **Order Placed Successfully** - Confirmation email immediately after order creation
- **Payment Confirmed** - Notification when payment is successfully processed
- **Payment Failed** - Alert when payment processing fails
- **Order Processing** - Update when order status changes to processing
- **Order Shipped** - Shipping notification with tracking information
- **Order Delivered** - Confirmation when order is marked as delivered
- **Order Cancelled** - Notification if order is cancelled

### 2. Email Templates

Professional, responsive email templates with:
- Consistent branding and modern design
- Mobile-responsive layout
- Order details and relevant information
- Unsubscribe and preference management links
- Visual elements like gradients and cards

### 3. Email Preferences System

- **Granular Control**: Users can enable/disable specific email types
- **Unsubscribe Functionality**: Complete unsubscribe from all emails
- **Preference Management**: Web interface to customize email settings
- **Resubscribe Option**: Easy way to resubscribe to notifications

### 4. Error Handling and Logging

- **Graceful Failures**: Email failures don't break order processing
- **Comprehensive Logging**: All email events are logged
- **Admin Notifications**: Admins are notified of email failures
- **Retry Mechanism**: Failed emails can be retried automatically

## Technical Implementation

### Events System

Created Laravel events for order lifecycle changes:
- `OrderPlaced`
- `PaymentConfirmed`
- `PaymentFailed`
- `OrderStatusChanged`
- `OrderShipped`
- `OrderDelivered`
- `OrderCancelled`

### Mailable Classes

Professional email classes for each notification type:
- `PaymentConfirmedEmail`
- `PaymentFailedEmail`
- `OrderProcessingEmail`
- `OrderShippedEmail`
- `OrderDeliveredEmail`
- `OrderCancelledEmail`

### Database Schema

**Email Preferences Table:**
```sql
- id (UUID)
- email (string, indexed)
- order_confirmation (boolean, default: true)
- payment_confirmed (boolean, default: true)
- payment_failed (boolean, default: true)
- order_processing (boolean, default: true)
- order_shipped (boolean, default: true)
- order_delivered (boolean, default: true)
- order_cancelled (boolean, default: true)
- marketing_emails (boolean, default: false)
- unsubscribe_token (string, unique)
- unsubscribed_at (timestamp, nullable)
```

### Services

**EmailNotificationService:**
- Handles email sending with error handling
- Checks user preferences before sending
- Provides admin failure notifications
- Includes retry mechanisms

### Routes

Email preference management routes:
- `GET /email/preferences/{token}` - View preferences
- `PUT /email/preferences/{token}` - Update preferences
- `GET|POST /email/unsubscribe` - Unsubscribe interface
- `POST /email/resubscribe/{token}` - Resubscribe to emails

## Usage

### Testing the System

Use the test command to verify email notifications:

```bash
# Test all email types
php artisan email:test --email=<EMAIL>

# Test specific event
php artisan email:test --email=<EMAIL> --event=payment_confirmed
```

### Managing Failed Emails

Retry failed email jobs:

```bash
php artisan email:retry-failed --max-retries=3
```

### Processing Email Queue

Process queued emails:

```bash
php artisan queue:work
```

## Configuration

### Mail Settings

Configure mail settings in `.env`:
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Your Store Name"
```

### Admin Email

Set admin email for failure notifications:
```env
MAIL_ADMIN_EMAIL=<EMAIL>
```

## Email Templates

All email templates are located in `resources/views/emails/`:
- `layouts/base.blade.php` - Base template with consistent styling
- `orders/payment-confirmed.blade.php`
- `orders/payment-failed.blade.php`
- `orders/processing.blade.php`
- `orders/shipped.blade.php`
- `orders/delivered.blade.php`
- `orders/cancelled.blade.php`

## Email Preferences Management

Users can manage their email preferences through:
1. Links in email footers
2. Direct access via preference tokens
3. Unsubscribe functionality with granular control

## Monitoring and Maintenance

### Logs

Email events are logged with details:
- Email type and recipient
- Order information
- Success/failure status
- Error details for failures

### Queue Monitoring

Monitor email queue status:
```bash
php artisan queue:monitor
```

### Failed Job Handling

View failed jobs:
```bash
php artisan queue:failed
```

## Security Considerations

- Email preferences use secure tokens
- Input validation on all forms
- Rate limiting on email preference changes
- Secure unsubscribe tokens that expire appropriately

## Future Enhancements

Potential improvements:
- Email analytics and open tracking
- A/B testing for email templates
- Advanced segmentation options
- Integration with email service providers (SendGrid, Mailgun)
- Email template editor in admin panel

## Troubleshooting

### Common Issues

1. **Emails not sending**: Check queue worker is running
2. **Template errors**: Verify all required variables are available
3. **Preference links broken**: Ensure routes are properly registered
4. **High failure rate**: Check SMTP configuration and limits

### Debug Commands

```bash
# Test email configuration
php artisan tinker
Mail::raw('Test email', function($msg) { $msg->to('<EMAIL>')->subject('Test'); });

# Check queue status
php artisan queue:work --once

# View application logs
tail -f storage/logs/laravel.log
```

This email notification system provides a robust, user-friendly way to keep customers informed throughout their order journey while respecting their communication preferences.
