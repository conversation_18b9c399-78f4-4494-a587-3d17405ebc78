<?php

use App\Http\Controllers\CookieConsentController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\InquiryController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\YouTubeController;
use Illuminate\Support\Facades\Route;

Route::get('/', [HomeController::class, 'index'])->name('home');

// Static Pages
Route::get('/about', [PageController::class, 'about'])->name('about');
Route::get('/contact', [PageController::class, 'contact'])->name('contact');
Route::post('/contact', [PageController::class, 'submitContact'])->name('contact.submit');
Route::get('/privacy-policy', [PageController::class, 'privacy'])->name('privacy');
Route::get('/terms-and-conditions', [PageController::class, 'terms'])->name('terms');
Route::get('/cookie-policy', function () {
    return view('pages.cookie-policy');
})->name('cookie-policy');
Route::get('/youtube', [YouTubeController::class, 'index'])->name('youtube.index');

Route::get('/cookie-settings', [CookieConsentController::class, 'index'])->name('cookie-settings');
Route::post('/cookie-settings', [CookieConsentController::class, 'update'])->name('cookie-settings.update');
Route::post('/cookie-consent/accept-all', [CookieConsentController::class, 'acceptAll'])->name('cookie-consent.accept-all');
Route::post('/cookie-consent/decline-all', [CookieConsentController::class, 'declineAll'])->name('cookie-consent.decline-all');

// Services Routes
Route::get('/services', [ServiceController::class, 'index'])->name('services.index');
Route::get('/services/{slug}', [ServiceController::class, 'show'])->name('services.show');

// Inquiry Routes
Route::get('/inquiry', [InquiryController::class, 'create'])->name('inquiries.create');
Route::post('/inquiry', [InquiryController::class, 'store'])->name('inquiries.store');
Route::get('/inquiry/thank-you', [InquiryController::class, 'thankyou'])->name('inquiries.thankyou');

// AJAX Routes
Route::post('/ajax/notify', [\App\Http\Controllers\AjaxController::class, 'notify'])->name('ajax.notify');
Route::post('/ajax/shipping-methods', [\App\Http\Controllers\CheckoutController::class, 'getShippingMethods'])->name('ajax.shipping-methods');

// Store Routes
Route::prefix('store')->group(function () {
    // Product Routes
    Route::get('/', [\App\Http\Controllers\ProductController::class, 'index'])->name('store.index');
    Route::get('/products', [\App\Http\Controllers\ProductController::class, 'index'])->name('products.index');
    Route::get('/products/{slug}', [\App\Http\Controllers\ProductController::class, 'show'])->name('products.show');

    // Category Routes
    Route::get('/categories', [\App\Http\Controllers\CategoryController::class, 'index'])->name('categories.index');
    Route::get('/categories/{slug}', [\App\Http\Controllers\CategoryController::class, 'show'])->name('categories.show');

    // Cart Routes
    Route::get('/cart', [\App\Http\Controllers\CartController::class, 'index'])->name('cart.index');
    Route::post('/cart/add', [\App\Http\Controllers\CartController::class, 'add'])->name('cart.add');
    Route::patch('/cart/items/{cartItem}', [\App\Http\Controllers\CartController::class, 'update'])->name('cart.update');
    Route::delete('/cart/items/{cartItem}', [\App\Http\Controllers\CartController::class, 'remove'])->name('cart.remove');
    Route::delete('/cart', [\App\Http\Controllers\CartController::class, 'clear'])->name('cart.clear');

    // Enhanced Cart API Routes
    Route::get('/cart/summary', [\App\Http\Controllers\CartController::class, 'summary'])->name('cart.summary');
    Route::get('/cart/validate', [\App\Http\Controllers\CartController::class, 'validate'])->name('cart.validate');
    Route::get('/cart/recommendations', [\App\Http\Controllers\CartController::class, 'recommendations'])->name('cart.recommendations');

    // Checkout Routes
    Route::get('/checkout', [\App\Http\Controllers\CheckoutController::class, 'index'])->name('checkout.index');
    Route::post('/checkout', [\App\Http\Controllers\CheckoutController::class, 'process'])->name('checkout.process');
    Route::get('/checkout/confirmation', [\App\Http\Controllers\CheckoutController::class, 'confirmation'])->name('checkout.confirmation');
    Route::get('/checkout/{order}/confirm-stripe-payment', [\App\Http\Controllers\CheckoutController::class, 'confirmStripePayment'])->name('checkout.confirmStripePayment');

    // Payment page route
    Route::get('/checkout/{order}/payment/{method}', [\App\Http\Controllers\CheckoutController::class, 'showPayment'])->name('checkout.payment');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    // Profile Routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Order Routes
    Route::get('/orders', [\App\Http\Controllers\OrderController::class, 'index'])->name('orders.index');
    Route::get('/orders/{order}', [\App\Http\Controllers\OrderController::class, 'show'])->name('orders.show');

    // User Inquiries Routes
    Route::get('/my-inquiries', [InquiryController::class, 'userInquiries'])->name('user.inquiries');

    // Wishlist Routes
    Route::get('/wishlist', [\App\Http\Controllers\WishlistController::class, 'index'])->name('wishlist.index');
    Route::post('/wishlist', [\App\Http\Controllers\WishlistController::class, 'store'])->name('wishlist.store');
    Route::post('/wishlist/toggle', [\App\Http\Controllers\WishlistController::class, 'toggle'])->name('wishlist.toggle');
    Route::delete('/wishlist/{productId}', [\App\Http\Controllers\WishlistController::class, 'destroy'])->name('wishlist.destroy');
    Route::delete('/wishlist', [\App\Http\Controllers\WishlistController::class, 'clear'])->name('wishlist.clear');
    Route::get('/wishlist/status/{productId}', [\App\Http\Controllers\WishlistController::class, 'status'])->name('wishlist.status');
});

// Review Routes
Route::get('/products/{product}/reviews', [\App\Http\Controllers\ReviewController::class, 'index'])->name('reviews.index');

Route::middleware(['auth'])->group(function () {
    Route::post('/products/{product}/reviews', [\App\Http\Controllers\ReviewController::class, 'store'])->name('reviews.store');
    Route::put('/reviews/{review}', [\App\Http\Controllers\ReviewController::class, 'update'])->name('reviews.update');
    Route::delete('/reviews/{review}', [\App\Http\Controllers\ReviewController::class, 'destroy'])->name('reviews.destroy');
    Route::post('/reviews/{review}/helpful', [\App\Http\Controllers\ReviewController::class, 'markHelpful'])->name('reviews.helpful');
    Route::get('/products/{product}/reviews/form-data', [\App\Http\Controllers\ReviewController::class, 'getFormData'])->name('reviews.form-data');
});

// Admin Routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard
    Route::get('/', function () {
        return view('admin.dashboard');
    })->name('dashboard');

    // Content Blocks
    Route::resource('content-blocks', \App\Http\Controllers\Admin\ContentBlockController::class);

    // Tax Classes
    Route::resource('taxes', \App\Http\Controllers\Admin\TaxController::class);

    // User Management
    Route::resource('users', \App\Http\Controllers\Admin\UserController::class);
    Route::put('users/{user}/activate', [\App\Http\Controllers\Admin\UserController::class, 'activate'])->name('users.activate');
    Route::put('users/{user}/deactivate', [\App\Http\Controllers\Admin\UserController::class, 'deactivate'])->name('users.deactivate');

    // Product Management
    Route::resource('products', \App\Http\Controllers\Admin\ProductController::class);
    Route::post('products/{product}/variants', [\App\Http\Controllers\Admin\ProductController::class, 'storeVariant'])->name('products.variants.store');
    Route::put('products/{product}/variants/{variant}', [\App\Http\Controllers\Admin\ProductController::class, 'updateVariant'])->name('products.variants.update');
    Route::delete('products/{product}/variants/{variant}', [\App\Http\Controllers\Admin\ProductController::class, 'destroyVariant'])->name('products.variants.destroy');

    // Shipping Configuration
    Route::prefix('shipping')->name('shipping.')->group(function () {
        Route::get('configuration', [\App\Http\Controllers\Admin\ShippingConfigurationController::class, 'index'])->name('configuration');
        Route::put('configuration', [\App\Http\Controllers\Admin\ShippingConfigurationController::class, 'update'])->name('configuration.update');
        Route::get('configuration/reset', [\App\Http\Controllers\Admin\ShippingConfigurationController::class, 'reset'])->name('configuration.reset');
        Route::post('configuration/test', [\App\Http\Controllers\Admin\ShippingConfigurationController::class, 'test'])->name('configuration.test');
    });

    // Settings
    Route::get('settings', [\App\Http\Controllers\Admin\SettingController::class, 'index'])->name('settings.index');
    Route::put('settings', [\App\Http\Controllers\Admin\SettingController::class, 'update'])->name('settings.update');
    
    // Review Management
    Route::resource('reviews', \App\Http\Controllers\Admin\ReviewController::class)->except(['create', 'store', 'update']);
    Route::put('reviews/{review}/approve', [\App\Http\Controllers\Admin\ReviewController::class, 'approve'])->name('reviews.approve');
    Route::put('reviews/{review}/reject', [\App\Http\Controllers\Admin\ReviewController::class, 'reject'])->name('reviews.reject');
    Route::put('reviews/{review}/toggle-featured', [\App\Http\Controllers\Admin\ReviewController::class, 'toggleFeatured'])->name('reviews.toggle-featured');
    // Review Management
    Route::resource('reviews', \App\Http\Controllers\Admin\ReviewController::class)->except(['create', 'store', 'update']);
    Route::put('reviews/{review}/approve', [\App\Http\Controllers\Admin\ReviewController::class, 'approve'])->name('reviews.approve');
    Route::put('reviews/{review}/reject', [\App\Http\Controllers\Admin\ReviewController::class, 'reject'])->name('reviews.reject');
    Route::put('reviews/{review}/toggle-featured', [\App\Http\Controllers\Admin\ReviewController::class, 'toggleFeatured'])->name('reviews.toggle-featured');
});

// Email Preferences Routes
Route::get('/email/preferences/{token}', [\App\Http\Controllers\EmailPreferenceController::class, 'show'])->name('email.preferences');
Route::put('/email/preferences/{token}', [\App\Http\Controllers\EmailPreferenceController::class, 'update'])->name('email.preferences.update');
Route::match(['get', 'post'], '/email/unsubscribe', [\App\Http\Controllers\EmailPreferenceController::class, 'unsubscribe'])->name('email.unsubscribe');
Route::post('/email/resubscribe/{token}', [\App\Http\Controllers\EmailPreferenceController::class, 'resubscribe'])->name('email.preferences.resubscribe');

require __DIR__.'/auth.php';
require __DIR__.'/payment.php';
