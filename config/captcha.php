<?php

return [
    /*
    |--------------------------------------------------------------------------
    | reCAPTCHA Keys
    |--------------------------------------------------------------------------
    |
    | The reCAPTCHA server keys. You can get them from the reCAPTCHA admin console:
    | https://www.google.com/recaptcha/admin
    |
    */
    'secret' => env('NOCAPTCHA_SECRET'),
    'sitekey' => env('NOCAPTCHA_SITEKEY'),

    /*
    |--------------------------------------------------------------------------
    | reCAPTCHA Version
    |--------------------------------------------------------------------------
    |
    | The version of reCAPTCHA to use. Options: 'v2', 'invisible', 'v3'
    |
    */
    'version' => 'invisible',

    /*
    |--------------------------------------------------------------------------
    | reCAPTCHA Options
    |--------------------------------------------------------------------------
    |
    | Additional reCAPTCHA options. For more details, see:
    | https://developers.google.com/recaptcha/docs/display#config
    |
    */
    'options' => [
        'timeout' => 30,
        'verify_https' => true,
        'curl_timeout' => 10,
    ],

    /*
    |--------------------------------------------------------------------------
    | reCAPTCHA Attributes
    |--------------------------------------------------------------------------
    |
    | The attributes for the reCAPTCHA button/widget.
    |
    */
    'attributes' => [
        'data-theme' => 'light',
        'data-size' => 'invisible',
        'data-badge' => 'bottomright', // bottomright, bottomleft, inline
        'data-callback' => 'onCaptchaSuccess',
        'data-expired-callback' => 'onCaptchaExpired',
    ],

    /*
    |--------------------------------------------------------------------------
    | reCAPTCHA Threshold
    |--------------------------------------------------------------------------
    |
    | The score threshold for reCAPTCHA v3 (0.0 to 1.0).
    | A score of 1.0 is very likely a good interaction, 0.0 is very likely a bot.
    |
    */
    'threshold' => 0.5,
];
