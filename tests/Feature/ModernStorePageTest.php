<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\User;
use App\Models\Wishlist;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ModernStorePageTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->category = Category::factory()->create([
            'name' => ['en' => 'Electronics'],
            'slug' => ['en' => 'electronics'],
        ]);

        $this->product = Product::factory()->create([
            'name' => ['en' => 'Test Product'],
            'description' => ['en' => 'Test product description'],
            'slug' => ['en' => 'test-product'],
            'category_id' => $this->category->id,
            'is_active' => true,
            'is_featured' => true,
        ]);

        $this->variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'price' => 99.99,
            'compare_at_price' => 129.99,
            'is_active' => true,
        ]);

        $this->user = User::factory()->create();
    }

    public function test_store_page_loads_successfully()
    {
        $response = $this->get(route('products.index'));

        $response->assertStatus(200);
        $response->assertViewIs('store.products.index');
        $response->assertViewHas(['products', 'categories', 'totalProducts']);
    }

    public function test_store_page_displays_products()
    {
        $response = $this->get(route('products.index'));

        $response->assertStatus(200);
        $response->assertSee($this->product->getTranslation('name', 'en'));
        $response->assertSee('$99.99');
        $response->assertSee('$129.99'); // Compare at price
    }

    public function test_search_functionality_works()
    {
        $response = $this->get(route('products.index', ['search' => 'Test Product']));

        $response->assertStatus(200);
        $response->assertSee($this->product->getTranslation('name', 'en'));
    }

    public function test_category_filtering_works()
    {
        $response = $this->get(route('products.index', ['category' => $this->category->id]));

        $response->assertStatus(200);
        $response->assertSee($this->product->getTranslation('name', 'en'));
    }

    public function test_price_filtering_works()
    {
        $response = $this->get(route('products.index', [
            'min_price' => 50,
            'max_price' => 150
        ]));

        $response->assertStatus(200);
        $response->assertSee($this->product->getTranslation('name', 'en'));
    }

    public function test_sorting_functionality_works()
    {
        // Test price low to high
        $response = $this->get(route('products.index', ['sort' => 'price_low']));
        $response->assertStatus(200);

        // Test price high to low
        $response = $this->get(route('products.index', ['sort' => 'price_high']));
        $response->assertStatus(200);

        // Test name sorting
        $response = $this->get(route('products.index', ['sort' => 'name']));
        $response->assertStatus(200);
    }

    public function test_pagination_works()
    {
        // Create more products to test pagination
        Product::factory()->count(15)->create([
            'category_id' => $this->category->id,
            'is_active' => true,
        ])->each(function ($product) {
            ProductVariant::factory()->create([
                'product_id' => $product->id,
                'is_active' => true,
            ]);
        });

        $response = $this->get(route('products.index', ['per_page' => 12]));

        $response->assertStatus(200);
        $response->assertViewHas('products');
        
        $products = $response->viewData('products');
        $this->assertEquals(12, $products->perPage());
    }

    public function test_wishlist_integration_for_authenticated_users()
    {
        // Add product to wishlist
        Wishlist::create([
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
        ]);

        $response = $this->actingAs($this->user)->get(route('products.index'));

        $response->assertStatus(200);
        $response->assertViewHas('wishlistItems');
        
        $wishlistItems = $response->viewData('wishlistItems');
        $this->assertContains($this->product->id, $wishlistItems);
    }

    public function test_empty_state_displays_when_no_products()
    {
        // Delete all products
        Product::query()->delete();

        $response = $this->get(route('products.index'));

        $response->assertStatus(200);
        $response->assertSee('No products found');
        $response->assertSee('View All Products');
    }

    public function test_sale_badge_displays_for_discounted_products()
    {
        $response = $this->get(route('products.index'));

        $response->assertStatus(200);
        // Should show discount percentage
        $discountPercent = round((($this->variant->compare_at_price - $this->variant->price) / $this->variant->compare_at_price) * 100);
        $response->assertSee("-{$discountPercent}%");
    }

    public function test_breadcrumb_navigation_is_present()
    {
        $response = $this->get(route('products.index'));

        $response->assertStatus(200);
        $response->assertSee('Home');
        $response->assertSee('Products');
    }

    public function test_responsive_design_elements_are_present()
    {
        $response = $this->get(route('products.index'));

        $response->assertStatus(200);
        // Check for responsive grid classes
        $response->assertSee('grid-cols-1 sm:grid-cols-2 lg:grid-cols-3');
        $response->assertSee('lg:w-1/4'); // Sidebar
        $response->assertSee('lg:w-3/4'); // Main content
    }

    public function test_product_cards_have_proper_structure()
    {
        $response = $this->get(route('products.index'));

        $response->assertStatus(200);
        $response->assertSee('View Details');
        $response->assertSee($this->category->getTranslation('name', 'en'));
        $response->assertSee('hover:shadow-lg'); // Hover effects
    }
}
