<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Review;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ReviewSystemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->category = Category::factory()->create([
            'name' => ['en' => 'Electronics'],
            'slug' => ['en' => 'electronics'],
        ]);

        $this->product = Product::factory()->create([
            'name' => ['en' => 'Test Product'],
            'description' => ['en' => 'Test product description'],
            'slug' => ['en' => 'test-product'],
            'category_id' => $this->category->id,
            'is_active' => true,
        ]);

        $this->variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'price' => 99.99,
            'is_active' => true,
        ]);

        $this->user = User::factory()->create();
        $this->admin = User::factory()->create(['is_admin' => true]);
    }

    public function test_user_can_create_review()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('reviews.store', $this->product), [
                'rating' => 5,
                'title' => 'Excellent product!',
                'content' => 'This product exceeded my expectations. Highly recommended!',
            ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Thank you for your review! It will be published after moderation.',
                ]);

        $this->assertDatabaseHas('reviews', [
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'rating' => 5,
            'title' => 'Excellent product!',
            'is_approved' => false, // Should require approval
        ]);
    }

    public function test_user_cannot_review_same_product_twice()
    {
        // Create first review
        Review::factory()->create([
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'rating' => 5,
            'title' => 'First review',
            'content' => 'Great product',
        ]);

        // Try to create second review
        $response = $this->actingAs($this->user)
            ->postJson(route('reviews.store', $this->product), [
                'rating' => 4,
                'title' => 'Second review',
                'content' => 'Another review attempt',
            ]);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'You have already reviewed this product.',
                ]);
    }

    public function test_review_validation_rules()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('reviews.store', $this->product), [
                'rating' => 6, // Invalid rating
                'title' => 'Hi', // Too short
                'content' => 'Short', // Too short
            ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['rating', 'title', 'content']);
    }

    public function test_verified_purchase_detection()
    {
        // Create an order for the user
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'completed',
        ]);

        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_variant_id' => $this->variant->id,
            'quantity' => 1,
        ]);

        $response = $this->actingAs($this->user)
            ->postJson(route('reviews.store', $this->product), [
                'rating' => 5,
                'title' => 'Great purchase!',
                'content' => 'I bought this and love it!',
            ]);

        $response->assertStatus(200);

        $this->assertDatabaseHas('reviews', [
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'verified_purchase' => true,
        ]);
    }

    public function test_user_can_edit_review_within_24_hours()
    {
        $review = Review::factory()->create([
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'rating' => 4,
            'title' => 'Good product',
            'content' => 'Original content',
            'created_at' => now()->subHours(2), // 2 hours ago
        ]);

        $response = $this->actingAs($this->user)
            ->putJson(route('reviews.update', $review), [
                'rating' => 5,
                'title' => 'Excellent product!',
                'content' => 'Updated content after using it more',
            ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Review updated successfully! It will be re-reviewed for approval.',
                ]);

        $this->assertDatabaseHas('reviews', [
            'id' => $review->id,
            'rating' => 5,
            'title' => 'Excellent product!',
            'content' => 'Updated content after using it more',
            'is_approved' => false, // Should require re-approval
        ]);
    }

    public function test_user_cannot_edit_review_after_24_hours()
    {
        $review = Review::factory()->create([
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'created_at' => now()->subHours(25), // 25 hours ago
        ]);

        $response = $this->actingAs($this->user)
            ->putJson(route('reviews.update', $review), [
                'rating' => 5,
                'title' => 'Updated title',
                'content' => 'Updated content',
            ]);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'You can only edit your own reviews within 24 hours of posting.',
                ]);
    }

    public function test_user_can_delete_own_review()
    {
        $review = Review::factory()->create([
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
        ]);

        $response = $this->actingAs($this->user)
            ->deleteJson(route('reviews.destroy', $review));

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Review deleted successfully.',
                ]);

        $this->assertSoftDeleted('reviews', ['id' => $review->id]);
    }

    public function test_user_cannot_delete_others_review()
    {
        $otherUser = User::factory()->create();
        $review = Review::factory()->create([
            'product_id' => $this->product->id,
            'user_id' => $otherUser->id,
        ]);

        $response = $this->actingAs($this->user)
            ->deleteJson(route('reviews.destroy', $review));

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'You can only delete your own reviews.',
                ]);
    }

    public function test_reviews_index_with_filtering_and_sorting()
    {
        // Create multiple reviews
        $reviews = collect();
        for ($i = 1; $i <= 5; $i++) {
            $user = User::factory()->create();
            $reviews->push(Review::factory()->create([
                'product_id' => $this->product->id,
                'user_id' => $user->id,
                'rating' => $i,
                'is_approved' => true,
                'created_at' => now()->subDays($i),
            ]));
        }

        // Test basic index
        $response = $this->getJson(route('reviews.index', $this->product));
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'reviews',
                    'pagination',
                    'stats' => ['average_rating', 'total_reviews', 'rating_distribution'],
                ]);

        // Test filtering by rating
        $response = $this->getJson(route('reviews.index', $this->product) . '?rating=5');
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertEquals(1, count($data['reviews']));
        $this->assertEquals(5, $data['reviews'][0]['rating']);

        // Test sorting by rating
        $response = $this->getJson(route('reviews.index', $this->product) . '?sort=rating_high');
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertEquals(5, $data['reviews'][0]['rating']);
    }

    public function test_helpful_count_functionality()
    {
        $review = Review::factory()->create([
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'helpful_count' => 0,
        ]);

        $response = $this->actingAs($this->user)
            ->postJson(route('reviews.helpful', $review));

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Thank you for your feedback!',
                    'helpful_count' => 1,
                ]);

        $this->assertDatabaseHas('reviews', [
            'id' => $review->id,
            'helpful_count' => 1,
        ]);
    }

    public function test_product_rating_aggregation()
    {
        // Create reviews with different ratings
        $ratings = [5, 4, 4, 3, 5];
        foreach ($ratings as $rating) {
            $user = User::factory()->create();
            Review::factory()->create([
                'product_id' => $this->product->id,
                'user_id' => $user->id,
                'rating' => $rating,
                'is_approved' => true,
            ]);
        }

        // Refresh the product to get updated attributes
        $this->product->refresh();

        // Test average rating calculation
        $expectedAverage = array_sum($ratings) / count($ratings);
        $this->assertEquals(round($expectedAverage, 1), $this->product->average_rating);

        // Test reviews count
        $this->assertEquals(count($ratings), $this->product->reviews_count);

        // Test rating distribution
        $distribution = $this->product->rating_distribution;
        $this->assertEquals(2, $distribution[5]['count']); // Two 5-star reviews
        $this->assertEquals(2, $distribution[4]['count']); // Two 4-star reviews
        $this->assertEquals(1, $distribution[3]['count']); // One 3-star review
    }

    public function test_guest_cannot_create_review()
    {
        $response = $this->postJson(route('reviews.store', $this->product), [
            'rating' => 5,
            'title' => 'Great product',
            'content' => 'Love it!',
        ]);

        $response->assertStatus(401);
    }

    public function test_review_form_data_endpoint()
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('reviews.form-data', $this->product));

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'can_review',
                    'has_purchased',
                    'existing_review',
                    'product' => ['id', 'name'],
                ]);
    }
}
