<?php

namespace Tests\Feature;

use App\Models\Setting;
use App\Models\Language;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FooterCompanyNameTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create default language
        Language::factory()->create([
            'code' => 'en',
            'name' => 'English',
            'is_default' => true,
            'is_active' => true
        ]);
    }

    public function test_footer_displays_company_name_from_setting()
    {
        $companyName = 'Custom Footer Company';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('home'));
        
        $response->assertStatus(200);
        $response->assertSee($companyName);
        // Check both the header and copyright sections
        $response->assertSee("<h2 class=\"text-2xl font-bold text-gray-900 dark:text-white\">{$companyName}</h2>", false);
        $response->assertSee("&copy; " . date('Y') . " {$companyName}. All rights reserved.", false);
    }

    public function test_footer_displays_site_description_from_setting()
    {
        $description = 'Custom company description for footer';
        Setting::setValue('site_description', $description);
        
        $response = $this->get(route('home'));
        
        $response->assertStatus(200);
        $response->assertSee($description);
    }

    public function test_footer_about_section_uses_company_name()
    {
        $companyName = 'About Section Test Corp';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('home'));
        
        $response->assertStatus(200);
        $response->assertSee("{$companyName} is a leading provider");
    }

    public function test_footer_contact_email_uses_setting()
    {
        $email = '<EMAIL>';
        Setting::setValue('contact_email', $email);
        
        $response = $this->get(route('home'));
        
        $response->assertStatus(200);
        $response->assertSee("mailto:{$email}", false);
        $response->assertSee($email);
    }

    public function test_footer_contact_phone_uses_setting()
    {
        $phone = '******-TEST-PHONE';
        Setting::setValue('contact_phone', $phone);
        
        $response = $this->get(route('home'));
        
        $response->assertStatus(200);
        $response->assertSee($phone);
    }

    public function test_footer_contact_address_uses_setting()
    {
        $address = '456 Custom Street, Test City, 12345';
        Setting::setValue('contact_address', $address);
        
        $response = $this->get(route('home'));
        
        $response->assertStatus(200);
        $response->assertSee($address);
    }

    public function test_footer_secondary_phone_displays_when_set()
    {
        $secondaryPhone = '******-SECONDARY';
        Setting::setValue('contact_phone_secondary', $secondaryPhone);
        
        $response = $this->get(route('home'));
        
        $response->assertStatus(200);
        $response->assertSee($secondaryPhone);
    }

    public function test_footer_secondary_phone_hidden_when_not_set()
    {
        // Don't set secondary phone
        $response = $this->get(route('home'));
        
        $response->assertStatus(200);
        // Should not show the secondary phone section
        $content = $response->getContent();
        $secondaryPhoneOccurrences = substr_count($content, 'contact_phone_secondary');
        $this->assertEquals(0, $secondaryPhoneOccurrences);
    }

    public function test_footer_falls_back_to_defaults_when_settings_not_set()
    {
        // Don't set any custom settings
        $response = $this->get(route('home'));
        
        $response->assertStatus(200);
        // Should show default values
        $response->assertSee('WisdomTechno');
        $response->assertSee('<EMAIL>');
        $response->assertSee('+852 4453 7120');
        $response->assertSee('123 Tech Street, 75000 Paris');
    }

    public function test_footer_handles_special_characters_in_company_name()
    {
        $companyName = 'Test & Associates, Inc.';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('home'));
        
        $response->assertStatus(200);
        // Should properly escape HTML entities
        $response->assertSee('Test &amp; Associates, Inc.', false);
    }

    public function test_footer_updates_when_settings_change()
    {
        $originalName = 'Original Footer Company';
        $newName = 'Updated Footer Company';
        
        Setting::setValue('company_name', $originalName);
        
        $response = $this->get(route('home'));
        $response->assertSee($originalName);
        
        // Update the company name
        Setting::setValue('company_name', $newName);
        
        $response = $this->get(route('home'));
        $response->assertSee($newName);
        $response->assertDontSee($originalName);
    }

    public function test_footer_copyright_year_is_current()
    {
        $companyName = 'Copyright Test Company';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('home'));
        
        $response->assertStatus(200);
        $currentYear = date('Y');
        $response->assertSee("&copy; {$currentYear} {$companyName}. All rights reserved.", false);
    }

    public function test_footer_does_not_contain_hardcoded_wisdomtechno_when_company_name_set()
    {
        $companyName = 'Non-WisdomTechno Company';
        Setting::setValue('company_name', $companyName);
        Setting::setValue('contact_email', '<EMAIL>');
        Setting::setValue('contact_phone', '******-CUSTOM');
        Setting::setValue('contact_address', 'Custom Address');
        Setting::setValue('site_description', 'Custom description');
        
        $response = $this->get(route('home'));
        
        $response->assertStatus(200);
        
        // Should not contain hardcoded WisdomTechno references when custom settings are provided
        $content = $response->getContent();
        
        // Remove the fallback defaults from the content for this test
        $contentWithoutDefaults = preg_replace('/WisdomTechno\'\)/', '', $content);
        $contentWithoutDefaults = preg_replace('/wisdomtechno\.com\'\)/', '', $contentWithoutDefaults);
        
        $this->assertStringNotContainsString('WisdomTechno', $contentWithoutDefaults,
            'Footer should not contain hardcoded WisdomTechno references when company name is set');
    }

    public function test_footer_all_contact_fields_work_together()
    {
        $companyName = 'Complete Contact Test Corp';
        $email = '<EMAIL>';
        $phone = '******-COMPLETE';
        $secondaryPhone = '******-BACKUP';
        $address = '789 Complete Street, Full City, 67890';
        
        Setting::setValue('company_name', $companyName);
        Setting::setValue('contact_email', $email);
        Setting::setValue('contact_phone', $phone);
        Setting::setValue('contact_phone_secondary', $secondaryPhone);
        Setting::setValue('contact_address', $address);
        
        $response = $this->get(route('home'));
        
        $response->assertStatus(200);
        $response->assertSee($companyName);
        $response->assertSee($email);
        $response->assertSee($phone);
        $response->assertSee($secondaryPhone);
        $response->assertSee($address);
    }
}
