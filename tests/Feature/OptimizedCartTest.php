<?php

namespace Tests\Feature;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Category;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\User;
use App\Services\OptimizedCartService;
use App\Services\CartValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class OptimizedCartTest extends TestCase
{
    use RefreshDatabase;

    protected OptimizedCartService $cartService;
    protected CartValidationService $validationService;
    protected User $user;
    protected Product $product;
    protected ProductVariant $variant;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->cartService = app(OptimizedCartService::class);
        $this->validationService = app(CartValidationService::class);
        
        // Create test data
        $this->user = User::factory()->create();
        
        $category = Category::factory()->create();
        $this->product = Product::factory()->create(['category_id' => $category->id]);
        $this->variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'price' => 29.99
        ]);

        // Create inventory item for the variant
        \App\Models\InventoryItem::factory()->create([
            'product_variant_id' => $this->variant->id,
            'quantity_on_hand' => 100,
            'quantity_reserved' => 0,
            'track_inventory' => true,
            'allow_backorder' => false
        ]);
    }

    /** @test */
    public function it_caches_cart_data()
    {
        // Create a cart with items
        $cart = $this->cartService->getOrCreateCartForUser($this->user);
        $this->cartService->addItemToCart($cart, $this->variant->id, 2);

        // Clear query log to track subsequent queries
        \DB::flushQueryLog();
        \DB::enableQueryLog();

        // First call should hit database
        $cart1 = $this->cartService->getCartById($cart->id);
        $firstCallQueries = count(\DB::getQueryLog());

        // Clear query log
        \DB::flushQueryLog();

        // Second call should use cache
        $cart2 = $this->cartService->getCartById($cart->id);
        $secondCallQueries = count(\DB::getQueryLog());

        // Assert cache is working
        $this->assertEquals(0, $secondCallQueries, 'Second call should use cache and make no database queries');
        $this->assertEquals($cart1->id, $cart2->id);
        $this->assertEquals($cart1->items->count(), $cart2->items->count());
    }

    /** @test */
    public function it_invalidates_cache_on_cart_updates()
    {
        $cart = $this->cartService->getOrCreateCartForUser($this->user);
        $item = $this->cartService->addItemToCart($cart, $this->variant->id, 1);

        // Cache the cart
        $cachedCart = $this->cartService->getCartById($cart->id);
        $this->assertEquals(1, $cachedCart->items->count());

        // Update cart item
        $this->cartService->updateCartItemQuantity($item, 3);

        // Cache should be invalidated, fresh data should be returned
        $freshCart = $this->cartService->getCartById($cart->id);
        $this->assertEquals(3, $freshCart->items->first()->quantity);
    }

    /** @test */
    public function it_provides_optimized_cart_summary()
    {
        $cart = $this->cartService->getOrCreateCartForUser($this->user);
        $this->cartService->addItemToCart($cart, $this->variant->id, 2);

        $summary = $this->cartService->getCartSummary($cart->id);

        $this->assertArrayHasKey('total_items', $summary);
        $this->assertArrayHasKey('subtotal', $summary);
        $this->assertArrayHasKey('formatted_subtotal', $summary);
        
        $this->assertEquals(2, $summary['total_items']);
        $this->assertEquals(59.98, $summary['subtotal']); // 2 * 29.99
        $this->assertEquals('$59.98', $summary['formatted_subtotal']);
    }

    /** @test */
    public function it_validates_cart_items_correctly()
    {
        $cart = $this->cartService->getOrCreateCartForUser($this->user);
        $item = $this->cartService->addItemToCart($cart, $this->variant->id, 1);

        // Test with valid cart
        $validation = $this->validationService->validateCart($cart);
        $this->assertTrue($validation['valid']);
        $this->assertEmpty($validation['issues']);

        // Test with price change
        $this->variant->update(['price' => 39.99]);
        $validation = $this->validationService->validateCart($cart->fresh(['items.productVariant']));
        
        $this->assertFalse($validation['valid']);
        $this->assertNotEmpty($validation['issues']);
        
        $priceChangeIssue = collect($validation['issues'])->firstWhere('type', 'price_change');
        $this->assertNotNull($priceChangeIssue);
        $this->assertEquals(29.99, $priceChangeIssue['old_price']);
        $this->assertEquals(39.99, $priceChangeIssue['new_price']);
    }

    /** @test */
    public function it_detects_inactive_products()
    {
        $cart = $this->cartService->getOrCreateCartForUser($this->user);
        $this->cartService->addItemToCart($cart, $this->variant->id, 1);

        // Deactivate product
        $this->product->update(['is_active' => false]);

        $validation = $this->validationService->validateCart($cart->fresh(['items.productVariant.product']));
        
        $this->assertFalse($validation['valid']);
        
        $inactiveIssue = collect($validation['issues'])->firstWhere('type', 'product_inactive');
        $this->assertNotNull($inactiveIssue);
        $this->assertEquals('remove', $inactiveIssue['action']);
    }

    /** @test */
    public function it_auto_fixes_cart_issues()
    {
        $cart = $this->cartService->getOrCreateCartForUser($this->user);
        $item = $this->cartService->addItemToCart($cart, $this->variant->id, 1);

        // Change price
        $this->variant->update(['price' => 39.99]);

        $result = $this->validationService->autoFixCart($cart);
        
        $this->assertNotEmpty($result['fixed']);
        $this->assertEmpty($result['failed']);
        
        // Verify price was updated
        $item->refresh();
        $this->assertEquals(39.99, $item->unit_price);
    }

    /** @test */
    public function it_provides_cart_recommendations()
    {
        // Create additional products in same category
        $relatedProduct = Product::factory()->create(['category_id' => $this->product->category_id]);
        $relatedVariant = ProductVariant::factory()->create(['product_id' => $relatedProduct->id]);

        $cart = $this->cartService->getOrCreateCartForUser($this->user);
        $this->cartService->addItemToCart($cart, $this->variant->id, 1);

        $recommendations = $this->cartService->getCartRecommendations($cart, 4);
        
        $this->assertIsArray($recommendations);
        $this->assertNotEmpty($recommendations);
        
        // Should not include products already in cart
        $recommendedProductIds = array_column($recommendations, 'id');
        $this->assertNotContains($this->product->id, $recommendedProductIds);
    }

    /** @test */
    public function cart_controller_returns_validation_messages()
    {
        $this->actingAs($this->user);
        
        $cart = $this->cartService->getOrCreateCartForUser($this->user);
        $this->cartService->addItemToCart($cart, $this->variant->id, 1);

        // Change price to trigger validation issue
        $this->variant->update(['price' => 39.99]);

        $response = $this->get(route('cart.index'));
        
        $response->assertStatus(200);
        $response->assertViewHas('validationMessages');
        
        $validationMessages = $response->viewData('validationMessages');
        $this->assertNotEmpty($validationMessages);
    }

    /** @test */
    public function cart_summary_api_returns_correct_data()
    {
        $this->actingAs($this->user);
        
        $cart = $this->cartService->getOrCreateCartForUser($this->user);
        $this->cartService->addItemToCart($cart, $this->variant->id, 2);

        $response = $this->get(route('cart.summary'));
        
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'cart' => [
                'total_items' => 2,
                'subtotal' => 59.98,
                'formatted_subtotal' => '$59.98'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertArrayHasKey('items', $responseData['cart']);
        $this->assertCount(1, $responseData['cart']['items']); // 1 unique product with quantity 2
    }

    /** @test */
    public function cart_validation_api_returns_issues()
    {
        $this->actingAs($this->user);
        
        $cart = $this->cartService->getOrCreateCartForUser($this->user);
        $this->cartService->addItemToCart($cart, $this->variant->id, 1);

        // Create validation issue
        $this->variant->update(['price' => 39.99]);

        $response = $this->get(route('cart.validate'));
        
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'valid' => false
        ]);
        
        $responseData = $response->json();
        $this->assertArrayHasKey('issues', $responseData);
        $this->assertArrayHasKey('messages', $responseData);
        $this->assertNotEmpty($responseData['issues']);
    }

    /** @test */
    public function cart_recommendations_api_returns_products()
    {
        $this->actingAs($this->user);
        
        // Create related products
        $relatedProduct = Product::factory()->create(['category_id' => $this->product->category_id]);
        ProductVariant::factory()->create(['product_id' => $relatedProduct->id]);

        $cart = $this->cartService->getOrCreateCartForUser($this->user);
        $this->cartService->addItemToCart($cart, $this->variant->id, 1);

        $response = $this->get(route('cart.recommendations'));
        
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true
        ]);
        
        $responseData = $response->json();
        $this->assertArrayHasKey('recommendations', $responseData);
        $this->assertIsArray($responseData['recommendations']);
    }

    /** @test */
    public function empty_cart_returns_appropriate_responses()
    {
        $this->actingAs($this->user);
        
        // Test empty cart summary
        $response = $this->get(route('cart.summary'));
        $response->assertJson([
            'success' => true,
            'cart' => [
                'total_items' => 0,
                'subtotal' => 0,
                'formatted_subtotal' => '$0.00',
                'items' => []
            ]
        ]);

        // Test empty cart validation
        $response = $this->get(route('cart.validate'));
        $response->assertJson([
            'success' => true,
            'valid' => true,
            'issues' => []
        ]);
    }

    protected function tearDown(): void
    {
        Cache::flush();
        parent::tearDown();
    }
}
