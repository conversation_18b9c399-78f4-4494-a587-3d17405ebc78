<?php

namespace Tests\Feature;

use App\Models\Setting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TermsPageCompanyNameTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function terms_page_introduction_uses_company_name_setting()
    {
        $companyName = 'Legal Test Corporation';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('pages.terms'));
        
        $response->assertStatus(200);
        $response->assertSee("Welcome to {$companyName}. These Terms and Conditions");
    }

    /** @test */
    public function terms_page_definitions_section_uses_company_name()
    {
        $companyName = 'Definitions Test LLC';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('pages.terms'));
        
        $response->assertStatus(200);
        $response->assertSee("Refers to {$companyName}.");
    }

    /** @test */
    public function terms_page_services_definition_uses_company_name()
    {
        $companyName = 'Services Provider Inc';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('pages.terms'));
        
        $response->assertStatus(200);
        $response->assertSee("services, and content offered by {$companyName}.");
    }

    /** @test */
    public function terms_page_intellectual_property_uses_company_name()
    {
        $companyName = 'IP Holdings Corp';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('pages.terms'));
        
        $response->assertStatus(200);
        $response->assertSee("is the property of {$companyName} or its content suppliers");
    }

    /** @test */
    public function terms_page_liability_section_uses_company_name()
    {
        $companyName = 'Liability Limited Co';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('pages.terms'));
        
        $response->assertStatus(200);
        $response->assertSee("permitted by law, {$companyName} shall not be liable");
    }

    /** @test */
    public function terms_page_indemnification_uses_company_name()
    {
        $companyName = 'Indemnity Partners LLC';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('pages.terms'));
        
        $response->assertStatus(200);
        $response->assertSee("hold harmless {$companyName} and its officers");
    }

    /** @test */
    public function terms_page_falls_back_to_wisdomtechno_when_no_setting()
    {
        // Don't set company_name, should fall back to WisdomTechno
        $response = $this->get(route('pages.terms'));
        
        $response->assertStatus(200);
        $response->assertSee('Welcome to WisdomTechno. These Terms');
        $response->assertSee('Refers to WisdomTechno.');
        $response->assertSee('offered by WisdomTechno.');
        $response->assertSee('property of WisdomTechno or');
        $response->assertSee('law, WisdomTechno shall not');
        $response->assertSee('harmless WisdomTechno and');
    }

    /** @test */
    public function terms_page_handles_special_characters_in_company_name()
    {
        $companyName = 'Test & Associates, Inc.';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('pages.terms'));
        
        $response->assertStatus(200);
        // Should properly escape HTML entities
        $response->assertSee('Welcome to Test &amp; Associates, Inc.', false);
    }

    /** @test */
    public function terms_page_handles_long_company_names()
    {
        $companyName = 'Very Long Company Name That Might Cause Layout Issues International Corporation Limited';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('pages.terms'));
        
        $response->assertStatus(200);
        $response->assertSee("Welcome to {$companyName}");
    }

    /** @test */
    public function terms_page_updates_when_company_name_changes()
    {
        $originalName = 'Original Terms Company';
        $newName = 'Updated Terms Company';
        
        Setting::setValue('company_name', $originalName);
        
        $response = $this->get(route('pages.terms'));
        $response->assertSee("Welcome to {$originalName}");
        
        // Update the company name
        Setting::setValue('company_name', $newName);
        
        $response = $this->get(route('pages.terms'));
        $response->assertSee("Welcome to {$newName}");
        $response->assertDontSee("Welcome to {$originalName}");
    }

    /** @test */
    public function terms_page_company_name_appears_in_all_expected_locations()
    {
        $companyName = 'Comprehensive Test Corp';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('pages.terms'));
        
        $response->assertStatus(200);
        
        // Count the number of times the company name appears
        $content = $response->getContent();
        $occurrences = substr_count($content, $companyName);
        
        // Should appear in at least 6 locations based on our replacements
        $this->assertGreaterThanOrEqual(6, $occurrences, 
            "Company name should appear in at least 6 locations in the terms page");
    }

    /** @test */
    public function terms_page_does_not_contain_hardcoded_wisdomtechno()
    {
        $companyName = 'Non-WisdomTechno Company';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('pages.terms'));
        
        $response->assertStatus(200);
        
        // Should not contain any hardcoded "WisdomTechno" references
        // (except in the fallback defaults which are not visible when setting exists)
        $content = $response->getContent();
        
        // Remove the fallback defaults from the content for this test
        $contentWithoutDefaults = preg_replace('/WisdomTechno\'\)/', '', $content);
        
        $this->assertStringNotContainsString('WisdomTechno', $contentWithoutDefaults,
            'Terms page should not contain hardcoded WisdomTechno references when company name is set');
    }
}
