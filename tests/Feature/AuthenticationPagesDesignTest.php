<?php

namespace Tests\Feature;

use App\Models\Setting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AuthenticationPagesDesignTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Set up company settings for testing
        Setting::setValue('company_name', 'Test Company Inc');
        Setting::setValue('site_description', 'Test company description for authentication');
    }

    public function test_login_page_displays_company_branding()
    {
        $response = $this->get(route('login'));

        $response->assertStatus(200);
        $response->assertSee('Test Company Inc');
        $response->assertSee('Test company description for authentication');
        $response->assertSee('Welcome back');
        $response->assertSee('Sign in to your account to continue');
    }

    public function test_login_page_has_modern_design_elements()
    {
        $response = $this->get(route('login'));

        $response->assertStatus(200);
        // Check for gradient backgrounds
        $response->assertSee('bg-gradient-to-br from-indigo-50', false);
        $response->assertSee('bg-gradient-to-r from-indigo-600 to-purple-600', false);
        // Check for backdrop blur
        $response->assertSee('backdrop-blur-lg', false);
        // Check for modern form styling
        $response->assertSee('rounded-lg', false);
        $response->assertSee('shadow-2xl', false);
    }

    public function test_register_page_displays_company_branding()
    {
        $response = $this->get(route('register'));

        $response->assertStatus(200);
        $response->assertSee('Test Company Inc');
        $response->assertSee('Test company description for authentication');
        $response->assertSee('Create your account');
        $response->assertSee('Join us and start your journey today');
    }

    public function test_register_page_includes_terms_and_privacy_links()
    {
        $response = $this->get(route('register'));

        $response->assertStatus(200);
        $response->assertSee('By creating an account, you agree to our');
        $response->assertSee('Terms of Service');
        $response->assertSee('Privacy Policy');
        $response->assertSee(route('terms'), false);
    }

    public function test_forgot_password_page_displays_company_branding()
    {
        $response = $this->get(route('password.request'));

        $response->assertStatus(200);
        $response->assertSee('Test Company Inc');
        $response->assertSee('Forgot your password?');
        $response->assertSee("No problem. Just let us know your email address and we'll email you a password reset link.");
    }

    public function test_reset_password_page_displays_company_branding()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password')
        ]);

        $token = app('auth.password.broker')->createToken($user);

        $response = $this->get(route('password.reset', ['token' => $token, 'email' => $user->email]));

        $response->assertStatus(200);
        $response->assertSee('Test Company Inc');
        $response->assertSee('Reset your password');
        $response->assertSee('Enter your new password below');
    }

    public function test_confirm_password_page_displays_company_branding()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get(route('password.confirm'));

        $response->assertStatus(200);
        $response->assertSee('Test Company Inc');
        $response->assertSee('Confirm your password');
        $response->assertSee('This is a secure area of the application');
    }

    public function test_verify_email_page_displays_company_branding()
    {
        $user = User::factory()->create([
            'email_verified_at' => null
        ]);

        $response = $this->actingAs($user)->get(route('verification.notice'));

        $response->assertStatus(200);
        $response->assertSee('Test Company Inc');
        $response->assertSee('Verify your email');
        $response->assertSee('Thanks for signing up!');
    }

    public function test_authentication_pages_have_consistent_navigation()
    {
        $pages = [
            route('login'),
            route('register'),
            route('password.request')
        ];

        foreach ($pages as $page) {
            $response = $this->get($page);

            $response->assertStatus(200);
            // Check for footer navigation links
            $response->assertSee(route('home'), false);
            $response->assertSee(route('about'), false);
            $response->assertSee(route('contact'), false);
            // Check for copyright
            $response->assertSee('&copy; ' . date('Y') . ' Test Company Inc', false);
        }
    }

    public function test_authentication_pages_have_proper_form_validation_styling()
    {
        $response = $this->get(route('login'));

        $response->assertStatus(200);
        // Check for proper input styling
        $response->assertSee('focus:ring-indigo-500', false);
        $response->assertSee('border-gray-300 dark:border-gray-600', false);
        $response->assertSee('placeholder-gray-500 dark:placeholder-gray-400', false);
    }

    public function test_authentication_pages_have_dark_mode_support()
    {
        $response = $this->get(route('login'));

        $response->assertStatus(200);
        // Check for dark mode classes
        $response->assertSee('dark:bg-gray-800', false);
        $response->assertSee('dark:text-white', false);
        $response->assertSee('dark:text-gray-400', false);
        $response->assertSee('dark:border-gray-700', false);
    }

    public function test_authentication_pages_have_responsive_design()
    {
        $response = $this->get(route('login'));

        $response->assertStatus(200);
        // Check for responsive classes
        $response->assertSee('sm:max-w-md', false);
        $response->assertSee('sm:rounded-2xl', false);
        $response->assertSee('sm:px-10', false);
    }

    public function test_authentication_pages_have_accessibility_features()
    {
        $response = $this->get(route('login'));

        $response->assertStatus(200);
        // Check for proper labels and ARIA attributes
        $response->assertSee('for="email"', false);
        $response->assertSee('for="password"', false);
        $response->assertSee('autocomplete="username"', false);
        $response->assertSee('autocomplete="current-password"', false);
    }

    public function test_authentication_forms_maintain_functionality()
    {
        // Test login functionality
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password')
        ]);

        $response = $this->post(route('login'), [
            'email' => '<EMAIL>',
            'password' => 'password'
        ]);

        $response->assertRedirect(route('dashboard'));
        $this->assertAuthenticatedAs($user);
    }

    public function test_authentication_forms_show_validation_errors()
    {
        $response = $this->post(route('login'), [
            'email' => 'invalid-email',
            'password' => ''
        ]);

        $response->assertSessionHasErrors(['email', 'password']);
        $response->assertStatus(302);
    }

    public function test_company_name_fallback_works_when_not_set()
    {
        // Remove company name setting
        Setting::where('key', 'company_name')->delete();

        $response = $this->get(route('login'));

        $response->assertStatus(200);
        $response->assertSee('WisdomTechno'); // Should fall back to default
    }

    public function test_authentication_pages_load_custom_css()
    {
        $response = $this->get(route('login'));

        $response->assertStatus(200);
        $response->assertSee('css/custom.css', false);
    }

    public function test_authentication_pages_use_custom_logo()
    {
        $response = $this->get(route('login'));

        $response->assertStatus(200);
        // Check that custom logo is used instead of Laravel application logo
        $response->assertSee('img/logo.png', false);
        $response->assertSee('Test Company Inc Logo', false);
        // Ensure Laravel application logo component is not used
        $response->assertDontSee('<x-application-logo', false);
    }

    public function test_authentication_pages_have_proper_page_titles()
    {
        $pages = [
            route('login') => 'Sign In - Test Company Inc',
            route('register') => 'Sign Up - Test Company Inc',
            route('password.request') => 'Forgot Password - Test Company Inc'
        ];

        foreach ($pages as $url => $expectedTitle) {
            $response = $this->get($url);

            $response->assertStatus(200);
            $response->assertSee("<title>{$expectedTitle}</title>", false);
        }
    }
}
