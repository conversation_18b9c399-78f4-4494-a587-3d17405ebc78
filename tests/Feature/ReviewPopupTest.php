<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ReviewPopupTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->category = Category::factory()->create([
            'name' => ['en' => 'Electronics'],
            'slug' => ['en' => 'electronics'],
        ]);

        $this->product = Product::factory()->create([
            'name' => ['en' => 'Test Product'],
            'description' => ['en' => 'Test product description'],
            'slug' => ['en' => 'test-product'],
            'category_id' => $this->category->id,
            'is_active' => true,
        ]);

        $this->variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'price' => 99.99,
            'is_active' => true,
        ]);

        $this->user = User::factory()->create();
    }

    public function test_product_page_contains_review_modal()
    {
        $response = $this->get(route('products.show', $this->product->getTranslation('slug', 'en')));

        $response->assertStatus(200)
                ->assertSee('Write a Review')
                ->assertSee('reviewModal')
                ->assertSee('write-review-button');
    }

    public function test_product_page_contains_review_modal_elements()
    {
        $response = $this->get(route('products.show', $this->product->getTranslation('slug', 'en')));

        $response->assertStatus(200)
                // Check for modal structure
                ->assertSee('reviewModal')
                ->assertSee('closeModal')
                ->assertSee('reviewForm')
                ->assertSee('starRating')
                ->assertSee('submitReview')
                // Check for form fields
                ->assertSee('Rating')
                ->assertSee('Review Title')
                ->assertSee('Your Review')
                ->assertSee('Add Photos');
    }

    public function test_review_form_data_endpoint_requires_authentication()
    {
        $response = $this->getJson(route('reviews.form-data', $this->product));

        $response->assertStatus(401)
                ->assertJson([
                    'message' => 'Unauthenticated.',
                ]);
    }

    public function test_authenticated_user_can_access_review_form_data()
    {
        $response = $this->actingAs($this->user)
                         ->getJson(route('reviews.form-data', $this->product));

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'can_review',
                    'has_purchased',
                    'existing_review',
                    'product' => ['id', 'name'],
                ]);
    }

    public function test_review_submission_requires_authentication()
    {
        $response = $this->postJson(route('reviews.store', $this->product), [
            'rating' => 5,
            'title' => 'Great product!',
            'content' => 'This product is amazing and works perfectly.',
        ]);

        $response->assertStatus(401);
    }

    public function test_authenticated_user_can_submit_review_via_ajax()
    {
        $response = $this->actingAs($this->user)
                         ->postJson(route('reviews.store', $this->product), [
                             'rating' => 5,
                             'title' => 'Great product!',
                             'content' => 'This product is amazing and works perfectly.',
                         ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Thank you for your review! It will be published after moderation.',
                ]);

        $this->assertDatabaseHas('reviews', [
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'rating' => 5,
            'title' => 'Great product!',
        ]);
    }

    public function test_review_modal_javascript_is_included()
    {
        $response = $this->get(route('products.show', $this->product->getTranslation('slug', 'en')));

        $response->assertStatus(200)
                // Check for JavaScript functionality
                ->assertSee('openReviewModal')
                ->assertSee('submitReview')
                ->assertSee('setRating')
                ->assertSee('highlightStars');
    }

    public function test_review_modal_has_proper_styling()
    {
        $response = $this->get(route('products.show', $this->product->getTranslation('slug', 'en')));

        $response->assertStatus(200)
                // Check for modal styling classes
                ->assertSee('fixed inset-0')
                ->assertSee('bg-gray-600 bg-opacity-50')
                ->assertSee('z-50 hidden')
                ->assertSee('star-btn')
                ->assertSee('text-yellow-400');
    }

    public function test_review_form_has_validation_attributes()
    {
        $response = $this->get(route('products.show', $this->product->getTranslation('slug', 'en')));

        $response->assertStatus(200)
                // Check for form validation attributes
                ->assertSee('required')
                ->assertSee('maxlength=&quot;255&quot;')
                ->assertSee('maxlength=&quot;2000&quot;')
                ->assertSee('accept=&quot;image/*&quot;');
    }

    public function test_review_modal_shows_product_information()
    {
        $response = $this->get(route('products.show', $this->product->getTranslation('slug', 'en')));

        $response->assertStatus(200)
                ->assertSee($this->product->getTranslation('name', 'en'))
                ->assertSee("You&#039;re reviewing this product");
    }

    public function test_review_modal_has_character_counter()
    {
        $response = $this->get(route('products.show', $this->product->getTranslation('slug', 'en')));

        $response->assertStatus(200)
                ->assertSee('contentCount')
                ->assertSee('/2000 characters');
    }

    public function test_review_modal_has_loading_states()
    {
        $response = $this->get(route('products.show', $this->product->getTranslation('slug', 'en')));

        $response->assertStatus(200)
                ->assertSee('reviewFormLoading')
                ->assertSee('submit-loading')
                ->assertSee('Submitting...');
    }

    public function test_review_modal_has_different_user_states()
    {
        $response = $this->get(route('products.show', $this->product->getTranslation('slug', 'en')));

        $response->assertStatus(200)
                ->assertSee('loginRequired')
                ->assertSee('alreadyReviewed')
                ->assertSee('Login Required')
                ->assertSee('Review Already Submitted');
    }
}
