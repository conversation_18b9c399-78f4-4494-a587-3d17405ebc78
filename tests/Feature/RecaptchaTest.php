<?php

namespace Tests\Feature;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\NoCaptcha\NoCaptcha;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class RecaptchaTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_renders_recaptcha_widget()
    {
        // Mock the reCAPTCHA configuration
        Config::set('captcha.sitekey', 'test_site_key');
        
        $response = $this->get('/contact');
        
        $response->assertStatus(200);
        $response->assertSee('g-recaptcha');
    }

    /** @test */
    public function it_validates_recaptcha_response()
    {
        // Skip reCAPTCHA validation in tests by default
        Config::set('captcha.secret', 'test_secret_key');
        
        // Mock a successful reCAPTCHA response
        $mock = $this->mock(NoCaptcha::class);
        $mock->shouldReceive('verifyResponse')
            ->once()
            ->andReturn((object)['success' => true]);
        
        $response = $this->post('/contact', [
            'g-recaptcha-response' => 'test_token',
            // Add other required fields for your contact form
        ]);
        
        $response->assertStatus(302); // Assuming a redirect on success
    }
    
    /** @test */
    public function it_fails_on_invalid_recaptcha()
    {
        Config::set('captcha.secret', 'test_secret_key');
        
        // Mock a failed reCAPTCHA response
        $mock = $this->mock(NoCaptcha::class);
        $mock->shouldReceive('verifyResponse')
            ->once()
            ->andReturn((object)[
                'success' => false,
                'error-codes' => ['invalid-input-response']
            ]);
        
        $response = $this->post('/contact', [
            'g-recaptcha-response' => 'invalid_token',
            // Add other required fields for your contact form
        ]);
        
        $response->assertSessionHasErrors('g-recaptcha-response');
    }
}
