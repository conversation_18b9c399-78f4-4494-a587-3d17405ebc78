<?php

namespace Tests\Feature;

use App\Models\Setting;
use App\Models\ContentBlock;
use App\Models\Language;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ContentBlockCompanyIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create default language
        Language::factory()->create([
            'code' => 'en',
            'name' => 'English',
            'is_default' => true,
            'is_active' => true
        ]);
    }

    /** @test */
    public function about_page_title_uses_company_name_setting_as_default()
    {
        $companyName = 'Innovative Tech Corp';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('pages.about'));
        
        $response->assertStatus(200);
        
        // Check that the page title includes the company name
        $response->assertSee("<title>About {$companyName}</title>", false);
    }

    /** @test */
    public function about_page_hero_description_uses_company_name_in_default()
    {
        $companyName = 'Future Solutions Ltd';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('pages.about'));
        
        $response->assertStatus(200);
        
        // The hero description should include the company name in the default text
        $response->assertSee("{$companyName} is a leading provider");
    }

    /** @test */
    public function about_page_approach_intro_uses_company_name_in_default()
    {
        $companyName = 'Strategic Tech Partners';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('pages.about'));
        
        $response->assertStatus(200);
        
        // The approach intro should include the company name
        $response->assertSee("At {$companyName}, we take");
    }

    /** @test */
    public function content_block_overrides_company_name_defaults()
    {
        $companyName = 'Default Company';
        $customTitle = 'Custom About Page Title';
        
        Setting::setValue('company_name', $companyName);
        
        // Create a content block that overrides the default
        $contentBlock = ContentBlock::create([
            'key' => 'about.meta.title',
            'location' => 'about_page',
            'is_active' => true,
            'title' => ['en' => $customTitle],
            'content' => ['en' => $customTitle]
        ]);
        
        $response = $this->get(route('pages.about'));
        
        $response->assertStatus(200);
        
        // Should use the custom content block value, not the default with company name
        $response->assertSee("<title>{$customTitle}</title>", false);
        $response->assertDontSee("About {$companyName}");
    }

    /** @test */
    public function content_block_fallback_works_when_block_inactive()
    {
        $companyName = 'Fallback Test Company';
        Setting::setValue('company_name', $companyName);
        
        // Create an inactive content block
        ContentBlock::create([
            'key' => 'about.page.title',
            'location' => 'about_page',
            'is_active' => false,
            'title' => ['en' => 'Inactive Title'],
            'content' => ['en' => 'Inactive Content']
        ]);
        
        $response = $this->get(route('pages.about'));
        
        $response->assertStatus(200);
        
        // Should fall back to the default with company name
        $response->assertSee("About {$companyName}");
        $response->assertDontSee('Inactive Title');
    }

    /** @test */
    public function company_name_change_reflects_in_content_defaults()
    {
        $originalName = 'Original Company';
        $newName = 'Updated Company Name';
        
        // Set initial company name
        Setting::setValue('company_name', $originalName);
        
        $response = $this->get(route('pages.about'));
        $response->assertSee("About {$originalName}");
        
        // Update company name
        Setting::setValue('company_name', $newName);
        
        $response = $this->get(route('pages.about'));
        $response->assertSee("About {$newName}");
        $response->assertDontSee("About {$originalName}");
    }

    /** @test */
    public function multiple_content_blocks_use_company_name_consistently()
    {
        $companyName = 'Consistent Brand Corp';
        Setting::setValue('company_name', $companyName);
        
        $response = $this->get(route('pages.about'));
        
        $response->assertStatus(200);
        
        // Check multiple places where company name should appear
        $response->assertSee("About {$companyName}"); // Page title
        $response->assertSee("{$companyName} is a leading provider"); // Hero description
        $response->assertSee("At {$companyName}, we take"); // Approach intro
    }

    /** @test */
    public function content_block_helper_facade_works_with_company_name_defaults()
    {
        $companyName = 'Facade Test Company';
        Setting::setValue('company_name', $companyName);
        
        // Test the ContentBlock facade directly
        $title = \App\Facades\ContentBlock::get('about.meta.title', 'About ' . Setting::getValue('company_name', 'WisdomTechno'));
        
        $this->assertEquals("About {$companyName}", $title);
    }

    /** @test */
    public function empty_company_name_falls_back_to_wisdomtechno()
    {
        // Don't set any company name, should fall back to WisdomTechno
        $response = $this->get(route('pages.about'));
        
        $response->assertStatus(200);
        $response->assertSee('About WisdomTechno');
    }
}
