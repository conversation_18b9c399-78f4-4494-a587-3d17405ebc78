<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Review;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SimpleReviewTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->category = Category::factory()->create([
            'name' => ['en' => 'Electronics'],
            'slug' => ['en' => 'electronics'],
        ]);

        $this->product = Product::factory()->create([
            'name' => ['en' => 'Test Product'],
            'description' => ['en' => 'Test product description'],
            'slug' => ['en' => 'test-product'],
            'category_id' => $this->category->id,
            'is_active' => true,
        ]);

        $this->variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'price' => 99.99,
            'is_active' => true,
        ]);

        $this->user = User::factory()->create();
    }

    public function test_review_model_can_be_created()
    {
        $review = Review::create([
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'rating' => 5,
            'title' => 'Great product!',
            'content' => 'This product is amazing and works perfectly.',
            'verified_purchase' => false,
            'is_approved' => false,
        ]);

        $this->assertDatabaseHas('reviews', [
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'rating' => 5,
            'title' => 'Great product!',
        ]);

        $this->assertEquals(5, $review->rating);
        $this->assertEquals('Great product!', $review->title);
    }

    public function test_product_has_reviews_relationship()
    {
        $review = Review::factory()->create([
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'is_approved' => true,
        ]);

        $this->assertTrue($this->product->reviews()->exists());
        $this->assertEquals(1, $this->product->reviews()->count());
        $this->assertEquals($review->id, $this->product->reviews()->first()->id);
    }

    public function test_user_has_reviews_relationship()
    {
        $review = Review::factory()->create([
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
        ]);

        $this->assertTrue($this->user->reviews()->exists());
        $this->assertEquals(1, $this->user->reviews()->count());
        $this->assertEquals($review->id, $this->user->reviews()->first()->id);
    }

    public function test_review_scopes_work()
    {
        // Create approved and unapproved reviews
        $approvedReview = Review::factory()->create([
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'is_approved' => true,
        ]);

        $unapprovedReview = Review::factory()->create([
            'product_id' => $this->product->id,
            'user_id' => User::factory()->create()->id,
            'is_approved' => false,
        ]);

        // Test approved scope
        $approvedReviews = Review::approved()->get();
        $this->assertEquals(1, $approvedReviews->count());
        $this->assertEquals($approvedReview->id, $approvedReviews->first()->id);

        // Test by rating scope
        $fiveStarReviews = Review::byRating(5)->get();
        $this->assertGreaterThanOrEqual(1, $fiveStarReviews->count());
    }

    public function test_product_average_rating_calculation()
    {
        // Create reviews with different ratings
        Review::factory()->create([
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'rating' => 5,
            'is_approved' => true,
        ]);

        Review::factory()->create([
            'product_id' => $this->product->id,
            'user_id' => User::factory()->create()->id,
            'rating' => 3,
            'is_approved' => true,
        ]);

        // Refresh the product to get updated attributes
        $this->product->refresh();

        // Test average rating (5 + 3) / 2 = 4.0
        $this->assertEquals(4.0, $this->product->average_rating);
        $this->assertEquals(2, $this->product->reviews_count);
    }

    public function test_review_factory_works()
    {
        $review = Review::factory()->create([
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
        ]);

        $this->assertInstanceOf(Review::class, $review);
        $this->assertNotNull($review->id);
        $this->assertNotNull($review->title);
        $this->assertNotNull($review->content);
        $this->assertBetween($review->rating, 1, 5);
    }

    public function test_review_factory_states_work()
    {
        $approvedReview = Review::factory()->approved()->create([
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
        ]);

        $this->assertTrue($approvedReview->is_approved);
        $this->assertNotNull($approvedReview->approved_at);

        $featuredReview = Review::factory()->featured()->create([
            'product_id' => $this->product->id,
            'user_id' => User::factory()->create()->id,
        ]);

        $this->assertTrue($featuredReview->is_featured);
        $this->assertTrue($featuredReview->is_approved);

        $verifiedReview = Review::factory()->verifiedPurchase()->create([
            'product_id' => $this->product->id,
            'user_id' => User::factory()->create()->id,
        ]);

        $this->assertTrue($verifiedReview->verified_purchase);
    }

    private function assertBetween($value, $min, $max)
    {
        $this->assertGreaterThanOrEqual($min, $value);
        $this->assertLessThanOrEqual($max, $value);
    }
}
