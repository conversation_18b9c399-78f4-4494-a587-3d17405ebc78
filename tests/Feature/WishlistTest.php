<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\User;
use App\Models\Wishlist;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class WishlistTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create();

        // Create test category
        $category = Category::factory()->create([
            'name' => ['en' => 'Test Category'],
            'slug' => ['en' => 'test-category'],
        ]);

        // Create test product
        $this->product = Product::factory()->create([
            'category_id' => $category->id,
            'name' => ['en' => 'Test Product'],
            'slug' => ['en' => 'test-product'],
            'description' => ['en' => 'Test product description'],
            'is_active' => true,
        ]);

        // Create test variant
        ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'price' => 99.99,
            'compare_at_price' => 129.99,
        ]);
    }

    public function test_user_can_view_empty_wishlist(): void
    {
        $response = $this->actingAs($this->user)->get(route('wishlist.index'));

        $response->assertStatus(200)
                ->assertSee('Your wishlist is empty')
                ->assertSee('Start adding products');
    }

    public function test_user_can_add_product_to_wishlist(): void
    {
        $response = $this->actingAs($this->user)
                         ->postJson(route('wishlist.store'), [
                             'product_id' => $this->product->id
                         ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'in_wishlist' => true,
                    'wishlist_count' => 1,
                ])
                ->assertJsonStructure([
                    'success',
                    'message',
                    'in_wishlist',
                    'wishlist_count'
                ]);

        $this->assertDatabaseHas('wishlists', [
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
        ]);
    }

    public function test_user_cannot_add_same_product_twice(): void
    {
        // Add product first time
        $this->user->addToWishlist($this->product->id);

        // Try to add again
        $response = $this->actingAs($this->user)
                         ->postJson(route('wishlist.store'), [
                             'product_id' => $this->product->id
                         ]);

        $response->assertStatus(409)
                ->assertJson([
                    'success' => false,
                    'in_wishlist' => true,
                ]);
    }

    public function test_user_can_remove_product_from_wishlist(): void
    {
        // Add product to wishlist first
        $this->user->addToWishlist($this->product->id);

        $response = $this->actingAs($this->user)
                         ->deleteJson(route('wishlist.destroy', $this->product->id));

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'in_wishlist' => false,
                    'wishlist_count' => 0,
                ]);

        $this->assertDatabaseMissing('wishlists', [
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
        ]);
    }

    public function test_user_can_toggle_wishlist_status(): void
    {
        // Toggle to add
        $response = $this->actingAs($this->user)
                         ->postJson(route('wishlist.toggle'), [
                             'product_id' => $this->product->id
                         ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'action' => 'added',
                    'in_wishlist' => true,
                ]);

        // Toggle to remove
        $response = $this->actingAs($this->user)
                         ->postJson(route('wishlist.toggle'), [
                             'product_id' => $this->product->id
                         ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'action' => 'removed',
                    'in_wishlist' => false,
                ]);
    }

    public function test_user_can_view_wishlist_with_products(): void
    {
        // Add product to wishlist
        $this->user->addToWishlist($this->product->id);

        $response = $this->actingAs($this->user)->get(route('wishlist.index'));

        $response->assertStatus(200)
                ->assertSee($this->product->getTranslation('name', 'en'))
                ->assertSee('1 item')
                ->assertDontSee('Your wishlist is empty');
    }

    public function test_user_can_clear_entire_wishlist(): void
    {
        // Add multiple products
        $product2 = Product::factory()->create([
            'category_id' => $this->product->category_id,
            'name' => ['en' => 'Test Product 2'],
            'slug' => ['en' => 'test-product-2'],
            'is_active' => true,
        ]);

        $this->user->addToWishlist($this->product->id);
        $this->user->addToWishlist($product2->id);

        $response = $this->actingAs($this->user)
                         ->deleteJson(route('wishlist.clear'));

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'wishlist_count' => 0,
                ]);

        $this->assertEquals(0, $this->user->wishlists()->count());
    }

    public function test_user_can_check_wishlist_status(): void
    {
        $response = $this->actingAs($this->user)
                         ->getJson(route('wishlist.status', $this->product->id));

        $response->assertStatus(200)
                ->assertJson([
                    'in_wishlist' => false,
                    'wishlist_count' => 0,
                ]);

        // Add to wishlist and check again
        $this->user->addToWishlist($this->product->id);

        $response = $this->actingAs($this->user)
                         ->getJson(route('wishlist.status', $this->product->id));

        $response->assertStatus(200)
                ->assertJson([
                    'in_wishlist' => true,
                    'wishlist_count' => 1,
                ]);
    }

    public function test_guest_cannot_access_wishlist(): void
    {
        $response = $this->get(route('wishlist.index'));
        $response->assertRedirect(route('login'));

        $response = $this->postJson(route('wishlist.store'), [
            'product_id' => $this->product->id
        ]);
        $response->assertStatus(401);
    }

    public function test_cannot_add_inactive_product_to_wishlist(): void
    {
        // Make product inactive
        $this->product->update(['is_active' => false]);

        $response = $this->actingAs($this->user)
                         ->postJson(route('wishlist.store'), [
                             'product_id' => $this->product->id
                         ]);

        $response->assertStatus(404)
                ->assertJson([
                    'success' => false,
                ]);
    }

    public function test_cannot_add_nonexistent_product_to_wishlist(): void
    {
        $response = $this->actingAs($this->user)
                         ->postJson(route('wishlist.store'), [
                             'product_id' => 'nonexistent-id'
                         ]);

        $response->assertStatus(422); // Validation error
    }
}
