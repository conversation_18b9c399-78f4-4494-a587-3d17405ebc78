<?php

namespace Tests\Feature;

use App\Models\Setting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CompanyNameSettingTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user for testing admin functionality
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>'
        ]);
    }

    public function test_company_name_setting_can_be_created()
    {
        $companyName = 'Test Company Inc.';

        Setting::setValue('company_name', $companyName, 'general', 'text');

        $this->assertEquals($companyName, Setting::getValue('company_name'));
    }

    public function test_company_name_setting_returns_default_when_not_set()
    {
        $default = 'Default Company';

        $this->assertEquals($default, Setting::getValue('company_name', $default));
    }

    public function test_company_name_setting_can_be_updated()
    {
        $originalName = 'Original Company';
        $newName = 'Updated Company';

        Setting::setValue('company_name', $originalName);
        Setting::setValue('company_name', $newName);

        $this->assertEquals($newName, Setting::getValue('company_name'));
    }

    /** @test */
    public function admin_can_update_company_name_via_settings_form()
    {
        $this->actingAs($this->admin);

        $newCompanyName = 'New Company Name';

        $response = $this->put(route('admin.settings.update'), [
            'company_name' => $newCompanyName,
            'site_name' => 'Test Site',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+1234567890'
        ]);

        $response->assertRedirect(route('admin.settings.index'));
        $response->assertSessionHas('success');

        $this->assertEquals($newCompanyName, Setting::getValue('company_name'));
    }

    /** @test */
    public function terms_page_displays_company_name_from_setting()
    {
        $companyName = 'Custom Company Name';
        Setting::setValue('company_name', $companyName);

        $response = $this->get(route('pages.terms'));

        $response->assertStatus(200);
        $response->assertSee($companyName);
        $response->assertSee("Welcome to {$companyName}");
    }

    /** @test */
    public function about_page_uses_company_name_in_content_defaults()
    {
        $companyName = 'Test Tech Solutions';
        Setting::setValue('company_name', $companyName);

        $response = $this->get(route('pages.about'));

        $response->assertStatus(200);
        // The about page should use the company name in ContentBlock defaults
        $response->assertSee("About {$companyName}");
    }

    /** @test */
    public function checkout_confirmation_uses_contact_settings()
    {
        Setting::setValue('contact_email', '<EMAIL>');
        Setting::setValue('contact_phone', '******-123-4567');

        // Create a test order for the confirmation page
        $user = User::factory()->create();
        $order = \App\Models\Order::factory()->create(['user_id' => $user->id]);

        $this->actingAs($user);

        $response = $this->get(route('checkout.confirmation', $order));

        $response->assertStatus(200);
        $response->assertSee('mailto:<EMAIL>');
        $response->assertSee('tel:******-123-4567');
    }

    /** @test */
    public function admin_settings_form_includes_company_name_field()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('admin.settings.index'));

        $response->assertStatus(200);
        $response->assertSee('name="company_name"', false);
        $response->assertSee('Company Name');
    }

    /** @test */
    public function admin_settings_form_removes_hardcoded_defaults()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('admin.settings.index'));

        $response->assertStatus(200);
        // Should not contain hardcoded defaults in the form
        $response->assertDontSee('<EMAIL>');
        $response->assertDontSee('+852 4453 7120');
        $response->assertDontSee('<EMAIL>');
    }

    /** @test */
    public function migration_adds_company_name_setting_if_not_exists()
    {
        // Ensure the setting doesn't exist
        Setting::where('key', 'company_name')->delete();

        // Run the migration
        $this->artisan('migrate:fresh --seed');

        // Check that the company_name setting exists
        $this->assertNotNull(Setting::where('key', 'company_name')->first());
        $this->assertEquals('WisdomTechno', Setting::getValue('company_name'));
    }
}
