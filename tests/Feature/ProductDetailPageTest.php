<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\InventoryItem;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class ProductDetailPageTest extends TestCase
{
    use RefreshDatabase;

    protected Product $product;
    protected ProductVariant $variant;
    protected Category $category;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test category
        $this->category = Category::factory()->create([
            'name' => ['en' => 'Test Category'],
            'slug' => ['en' => 'test-category'],
        ]);

        // Create test product
        $this->product = Product::factory()->create([
            'category_id' => $this->category->id,
            'name' => ['en' => 'Test Product'],
            'slug' => ['en' => 'test-product'],
            'description' => ['en' => 'This is a test product description'],
            'is_active' => true,
        ]);

        // Create test variant
        $this->variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'price' => 99.99,
            'compare_at_price' => 129.99,
            'is_active' => true,
        ]);

        // Create inventory item
        InventoryItem::factory()->create([
            'product_variant_id' => $this->variant->id,
            'quantity_on_hand' => 50,
            'quantity_reserved' => 0,
            'track_inventory' => true,
            'allow_backorder' => false,
        ]);
    }

    public function test_product_page_displays_correctly(): void
    {
        $response = $this->get(route('products.show', 'test-product'));

        $response->assertStatus(200)
            ->assertSee('Test Product')
            ->assertSee('This is a test product description')
            ->assertSee('$99.99')
            ->assertSee('$129.99')
            ->assertSee('Test Category');
    }

    public function test_product_page_shows_variant_information(): void
    {
        $response = $this->get(route('products.show', 'test-product'));

        $response->assertStatus(200)
            ->assertSee('In stock')
            ->assertSee('Add to Cart')
            ->assertSee('Buy Now');
    }

    public function test_product_not_found_returns_404(): void
    {
        $response = $this->get(route('products.show', 'non-existent-product'));

        $response->assertStatus(404);
    }

    public function test_inactive_product_returns_404(): void
    {
        $this->product->update(['is_active' => false]);

        $response = $this->get(route('products.show', 'test-product'));

        $response->assertStatus(404);
    }

    public function test_product_page_displays_related_products(): void
    {
        // Create related products in the same category
        $relatedProduct1 = Product::factory()->create([
            'category_id' => $this->category->id,
            'name' => ['en' => 'Related Product 1'],
            'slug' => ['en' => 'related-product-1'],
            'is_active' => true,
        ]);

        $relatedProduct2 = Product::factory()->create([
            'category_id' => $this->category->id,
            'name' => ['en' => 'Related Product 2'],
            'slug' => ['en' => 'related-product-2'],
            'is_active' => true,
        ]);

        // Create variants for related products
        ProductVariant::factory()->create([
            'product_id' => $relatedProduct1->id,
            'price' => 49.99,
        ]);

        ProductVariant::factory()->create([
            'product_id' => $relatedProduct2->id,
            'price' => 79.99,
        ]);

        $response = $this->get(route('products.show', 'test-product'));

        $response->assertStatus(200)
            ->assertSee('Related Products')
            ->assertSee('Related Product 1')
            ->assertSee('Related Product 2');
    }

    public function test_product_page_handles_out_of_stock_variants(): void
    {
        // Update inventory to be out of stock
        $this->variant->inventoryItem->update([
            'quantity_on_hand' => 0,
            'allow_backorder' => false,
        ]);

        $response = $this->get(route('products.show', 'test-product'));

        $response->assertStatus(200)
            ->assertSee('Out of stock');
    }

    public function test_product_page_shows_low_stock_warning(): void
    {
        // Update inventory to low stock
        $this->variant->inventoryItem->update([
            'quantity_on_hand' => 3,
            'quantity_reserved' => 0,
        ]);

        $response = $this->get(route('products.show', 'test-product'));

        $response->assertStatus(200)
            ->assertSee('Only 3 left');
    }

    public function test_product_page_displays_discount_percentage(): void
    {
        $response = $this->get(route('products.show', 'test-product'));

        // Calculate expected discount: (1 - 99.99/129.99) * 100 = 23%
        $response->assertStatus(200)
            ->assertSee('23% off');
    }

    public function test_product_page_includes_breadcrumb_navigation(): void
    {
        $response = $this->get(route('products.show', 'test-product'));

        $response->assertStatus(200)
            ->assertSee('Store')
            ->assertSee('Test Category')
            ->assertSee('Test Product');
    }

    public function test_product_page_includes_social_sharing_buttons(): void
    {
        $response = $this->get(route('products.show', 'test-product'));

        $response->assertStatus(200)
            ->assertSee('data-platform="facebook"', false)
            ->assertSee('data-platform="twitter"', false)
            ->assertSee('data-platform="pinterest"', false)
            ->assertSee('data-platform="email"', false);
    }

    public function test_product_page_includes_required_meta_tags(): void
    {
        $response = $this->get(route('products.show', 'test-product'));

        $response->assertStatus(200)
            ->assertSee('<meta name="csrf-token"', false);
    }

    public function test_product_page_loads_required_assets(): void
    {
        $response = $this->get(route('products.show', 'test-product'));

        $response->assertStatus(200)
            ->assertSee('product-page.css')
            ->assertSee('product-page.js');
    }

    public function test_product_page_caching_works(): void
    {
        // Clear cache first
        Cache::flush();

        // First request should hit the database
        $response1 = $this->get(route('products.show', 'test-product'));
        $response1->assertStatus(200);

        // Second request should use cache
        $response2 = $this->get(route('products.show', 'test-product'));
        $response2->assertStatus(200);

        // Verify cache key exists
        $this->assertTrue(Cache::has('product_detail_test-product'));
    }

    public function test_product_page_handles_multiple_variants(): void
    {
        // Create additional variants with attributes
        $variant2 = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'price' => 109.99,
            'attributes' => ['color' => 'red', 'size' => 'large'],
            'is_active' => true,
        ]);

        $variant3 = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'price' => 89.99,
            'attributes' => ['color' => 'blue', 'size' => 'medium'],
            'is_active' => true,
        ]);

        $response = $this->get(route('products.show', 'test-product'));

        $response->assertStatus(200)
            ->assertSee('$89.99 - $109.99') // Price range
            ->assertSee('Color')
            ->assertSee('Size');
    }

    public function test_empty_slug_returns_404(): void
    {
        // Test with a space character which should be handled as empty
        $response = $this->get('/store/products/ ');

        $response->assertStatus(404);
    }

    public function test_product_page_performance_with_many_related_products(): void
    {
        // Create many related products to test query optimization
        for ($i = 1; $i <= 20; $i++) {
            $relatedProduct = Product::factory()->create([
                'category_id' => $this->category->id,
                'name' => ['en' => "Related Product {$i}"],
                'slug' => ['en' => "related-product-{$i}"],
                'is_active' => true,
            ]);

            ProductVariant::factory()->create([
                'product_id' => $relatedProduct->id,
                'price' => 50.00 + $i,
            ]);
        }

        $startTime = microtime(true);
        $response = $this->get(route('products.show', 'test-product'));
        $endTime = microtime(true);

        $response->assertStatus(200);

        // Ensure page loads within reasonable time (less than 1 second)
        $this->assertLessThan(1.0, $endTime - $startTime);
    }
}
