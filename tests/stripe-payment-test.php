<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🔧 Stripe Payment Page Test\n";
echo "===========================\n\n";

try {
    // Test 1: Check if we can find an order
    echo "1. Testing Order Lookup...\n";
    $order = \App\Models\Order::with(['billingAddress', 'shippingAddress', 'user', 'payments'])
        ->where('status', 'pending')
        ->first();
    
    if (!$order) {
        echo "⚠️  No pending orders found. Creating a test order...\n";
        
        // Create a test user if needed
        $user = \App\Models\User::first();
        if (!$user) {
            echo "❌ No users found. Please create a user first.\n";
            exit(1);
        }
        
        // Create test addresses
        $billingAddress = \App\Models\Address::create([
            'address_line1' => '123 Test Street',
            'city' => 'Test City',
            'region' => 'Test State',
            'postal_code' => '12345',
            'country' => 'US',
        ]);
        
        $shippingAddress = \App\Models\Address::create([
            'address_line1' => '456 Ship Street',
            'city' => 'Ship City',
            'region' => 'Ship State',
            'postal_code' => '67890',
            'country' => 'US',
        ]);
        
        // Create test order
        $order = \App\Models\Order::create([
            'user_id' => $user->id,
            'order_number' => 'TEST-' . time(),
            'billing_address_id' => $billingAddress->id,
            'shipping_address_id' => $shippingAddress->id,
            'customer_email' => $user->email,
            'customer_name' => $user->name,
            'status' => 'pending',
            'subtotal' => 100.00,
            'shipping_cost' => 10.00,
            'tax_amount' => 8.50,
            'discount_amount' => 0.00,
            'total' => 118.50,
            'currency' => 'USD',
            'shipping_method' => 'standard',
        ]);
        
        // Create a payment record
        \App\Models\Payment::create([
            'order_id' => $order->id,
            'payment_method' => 'stripe',
            'amount' => $order->total,
            'currency' => 'USD',
            'status' => 'pending',
        ]);
        
        // Reload with relationships
        $order = \App\Models\Order::with(['billingAddress', 'shippingAddress', 'user', 'payments'])
            ->find($order->id);
    }
    
    echo "✅ Order found: {$order->order_number}\n";
    echo "   Customer: {$order->customer_name}\n";
    echo "   Email: {$order->customer_email}\n";
    echo "   Total: {$order->currency} {$order->total}\n\n";
    
    // Test 2: Check Stripe configuration
    echo "2. Testing Stripe Configuration...\n";
    $stripeKey = config('services.stripe.key');
    $stripeSecret = config('services.stripe.secret');
    
    if (!$stripeKey) {
        echo "❌ Stripe public key not configured\n";
        exit(1);
    }
    
    if (!$stripeSecret) {
        echo "❌ Stripe secret key not configured\n";
        exit(1);
    }
    
    echo "✅ Stripe public key: " . substr($stripeKey, 0, 20) . "...\n";
    echo "✅ Stripe secret key: " . substr($stripeSecret, 0, 20) . "...\n\n";
    
    // Test 3: Test PaymentService
    echo "3. Testing PaymentService...\n";
    $paymentService = app(\App\Services\PaymentService::class);
    $result = $paymentService->createStripePaymentIntent($order);
    
    if (!$result['success']) {
        echo "❌ Failed to create payment intent: " . $result['message'] . "\n";
        exit(1);
    }
    
    echo "✅ Payment intent created successfully\n";
    echo "   Client Secret: " . substr($result['client_secret'], 0, 20) . "...\n\n";
    
    // Test 4: Test Controller Method
    echo "4. Testing Controller Method...\n";
    $controller = new \App\Http\Controllers\CheckoutController(
        app(\App\Services\CartService::class),
        app(\App\Services\OrderService::class),
        app(\App\Services\PaymentService::class),
        app(\App\Services\InventoryService::class),
        app(\App\Services\ShippingService::class),
        app(\App\Repositories\CheckoutRepository::class)
    );
    
    // Create a mock request
    $request = Request::create('/checkout/' . $order->order_number . '/payment/stripe', 'GET');
    $request->setUserResolver(function () use ($order) {
        return $order->user;
    });
    
    try {
        $response = $controller->showPayment($request, $order->order_number, 'stripe');
        
        if ($response instanceof \Illuminate\View\View) {
            echo "✅ Controller returned view successfully\n";
            echo "   View: {$response->name()}\n";
            
            $data = $response->getData();
            echo "   Order in view: " . ($data['order'] ? 'Yes' : 'No') . "\n";
            echo "   Client Secret in view: " . ($data['clientSecret'] ? 'Yes' : 'No') . "\n";
            echo "   Public Key in view: " . ($data['publicKey'] ? 'Yes' : 'No') . "\n";
        } else {
            echo "❌ Controller returned redirect instead of view\n";
        }
    } catch (\Exception $e) {
        echo "❌ Controller error: " . $e->getMessage() . "\n";
    }
    
    echo "\n📋 Test Summary\n";
    echo "================\n";
    echo "✅ Order lookup working\n";
    echo "✅ Stripe configuration valid\n";
    echo "✅ Payment intent creation working\n";
    echo "✅ Controller method working\n\n";
    
    echo "🎯 Next Steps:\n";
    echo "1. Visit: http://localhost:8000/checkout/{$order->order_number}/payment/stripe\n";
    echo "2. Check browser console for JavaScript errors\n";
    echo "3. Test with card: 4242 4242 4242 4242\n";
    
} catch (\Exception $e) {
    echo "❌ Test failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
