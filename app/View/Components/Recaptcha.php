<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;
use Illuminate\Support\Facades\Config;

class Recaptcha extends Component
{
    /**
     * The form ID that this reCAPTCHA belongs to.
     *
     * @var string
     */
    public $formId;

    /**
     * The reCAPTCHA site key.
     *
     * @var string
     */
    public $siteKey;

    /**
     * The reCAPTCHA version (v2, invisible, v3).
     *
     * @var string
     */
    public $version;

    /**
     * Whether to show the badge.
     *
     * @var bool
     */
    public $showBadge;

    /**
     * The badge position (bottomright, bottomleft, inline).
     *
     * @var string
     */
    public $badgePosition;

    /**
     * Create a new component instance.
     *
     * @param string $formId The ID of the form this reCAPTCHA belongs to
     * @param string|null $siteKey The reCAPTCHA site key (uses config if not provided)
     * @param string $version The reCAPTCHA version (v2, invisible, v3)
     * @param bool $showBadge Whether to show the badge
     * @param string $badgePosition The position of the badge (bottomright, bottomleft, inline)
     * @return void
     */
    public function __construct(
        string $formId,
        ?string $siteKey = null,
        string $version = 'invisible',
        bool $showBadge = true,
        string $badgePosition = 'bottomright'
    ) {
        $this->formId = $formId;
        $this->siteKey = $siteKey ?? config('captcha.sitekey');
        $this->version = in_array($version, ['v2', 'invisible', 'v3']) ? $version : 'invisible';
        $this->showBadge = $showBadge;
        $this->badgePosition = in_array($badgePosition, ['bottomright', 'bottomleft', 'inline']) 
            ? $badgePosition 
            : 'bottomright';
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.recaptcha', [
            'siteKey' => $this->siteKey,
            'version' => $this->version,
            'showBadge' => $this->showBadge,
            'badgePosition' => $this->badgePosition,
        ]);
    }
}
