<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class EmailPreference extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'email',
        'order_confirmation',
        'payment_confirmed',
        'payment_failed',
        'order_processing',
        'order_shipped',
        'order_delivered',
        'order_cancelled',
        'marketing_emails',
        'unsubscribe_token',
        'unsubscribed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'order_confirmation' => 'boolean',
        'payment_confirmed' => 'boolean',
        'payment_failed' => 'boolean',
        'order_processing' => 'boolean',
        'order_shipped' => 'boolean',
        'order_delivered' => 'boolean',
        'order_cancelled' => 'boolean',
        'marketing_emails' => 'boolean',
        'unsubscribed_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->unsubscribe_token)) {
                $model->unsubscribe_token = Str::random(64);
            }
        });
    }

    /**
     * Get or create email preferences for an email address.
     */
    public static function getOrCreateForEmail(string $email): self
    {
        return static::firstOrCreate(
            ['email' => $email],
            ['unsubscribe_token' => Str::random(64)]
        );
    }

    /**
     * Check if the user wants to receive a specific type of email.
     */
    public function wantsEmail(string $type): bool
    {
        return $this->{$type} ?? false;
    }

    /**
     * Unsubscribe from all emails.
     */
    public function unsubscribeFromAll(): void
    {
        $this->update([
            'order_confirmation' => false,
            'payment_confirmed' => false,
            'payment_failed' => false,
            'order_processing' => false,
            'order_shipped' => false,
            'order_delivered' => false,
            'order_cancelled' => false,
            'marketing_emails' => false,
            'unsubscribed_at' => now(),
        ]);
    }

    /**
     * Check if user is unsubscribed from all emails.
     */
    public function isUnsubscribedFromAll(): bool
    {
        return !$this->order_confirmation &&
               !$this->payment_confirmed &&
               !$this->payment_failed &&
               !$this->order_processing &&
               !$this->order_shipped &&
               !$this->order_delivered &&
               !$this->order_cancelled &&
               !$this->marketing_emails;
    }

    /**
     * Get the unsubscribe URL for this email.
     */
    public function getUnsubscribeUrl(): string
    {
        return route('email.unsubscribe', ['token' => $this->unsubscribe_token]);
    }

    /**
     * Get the email preferences URL for this email.
     */
    public function getPreferencesUrl(): string
    {
        return route('email.preferences', ['token' => $this->unsubscribe_token]);
    }
}
