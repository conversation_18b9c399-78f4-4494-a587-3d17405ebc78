<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

/**
 * App\Models\Setting
 *
 * @property string $id
 * @property string|null $key
 * @property string|null $value
 * @property string $category
 * @property string $type
 * @property array<array-key, mixed>|null $options
 * @property bool $is_public
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @mixin \Illuminate\Database\Eloquent\Builder
 * @method static mixed get(string $key, mixed $default = null) Alias for getValue
 * @method static bool set(string $key, mixed $value, string $category = 'general', string $type = 'text', array $options = [], bool $isPublic = true) Alias for setValue
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereIsPublic($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereValue($value)
 * @mixin \Eloquent
 */
class Setting extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'key',
        'value',
        'category',
        'type',
        'options',
        'is_public',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'options' => 'array',
        'is_public' => 'boolean',
    ];

    /**
     * Get a setting value by key.
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getValue(string $key, $default = null)
    {
        $setting = self::where('key', $key)->first();

        if (!$setting) {
            return $default;
        }

        // Handle type casting
        switch ($setting->type) {
            case 'boolean':
            case 'bool':
                // Handle boolean values more reliably
                if (in_array(strtolower($setting->value), ['true', '1', 'yes', 'on'], true)) {
                    return true;
                } elseif (in_array(strtolower($setting->value), ['false', '0', 'no', 'off', ''], true)) {
                    return false;
                } else {
                    return filter_var($setting->value, FILTER_VALIDATE_BOOLEAN);
                }
            case 'integer':
            case 'int':
                return (int) $setting->value;
            case 'float':
            case 'double':
                return (float) $setting->value;
            case 'array':
            case 'json':
                return json_decode($setting->value, true) ?? $default;
            default:
                return $setting->value;
        }
    }

    /**
     * Set a setting value by key.
     *
     * @param string $key
     * @param mixed $value
     * @param string $category
     * @param string $type
     * @param array $options
     * @param bool $isPublic
     * @return Setting
     */
    public static function setValue(string $key, $value, string $category = 'general', string $type = 'text', array $options = [], bool $isPublic = true)
    {
        // Convert value to string for storage
        if (is_array($value) || is_object($value)) {
            $storedValue = json_encode($value);
        } elseif ($type === 'boolean' || $type === 'bool') {
            // Handle boolean values explicitly
            if (is_bool($value)) {
                $storedValue = $value ? 'true' : 'false';
            } elseif (in_array(strtolower((string) $value), ['true', '1', 'yes', 'on'], true)) {
                $storedValue = 'true';
            } else {
                $storedValue = 'false';
            }
        } else {
            $storedValue = (string) $value;
        }

        return self::updateOrCreate(
            ['key' => $key],
            [
                'value' => $storedValue,
                'category' => $category,
                'type' => $type,
                'options' => $options,
                'is_public' => $isPublic,
            ]
        );
    }

    /**
     * Get all settings as an associative array
     *
     * @return array
     */
    public static function allAsArray(): array
    {
        return self::all()
            ->mapWithKeys(fn($setting) => [$setting->key => $setting->value])
            ->toArray();
    }

    /**
     * Get all settings for a specific category
     *
     * @param string $category
     * @return array
     */
    public static function getByCategory(string $category): array
    {
        return self::where('category', $category)
            ->get()
            ->mapWithKeys(fn($setting) => [$setting->key => $setting->value])
            ->toArray();
    }

    /**
     * Get a setting value (alias for getValue for backward compatibility)
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        return self::getValue($key, $default);
    }

    /**
     * Set a setting value (alias for setValue for backward compatibility)
     *
     * @param string $key
     * @param mixed $value
     * @param string $category
     * @param string $type
     * @param array $options
     * @param bool $isPublic
     * @return Setting
     */
    public static function set(string $key, $value, string $category = 'general', string $type = 'text', array $options = [], bool $isPublic = true)
    {
        return self::setValue($key, $value, $category, $type, $options, $isPublic);
    }
}
