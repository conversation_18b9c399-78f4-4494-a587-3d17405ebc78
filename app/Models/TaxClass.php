<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\TaxClass
 *
 * @property int $id
 * @property string|null $country
 * @property string|null $state
 * @property string|null $zip
 * @property string|null $city
 * @property float $rate
 * @property string|null $name
 * @property bool $is_global
 * @property int|null $priority
 * @property bool $on_shipping
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Product[] $products
 * @property-read int|null $products_count
 * @method static \Illuminate\Database\Eloquent\Builder|TaxClass newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaxClass newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaxClass query()
 * @method static \Illuminate\Database\Eloquent\Builder|TaxClass whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxClass whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxClass whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxClass whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxClass whereIsGlobal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxClass whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxClass whereOnShipping($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxClass wherePriority($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxClass whereRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxClass whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxClass whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxClass whereZip($value)
 * @mixin \Eloquent
 */
class TaxClass extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'tax_classes';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'country',
        'state',
        'zip',
        'city',
        'rate',
        'name',
        'is_global',
        'priority',
        'on_shipping',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'rate' => 'float',
        'is_global' => 'boolean',
        'priority' => 'integer',
        'on_shipping' => 'boolean',
    ];

    /**
     * Get the products that use this tax class.
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'tax_class_id');
    }

    /**
     * Scope a query to only include global tax classes.
     */
    public function scopeGlobal($query)
    {
        return $query->where('is_global', true);
    }

    /**
     * Scope a query to only include tax classes for a specific location.
     */
    public function scopeForLocation($query, $country = null, $state = null, $city = null, $zip = null)
    {
        return $query->where(function ($q) use ($country, $state, $city, $zip) {
            $q->where('is_global', true)
              ->orWhere(function ($subQ) use ($country, $state, $city, $zip) {
                  if ($country) {
                      $subQ->where('country', $country);
                  }
                  if ($state) {
                      $subQ->where('state', $state);
                  }
                  if ($city) {
                      $subQ->where('city', $city);
                  }
                  if ($zip) {
                      $subQ->where('zip', $zip);
                  }
              });
        });
    }

    /**
     * Get the applicable tax rate for a given location.
     */
    public static function getApplicableRate($country = null, $state = null, $city = null, $zip = null, $includeShipping = false)
    {
        $query = static::forLocation($country, $state, $city, $zip);
        
        if ($includeShipping) {
            $query->where('on_shipping', true);
        }
        
        // Order by priority (higher priority first) and specificity
        $taxClass = $query->orderByDesc('priority')
                         ->orderByDesc(function ($q) {
                             // More specific locations have higher priority
                             return $q->selectRaw('
                                 CASE 
                                     WHEN country IS NOT NULL AND state IS NOT NULL AND city IS NOT NULL AND zip IS NOT NULL THEN 4
                                     WHEN country IS NOT NULL AND state IS NOT NULL AND city IS NOT NULL THEN 3
                                     WHEN country IS NOT NULL AND state IS NOT NULL THEN 2
                                     WHEN country IS NOT NULL THEN 1
                                     ELSE 0
                                 END
                             ');
                         })
                         ->first();

        return $taxClass ? $taxClass->rate : 0;
    }

    /**
     * Check if this tax class applies to shipping.
     */
    public function appliesToShipping(): bool
    {
        return $this->on_shipping;
    }

    /**
     * Get a formatted display name for the tax class.
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->name) {
            return $this->name;
        }

        $parts = array_filter([
            $this->city,
            $this->state,
            $this->country,
            $this->zip
        ]);

        if (empty($parts)) {
            return $this->is_global ? 'Global Tax' : 'Tax Class';
        }

        return implode(', ', $parts) . ' (' . $this->rate . '%)';
    }
}
