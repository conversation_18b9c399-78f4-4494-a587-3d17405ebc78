<?php

namespace App\Services;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\User;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;

class CartEventService
{
    /**
     * Track cart event for analytics.
     */
    public function trackCartEvent(string $event, Cart $cart, array $data = []): void
    {
        $eventData = [
            'event' => $event,
            'cart_id' => $cart->id,
            'user_id' => $cart->user_id,
            'session_id' => $cart->session_id,
            'cart_value' => $this->calculateCartValue($cart),
            'item_count' => $cart->items->count(),
            'timestamp' => now()->toISOString(),
            ...$data
        ];

        // Dispatch Laravel event
        Event::dispatch('cart.' . $event, [$cart, $eventData]);

        // Log for analytics
        Log::channel('analytics')->info("Cart event: {$event}", $eventData);

        // Track in analytics service (if available)
        if (class_exists('\App\Services\AnalyticsService')) {
            app(\App\Services\AnalyticsService::class)->track(
                $cart->user_id ?? $cart->session_id,
                $event,
                $eventData
            );
        }
    }

    /**
     * Track item added to cart.
     */
    public function trackItemAdded(Cart $cart, CartItem $item): void
    {
        $this->trackCartEvent('item_added', $cart, [
            'item_id' => $item->id,
            'product_id' => $item->productVariant->product_id,
            'variant_id' => $item->product_variant_id,
            'product_name' => $item->productVariant->product->getTranslation('name', app()->getLocale()),
            'variant_name' => $item->productVariant->getTranslation('name', app()->getLocale()),
            'quantity' => $item->quantity,
            'unit_price' => $item->unit_price,
            'item_value' => $item->quantity * $item->unit_price,
            'category_id' => $item->productVariant->product->category_id
        ]);
    }

    /**
     * Track item removed from cart.
     */
    public function trackItemRemoved(Cart $cart, CartItem $item): void
    {
        $this->trackCartEvent('item_removed', $cart, [
            'item_id' => $item->id,
            'product_id' => $item->productVariant->product_id,
            'variant_id' => $item->product_variant_id,
            'product_name' => $item->productVariant->product->getTranslation('name', app()->getLocale()),
            'quantity_removed' => $item->quantity,
            'value_removed' => $item->quantity * $item->unit_price
        ]);
    }

    /**
     * Track item quantity updated.
     */
    public function trackItemUpdated(Cart $cart, CartItem $item, int $oldQuantity): void
    {
        $quantityChange = $item->quantity - $oldQuantity;
        $valueChange = $quantityChange * $item->unit_price;

        $this->trackCartEvent('item_updated', $cart, [
            'item_id' => $item->id,
            'product_id' => $item->productVariant->product_id,
            'variant_id' => $item->product_variant_id,
            'old_quantity' => $oldQuantity,
            'new_quantity' => $item->quantity,
            'quantity_change' => $quantityChange,
            'value_change' => $valueChange,
            'action' => $quantityChange > 0 ? 'increased' : 'decreased'
        ]);
    }

    /**
     * Track cart viewed.
     */
    public function trackCartViewed(Cart $cart): void
    {
        $this->trackCartEvent('cart_viewed', $cart, [
            'page' => 'cart',
            'items_in_cart' => $cart->items->count(),
            'categories_in_cart' => $cart->items->map(function ($item) {
                return $item->productVariant->product->category_id;
            })->unique()->count()
        ]);
    }

    /**
     * Track cart abandonment.
     */
    public function trackCartAbandoned(Cart $cart, string $reason = 'unknown'): void
    {
        $this->trackCartEvent('cart_abandoned', $cart, [
            'reason' => $reason,
            'time_since_last_update' => now()->diffInMinutes($cart->updated_at),
            'checkout_attempts' => $this->getCheckoutAttempts($cart),
            'abandonment_stage' => $this->getAbandonmentStage($cart)
        ]);
    }

    /**
     * Track cart cleared.
     */
    public function trackCartCleared(Cart $cart): void
    {
        $itemsData = $cart->items->map(function ($item) {
            return [
                'product_id' => $item->productVariant->product_id,
                'quantity' => $item->quantity,
                'value' => $item->quantity * $item->unit_price
            ];
        })->toArray();

        $this->trackCartEvent('cart_cleared', $cart, [
            'items_cleared' => $cart->items->count(),
            'value_cleared' => $this->calculateCartValue($cart),
            'items_data' => $itemsData
        ]);
    }

    /**
     * Track cart converted to order.
     */
    public function trackCartConverted(Cart $cart, string $orderId): void
    {
        $this->trackCartEvent('cart_converted', $cart, [
            'order_id' => $orderId,
            'conversion_time' => now()->diffInMinutes($cart->created_at),
            'items_purchased' => $cart->items->count(),
            'order_value' => $this->calculateCartValue($cart)
        ]);
    }

    /**
     * Track cart validation issues.
     */
    public function trackValidationIssues(Cart $cart, array $issues): void
    {
        $issuesSummary = [];
        foreach ($issues as $issue) {
            $type = $issue['type'];
            if (!isset($issuesSummary[$type])) {
                $issuesSummary[$type] = 0;
            }
            $issuesSummary[$type]++;
        }

        $this->trackCartEvent('cart_validation_issues', $cart, [
            'total_issues' => count($issues),
            'issues_by_type' => $issuesSummary,
            'issues_details' => $issues
        ]);
    }

    /**
     * Track cart recommendations shown.
     */
    public function trackRecommendationsShown(Cart $cart, array $recommendations): void
    {
        $this->trackCartEvent('recommendations_shown', $cart, [
            'recommendations_count' => count($recommendations),
            'recommended_products' => array_column($recommendations, 'id'),
            'recommendation_context' => 'cart_page'
        ]);
    }

    /**
     * Track recommendation clicked.
     */
    public function trackRecommendationClicked(Cart $cart, string $productId): void
    {
        $this->trackCartEvent('recommendation_clicked', $cart, [
            'recommended_product_id' => $productId,
            'context' => 'cart_page'
        ]);
    }

    /**
     * Calculate total cart value.
     */
    private function calculateCartValue(Cart $cart): float
    {
        return $cart->items->sum(function ($item) {
            return $item->quantity * $item->unit_price;
        });
    }

    /**
     * Get number of checkout attempts for this cart.
     */
    private function getCheckoutAttempts(Cart $cart): int
    {
        // This would require tracking checkout attempts in the database
        // For now, return 0 as placeholder
        return 0;
    }

    /**
     * Determine abandonment stage.
     */
    private function getAbandonmentStage(Cart $cart): string
    {
        // Determine where in the process the cart was abandoned
        // This could be enhanced with more sophisticated tracking
        
        if ($cart->items->isEmpty()) {
            return 'empty_cart';
        }

        if ($cart->updated_at < now()->subHours(24)) {
            return 'long_term_abandonment';
        }

        if ($cart->updated_at < now()->subHours(1)) {
            return 'short_term_abandonment';
        }

        return 'recent_activity';
    }

    /**
     * Get cart analytics summary.
     */
    public function getCartAnalytics(Cart $cart): array
    {
        return [
            'cart_age' => now()->diffInMinutes($cart->created_at),
            'last_activity' => now()->diffInMinutes($cart->updated_at),
            'total_items' => $cart->items->count(),
            'total_value' => $this->calculateCartValue($cart),
            'average_item_value' => $cart->items->count() > 0 
                ? $this->calculateCartValue($cart) / $cart->items->count() 
                : 0,
            'unique_products' => $cart->items->map(function ($item) {
                return $item->productVariant->product_id;
            })->unique()->count(),
            'categories_represented' => $cart->items->map(function ($item) {
                return $item->productVariant->product->category_id;
            })->unique()->count()
        ];
    }

    /**
     * Track cart performance metrics.
     */
    public function trackPerformanceMetrics(string $operation, float $executionTime, array $context = []): void
    {
        Log::channel('performance')->info("Cart operation performance", [
            'operation' => $operation,
            'execution_time_ms' => $executionTime * 1000,
            'context' => $context,
            'timestamp' => now()->toISOString()
        ]);
    }
}
