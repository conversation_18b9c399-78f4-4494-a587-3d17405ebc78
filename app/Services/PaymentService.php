<?php

namespace App\Services;

use App\Contracts\PaymentGateway;
use App\Events\PaymentConfirmed;
use App\Events\PaymentFailed;
use App\Models\Order;
use App\Models\Payment;
use App\Services\Gateways\PaymentGatewayFactory;
use App\Services\Gateways\SrmklivePayPalGateway; // For type hinting
use Illuminate\Database\Eloquent\Collection;
use GuzzleHttp\Exception\ConnectException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PaymentService extends BaseService
{
    /**
     * Maximum number of retry attempts for payment operations.
     */
    protected const MAX_RETRY_ATTEMPTS = 3;
    /**
     * Create a new service instance.
     */
    public function __construct(Payment $payment)
    {
        $this->model = $payment;
    }

    /**
     * Get payments by order.
     */
    public function getPaymentsByOrder(string $orderId): Collection
    {
        return $this->model->where('order_id', $orderId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get payment by its primary transaction ID (e.g., Stripe Charge ID, PayPal Capture ID).
     */
    public function getPaymentByGatewayTransactionId(string $gatewayTransactionId, ?string $paymentMethod = null): ?Payment
    {
        $query = $this->model->where('gateway_transaction_id', $gatewayTransactionId);
        if ($paymentMethod) {
            $query->where('payment_method', $paymentMethod);
        }
        return $query->first();
    }

    /**
     * Get payment by its gateway's order/intent ID (e.g., PayPal Order ID, Stripe Payment Intent ID).
     */
    public function getPaymentByGatewayPaymentId(string $gatewayPaymentId, ?string $paymentMethod = null): ?Payment
    {
        $query = $this->model->where('gateway_payment_id', $gatewayPaymentId);
        if ($paymentMethod) {
            $query->where('payment_method', $paymentMethod);
        }
        return $query->latest()->first(); // Get the latest in case of retries creating multiple pending
    }


    public function createPayment(array $data): Payment
    {
        // Ensure metadata is an array
        if (isset($data['metadata']) && !is_array($data['metadata'])) {
            $data['metadata'] = (array) $data['metadata'];
        } elseif (!isset($data['metadata'])) {
            $data['metadata'] = [];
        }
        // Merge payment_details into metadata if it exists, then remove payment_details
        if (isset($data['payment_details'])) {
            $data['metadata'] = array_merge($data['metadata'], (array) $data['payment_details']);
            unset($data['payment_details']);
        }

        return $this->create($data);
    }

    /**
     * Update payment status.
     */

    public function updatePaymentStatus(Payment $payment, string $status, array $metadataUpdates = null): Payment
    {
        $previousStatus = $payment->status;
        $data = ['status' => $status];

        if ($metadataUpdates !== null) {
            // Ensure metadata is an array and merge
            $currentMetadata = is_array($payment->metadata) ? $payment->metadata : (array) $payment->metadata;
            $data['metadata'] = array_merge($currentMetadata, $metadataUpdates);
        }

        $payment = $this->update($payment, $data);

        if ($status === 'completed' && $payment->order && $payment->order->status === 'pending') {
            $this->updateOrderStatusAfterPayment($payment->order);
        } elseif (in_array($status, ['failed', 'cancelled', 'reversed']) && $payment->order && $payment->order->status === 'pending') {
             // More specific order status updates based on payment failure type can be added here
            if ($payment->order->status !== 'payment_failed') { // Avoid redundant updates
                $payment->order->update(['status' => 'payment_failed']);
            }
        }

        // Dispatch payment events if status changed
        if ($previousStatus !== $status && $payment->order) {
            if ($status === 'completed') {
                PaymentConfirmed::dispatch($payment->order, $payment);
            } elseif (in_array($status, ['failed', 'cancelled', 'reversed'])) {
                $reason = $metadataUpdates['error_message'] ?? $metadataUpdates['failure_reason'] ?? null;
                PaymentFailed::dispatch($payment->order, $payment, $reason);
            }
        }

        return $payment;
    }

    /**
     * Update payment with the given data.
     */

    public function updatePayment(Payment $payment, array $data): Payment
    {
        // Ensure metadata is an array if provided
        if (isset($data['metadata']) && !is_array($data['metadata'])) {
            $data['metadata'] = (array) $data['metadata'];
        }
         // Merge payment_details into metadata if it exists, then remove payment_details
        if (isset($data['payment_details'])) {
            $newMetadata = isset($data['metadata']) ? $data['metadata'] : (is_array($payment->metadata) ? $payment->metadata : (array) $payment->metadata);
            $data['metadata'] = array_merge($newMetadata, (array) $data['payment_details']);
            unset($data['payment_details']);
        }

        return $this->update($payment, $data);
    }

    /**
     * Process payment.
     *
     * This method handles all payment processing, including cases where
     * additional actions are required (like 3D Secure authentication).
     * 
     * @param Order $order The order to process payment for
     * @param string $paymentMethod The payment method to use
     * @param array $paymentData Payment data including optional idempotency_key
     * @return Payment|null The processed payment or null on failure
     * @throws \Exception If payment processing fails
     */

    public function processPayment(Order $order, string $paymentMethod, array $paymentData): ?Payment
    {
        $idempotencyKey = $paymentData['idempotency_key'] ?? (string) Str::uuid();
        $existingPayment = $this->model->where('idempotency_key', $idempotencyKey)->first();
        if ($existingPayment) {
            Log::info('Found existing payment with the same idempotency key', ['idempotency_key' => $idempotencyKey, 'payment_id' => $existingPayment->id]);
            return $existingPayment;
        }

        return DB::transaction(function () use ($order, $paymentMethod, $paymentData, $idempotencyKey) {
            $payment = $this->createPayment([
                'order_id' => $order->id,
                'payment_method' => $paymentMethod,
                // gateway_payment_id will be set by the gateway during createPaymentIntent
                // gateway_transaction_id will be set by the gateway during executePayment/capture
                'status' => 'pending', // Initial status
                'amount' => $order->total,
                'currency' => $order->currency,
                'metadata' => ['initial_payment_data' => $paymentData], // Store initial data in metadata
                'idempotency_key' => $idempotencyKey,
            ]);

            $gateway = $this->getGateway($paymentMethod);
            $result = [];

            try {
                if ($paymentMethod === 'stripe') {
                    // For Stripe, createPaymentIntent is usually called from frontend/controller to get client_secret.
                    // Here, we'd be confirming it if payment_method_id is provided.
                    // This part of processPayment might need adjustment based on your Stripe flow.
                    // If createStripePaymentIntent was already called and you have a payment_intent_id:
                    if (isset($paymentData['payment_intent_id'])) {
                         $payment->gateway_payment_id = $paymentData['payment_intent_id'];
                         // Potentially confirm payment here or rely on webhook
                         $verifyResult = $gateway->verifyPayment($paymentData['payment_intent_id']);
                         $result = array_merge($result, $verifyResult);
                         $payment->status = $verifyResult['status'] ?? 'pending'; // map gateway status
                    } else {
                        // This assumes processStripePayment handles intent creation and confirmation
                        $result = $this->processStripePayment($order, $paymentData, $payment); // Pass payment to update
                    }
                } elseif ($paymentMethod === 'paypal') {
                    // PayPal's createPaymentIntent (createOrder) is called first, then executePayment.
                    // This method assumes executePayment is being called.
                    if (isset($paymentData['paypal_order_id'])) { // This is gateway_payment_id
                        $payment->gateway_payment_id = $paymentData['paypal_order_id'];
                        $payment->save(); // Save gateway_payment_id
                        $result = $gateway->executePayment($paymentData['paypal_order_id'], $paymentData);
                    } else {
                        throw new \Exception("PayPal Order ID is required to process PayPal payment.");
                    }
                } elseif ($paymentMethod === 'bank_transfer') {
                    $result = $gateway->createPaymentIntent($order, $paymentData); // This will set payment to pending
                    $payment->status = 'pending'; // Bank transfers start as pending
                } else {
                    throw new \Exception("Unsupported payment method: {$paymentMethod}");
                }

                // Update payment based on gateway result
                if (isset($result['payment']) && $result['payment'] instanceof Payment) {
                    // If gateway returned the updated payment model (like SrmklivePayPalGateway does)
                    $payment = $result['payment'];
                } else {
                    // Update payment with details from result
                    $statusToSet = $payment->status; // Keep current status unless explicitly changed
                    if (isset($result['status'])) $statusToSet = $result['status']; // Gateway's status for our system
                    elseif ($result['success'] && !($result['requires_action'] ?? false) && $paymentMethod !== 'bank_transfer') $statusToSet = 'completed';
                    elseif ($result['success'] && ($result['requires_action'] ?? false)) $statusToSet = 'requires_action';
                    elseif (!$result['success']) $statusToSet = 'failed';

                    $metadataUpdates = $result['details'] ?? ($result['metadata'] ?? ($result['error'] ?? []));
                    if (isset($result['gateway_transaction_id'])) $payment->gateway_transaction_id = $result['gateway_transaction_id'];
                    if (isset($result['gateway_payment_id'])) $payment->gateway_payment_id = $result['gateway_payment_id'];

                    $this->updatePaymentStatus($payment, $statusToSet, is_array($metadataUpdates) ? $metadataUpdates : ['gateway_response' => $metadataUpdates]);
                }


            } catch (\Exception $e) {
                Log::error('Payment processing error in PaymentService: ' . $e->getMessage(), [
                    'order_id' => $order->id, 'payment_method' => $paymentMethod, 'exception_trace' => $e->getTraceAsString(),
                ]);
                $this->updatePaymentStatus($payment, 'failed', ['error' => $e->getMessage(), 'error_code' => 'payment_processing_exception']);
            }
            return $payment->fresh();
        });
    }


    /**
     * Process refund by calling the appropriate gateway.
     * This method updates the original payment record.
     */
    public function refundPayment(Payment $payment, ?float $amount = null, ?string $reason = null): array
    {
        if (!in_array($payment->status, ['completed', 'partially_refunded'])) {
             Log::warning('Attempted to refund payment not in completed or partially_refunded state.', ['payment_id' => $payment->id, 'status' => $payment->status]);
            return ['success' => false, 'message' => 'Payment cannot be refunded. Current status: ' . $payment->status];
        }

        $gateway = $this->getGateway($payment->payment_method);
        $result = $gateway->refundPayment($payment, $amount, $reason); // This should update $payment internally or return data to do so

        // The SrmklivePayPalGateway's refundPayment method already updates the payment model's status and metadata.
        // For Stripe or other gateways, you'd need to ensure similar behavior or handle the update here.
        // If the gateway's refundPayment method doesn't update the $payment object directly,
        // you would update it here based on $result.

        if ($result['success']) {
            // Assuming the gateway's refundPayment method has updated the $payment object (passed by reference or re-fetched)
            // or the $result contains enough info to update it.
            // SrmklivePayPalGateway updates the payment passed to it.
            Log::info('Refund processed successfully by gateway.', ['payment_id' => $payment->id, 'result' => $result]);
        } else {
            Log::error('Refund processing failed by gateway.', ['payment_id' => $payment->id, 'error' => $result['message'] ?? 'Unknown error']);
        }
        return $result; // Return the gateway's result
    }

    /**
     * Update order status after payment.
     */
    protected function updateOrderStatusAfterPayment(Order $order): void
    {
        if ($order->status === 'pending') {
            $order->update(['status' => 'processing']);
        }
    }

    /**
     * Process Stripe payment.
     */
    protected function processStripePayment(Order $order, array $paymentData): array
    {
        try {
            // Validate required data
            if (!isset($paymentData['payment_method_id'])) {
                throw new \Exception('Stripe payment method ID is required.');
            }

            // Get the Stripe gateway
            $gateway = $this->getGateway('stripe');

            // Create a payment intent with the payment method
            $result = $gateway->createPaymentIntent($order, [
                'payment_method' => $paymentData['payment_method_id'],
                'confirm' => true,
            ]);

            if (!$result['success']) {
                return [
                    'success' => false,
                    'details' => [
                        'error' => $result['message'] ?? 'Payment failed',
                        'error_code' => 'stripe_payment_error',
                    ],
                ];
            }

            // If payment requires further action, return that info
            if (isset($result['requires_action']) && $result['requires_action']) {
                return [
                    'success' => true,
                    'requires_action' => true,
                    'details' => [
                        'transaction_id' => $result['payment_intent_id'],
                        'payment_method' => 'stripe',
                        'payment_date' => now()->toIso8601String(),
                        'client_secret' => $result['client_secret'],
                        'stripe_response' => $result,
                    ],
                ];
            }

            // Payment succeeded
            return [
                'success' => true,
                'details' => [
                    'transaction_id' => $result['payment_intent_id'],
                    'payment_method' => 'stripe',
                    'payment_date' => now()->toIso8601String(),
                    'stripe_response' => $result,
                ],
            ];
        } catch (\Exception $e) {
            Log::error('Error processing Stripe payment', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'details' => [
                    'error' => $e->getMessage(),
                    'error_code' => 'stripe_payment_error',
                ],
            ];
        }
    }

    /**
     * Process PayPal payment.
     */
    protected function processPayPalPayment(Order $order, array $paymentData): array
    {
        try {
            // Validate required data
            if (!isset($paymentData['paypal_order_id'])) {
                throw new \Exception('PayPal order ID is required.');
            }

            // Get the PayPal gateway
            $gateway = $this->getGateway('paypal');

            // Execute the payment
            $result = $gateway->executePayment($paymentData['paypal_order_id'], [
                'payer_id' => $paymentData['payer_id'] ?? null,
            ]);

            if (!$result['success']) {
                return [
                    'success' => false,
                    'details' => [
                        'error' => $result['message'] ?? 'Payment failed',
                        'error_code' => 'paypal_payment_error',
                    ],
                ];
            }

            // Payment succeeded
            return [
                'success' => true,
                'details' => [
                    'transaction_id' => $result['payment_intent_id'] ?? $paymentData['paypal_order_id'],
                    'payment_method' => 'paypal',
                    'payment_date' => now()->toIso8601String(),
                    'paypal_response' => $result,
                ],
            ];
        } catch (\Exception $e) {
            Log::error('Error processing PayPal payment', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'details' => [
                    'error' => $e->getMessage(),
                    'error_code' => 'paypal_payment_error',
                ],
            ];
        }
    }

    /**
     * Process Stripe refund.
     */
    protected function processStripeRefund(Payment $payment, float $amount, string $reason = null): array
    {
        try {
            // Validate payment has a transaction ID
            if (!isset($payment->transaction_id)) {
                throw new \Exception('Payment has no transaction ID.');
            }

            // Get the Stripe gateway
            $gateway = $this->getGateway('stripe');

            // Process the refund
            $result = $gateway->refundPayment($payment, $amount, $reason);

            if (!$result['success']) {
                return [
                    'success' => false,
                    'details' => [
                        'error' => $result['message'] ?? 'Refund failed',
                        'error_code' => 'stripe_refund_error',
                    ],
                ];
            }

            // Refund succeeded
            return [
                'success' => true,
                'details' => [
                    'refund_id' => $result['refund_id'],
                    'refund_date' => now()->toIso8601String(),
                    'stripe_response' => $result,
                ],
            ];
        } catch (\Exception $e) {
            Log::error('Error processing Stripe refund', [
                'payment_id' => $payment->id,
                'amount' => $amount,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'details' => [
                    'error' => $e->getMessage(),
                    'error_code' => 'stripe_refund_error',
                ],
            ];
        }
    }

    /**
     * Process PayPal refund.
     */
    protected function processPayPalRefund(Payment $payment, float $amount, string $reason = null): array
    {
        try {
            // Validate payment has a transaction ID
            if (!isset($payment->transaction_id)) {
                throw new \Exception('Payment has no transaction ID.');
            }

            // Get the PayPal gateway
            $gateway = $this->getGateway('paypal');

            // Process the refund
            $result = $gateway->refundPayment($payment, $amount, $reason);

            if (!$result['success']) {
                return [
                    'success' => false,
                    'details' => [
                        'error' => $result['message'] ?? 'Refund failed',
                        'error_code' => 'paypal_refund_error',
                    ],
                ];
            }

            // Refund succeeded
            return [
                'success' => true,
                'details' => [
                    'refund_id' => $result['refund_id'],
                    'refund_date' => now()->toIso8601String(),
                    'paypal_response' => $result,
                ],
            ];
        } catch (\Exception $e) {
            Log::error('Error processing PayPal refund', [
                'payment_id' => $payment->id,
                'amount' => $amount,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'details' => [
                    'error' => $e->getMessage(),
                    'error_code' => 'paypal_refund_error',
                ],
            ];
        }
    }

    /**
     * Create a payment intent with Stripe.
     */
    public function createStripePaymentIntent(Order $order, array $data = [], bool $retry = true): array
    {
        try {
            // Get the Stripe gateway
            $gateway = $this->getGateway('stripe');

            // Create a payment intent
            return $gateway->createPaymentIntent($order, $data);
        } catch (\Exception $e) {
            Log::error('Error creating Stripe payment intent: ' . $e->getMessage(), ['order_id' => $order->id, 'exception_trace' => $e->getTraceAsString()]);

            // Retry if enabled and not already retrying
            if ($retry && $this->shouldRetry('stripe_intent_' . $order->id)) {
                Log::info('Retrying Stripe payment intent creation', [
                    'order_id' => $order->id,
                    'attempt' => $this->getRetryAttempt('stripe_intent_' . $order->id),
                ]);

                return $this->createStripePaymentIntent($order, $data, false);
            }

            return [
                'success' => false,
                'message' => 'An error occurred while creating the payment intent',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create a PayPal order.
     */
    public function createPayPalPayment(Order $order, string $returnUrl, string $cancelUrl, bool $retry = true): array
    {
        try {
            // Get the PayPal gateway
            $gateway = $this->getGateway('paypal');

            // Create a payment intent (PayPal order)
            return $gateway->createPaymentIntent($order, [
                'return_url' => $returnUrl,
                'cancel_url' => $cancelUrl,
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating PayPal payment: ' . $e->getMessage(), ['order_id' => $order->id, 'exception_trace' => $e->getTraceAsString()]);

            // Retry if enabled and not already retrying
            if ($retry && $this->shouldRetry('paypal_payment_' . $order->id)) {
                Log::info('Retrying PayPal payment creation', [
                    'order_id' => $order->id,
                    'attempt' => $this->getRetryAttempt('paypal_payment_' . $order->id),
                ]);

                return $this->createPayPalPayment($order, $returnUrl, $cancelUrl, false);
            }

            return [
                'success' => false,
                'message' => 'An error occurred while creating the PayPal payment',
                'error' => $e->getMessage(),
            ];
        }
    }
    /**
     * Execute a PayPal payment.
     *
     * This method captures a PayPal payment and handles connection loss recovery.
     * It includes robust error handling and retry mechanisms to ensure payments
     * are properly processed even if there are network issues.
     */
    public function executePayPalPayment(string $paymentId, string $payerId, bool $retry = true): array
    {
        try {
            // Get the PayPal gateway
            $gateway = $this->getGateway('paypal');

            // Check if payment already exists and is completed
            $payment = Payment::where('gateway_payment_id', $paymentId)->first();

            if ($payment && $payment->status === 'completed') {
                Log::info('PayPal payment already completed', [
                    'payment_id' => $paymentId,
                    'internal_payment_id' => $payment->id,
                ]);

                return [
                    'success' => true,
                    'message' => 'Payment already completed',
                    'payment' => $payment,
                ];
            }

            // Execute the payment
            $result = $gateway->executePayment($paymentId, [
                'payer_id' => $payerId,
            ]);

            // If successful, log the success
            if ($result['success']) {
                Log::info('PayPal payment executed successfully', [
                    'payment_id' => $paymentId,
                    'payer_id' => $payerId,
                    'internal_payment_id' => $result['payment']->id ?? null,
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            // Check if this is a network error or connection loss
            $isConnectionError = (
                strpos($e->getMessage(), 'cURL error') !== false ||
                strpos($e->getMessage(), 'Connection') !== false ||
                strpos($e->getMessage(), 'timeout') !== false ||
                $e instanceof \GuzzleHttp\Exception\ConnectException
            );

            Log::error('Error executing PayPal payment: ' . $e->getMessage(), ['payment_id' => $paymentId, 'exception_trace' => $e->getTraceAsString()]);

            // For connection errors, we should check if the payment was actually processed
            if ($isConnectionError) {
                try {
                    // Get the payment from our database
                    $payment = Payment::where('gateway_payment_id', $paymentId)->first();

                    if ($payment) {
                        // Check with PayPal if the order was actually captured
                        $verifyResult = $gateway->verifyPayment($paymentId);

                        if ($verifyResult['success'] && isset($verifyResult['status']) && $verifyResult['status'] === 'completed') {
                            Log::info('PayPal payment verified after connection error', [
                                'payment_id' => $paymentId,
                                'internal_payment_id' => $payment->id,
                            ]);

                            // Update the payment status if needed
                            if ($payment->status !== 'completed') {
                                $payment->update([
                                    'status' => 'completed',
                                    'processed_at' => now(),
                                    'metadata' => array_merge($payment->metadata ?? [], [
                                        'recovery_method' => 'connection_error_verification',
                                        'verification_result' => $verifyResult,
                                    ]),
                                ]);

                                // Update order status if needed
                                $order = $payment->order;
                                if ($order && $order->status === 'pending') {
                                    $order->update(['status' => 'processing']);
                                }
                            }

                            return [
                                'success' => true,
                                'message' => 'Payment verified after connection error',
                                'payment' => $payment,
                                'verification_result' => $verifyResult,
                            ];
                        }
                    }
                } catch (\Exception $verifyException) {
                    Log::error('Error verifying PayPal payment after connection error: ' . $verifyException->getMessage(), [
                        'payment_id' => $paymentId,
                        'exception' => $verifyException,
                    ]);
                }
            }

            // Retry if enabled and not already retrying
            if ($retry && $this->shouldRetry('paypal_execute_' . $paymentId)) {
                $attempt = $this->getRetryAttempt('paypal_execute_' . $paymentId);

                Log::info('Retrying PayPal payment execution', [
                    'payment_id' => $paymentId,
                    'payer_id' => $payerId,
                    'attempt' => $attempt,
                ]);

                // Add exponential backoff for retries
                $backoffSeconds = min(pow(2, $attempt - 1), 30); // Max 30 seconds
                if ($backoffSeconds > 0) {
                    sleep($backoffSeconds);
                }

                return $this->executePayPalPayment($paymentId, $payerId, false);
            }

            return [
                'success' => false,
                'message' => 'An error occurred while executing the PayPal payment',
                'error' => $e->getMessage(),
                'is_connection_error' => $isConnectionError,
            ];
        }
    }

    /**
     * Verify a Stripe payment.
     */
    public function verifyStripePayment(string $paymentIntentId): array
    {
        try {
            // Get the Stripe gateway
            $gateway = $this->getGateway('stripe');

            // Verify the payment
            return $gateway->verifyPayment($paymentIntentId);
        } catch (\Exception $e) {
            Log::error('Error verifying Stripe payment: ' . $e->getMessage(), [
                'payment_intent_id' => $paymentIntentId,
                'exception' => $e,
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while verifying the payment',
                'error' => $e->getMessage(),
            ];
        }
    }

    // --- WEBHOOK PROCESSING METHODS ---

    public function processPayPalWebhook(
        array $payloadArray,
        string $paypalTransmissionSig,
        string $paypalAuthAlgo,
        string $paypalCertUrl,
        string $paypalTransmissionId,
        string $paypalTransmissionTime
    ): array {
        try {
            /** @var SrmklivePayPalGateway $payPalGateway */
            $payPalGateway = $this->getGateway('paypal');

            $concatenatedHeaderValues = implode(',', [$paypalAuthAlgo, $paypalCertUrl, $paypalTransmissionId, $paypalTransmissionTime]);

            if (!$payPalGateway->validateWebhook($payloadArray, $paypalTransmissionSig, $concatenatedHeaderValues)) {
                Log::warning("PaymentService: PayPal webhook validation failed as per gateway.");
                return ['success' => false, 'message' => 'Webhook validation failed.'];
            }
            // The gateway's processWebhookEvent handles finding the payment and updating it.
            return $payPalGateway->processWebhookEvent($payloadArray);

        } catch (\Exception $e) {
            Log::error('PaymentService::processPayPalWebhook exception: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return ['success' => false, 'message' => 'Error processing PayPal webhook.', 'error_details' => $e->getMessage()];
        }
    }

    public function processStripeWebhook(string $rawPayload, string $stripeSignatureHeader): array
    {
        try {
            $stripeWebhookSecret = config('services.stripe.webhook_secret');
            if (empty($stripeWebhookSecret)) {
                Log::error('Stripe webhook secret is not configured.');
                return ['success' => false, 'message' => 'Stripe webhook secret not configured.'];
            }

            $event = \Stripe\Webhook::constructEvent($rawPayload, $stripeSignatureHeader, $stripeWebhookSecret);
            Log::info('Stripe webhook event constructed and verified.', ['event_id' => $event->id, 'event_type' => $event->type]);

            switch ($event->type) {
                case 'payment_intent.succeeded':
                    return $this->handleStripePaymentSucceededEvent($event->data->object);
                case 'payment_intent.payment_failed':
                    return $this->handleStripePaymentFailedEvent($event->data->object);
                case 'charge.refunded': // This event gives the Charge object
                    return $this->handleStripeChargeRefundedEvent($event->data->object);
                default:
                    Log::info("PaymentService: Unhandled Stripe event type: {$event->type}", ['event_id' => $event->id]);
                    return ['success' => true, 'message' => "Unhandled Stripe event type: {$event->type}"];
            }
        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            Log::error('PaymentService: Stripe webhook signature verification failed.', ['error' => $e->getMessage()]);
            return ['success' => false, 'message' => 'Stripe signature verification failed.'];
        } catch (\UnexpectedValueException $e) {
            Log::error('PaymentService: Stripe webhook invalid payload.', ['error' => $e->getMessage()]);
            return ['success' => false, 'message' => 'Invalid Stripe payload.'];
        } catch (\Exception $e) {
            Log::error('PaymentService::processStripeWebhook exception: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return ['success' => false, 'message' => 'Error processing Stripe webhook.', 'error_details' => $e->getMessage()];
        }
    }

    // --- Stripe Event Handlers ---

    protected function handleStripePaymentSucceededEvent(\Stripe\PaymentIntent $stripePaymentIntent): array
    {
        $payment = $this->getPaymentByGatewayPaymentId($stripePaymentIntent->id, 'stripe');

        if (!$payment) {
            Log::warning('Stripe payment_intent.succeeded webhook: No matching payment found.', ['payment_intent_id' => $stripePaymentIntent->id]);
            // Optionally, create a payment record if it's missing but you want to record the transaction.
            return ['success' => false, 'message' => 'Matching payment not found for succeeded intent.'];
        }

        if ($payment->status === 'completed') {
            Log::info('Stripe payment_intent.succeeded webhook: Payment already marked as completed.', ['payment_id' => $payment->id]);
            return ['success' => true, 'message' => 'Payment already completed.'];
        }

        $expectedAmountCents = (int) round($payment->amount * 100);
        if ($stripePaymentIntent->amount_received < $expectedAmountCents) {
            Log::error('Stripe payment_intent.succeeded webhook: Underpayment detected.', [
                'payment_id' => $payment->id, 'expected_cents' => $expectedAmountCents, 'received_cents' => $stripePaymentIntent->amount_received
            ]);
            $this->updatePaymentStatus($payment, 'failed', [
                'stripe_event_type' => 'payment_intent.succeeded', 'failure_reason' => 'underpayment_webhook',
                'expected_amount' => $payment->amount, 'captured_amount' => $stripePaymentIntent->amount_received / 100,
                'stripe_payment_intent_id' => $stripePaymentIntent->id, 'source' => 'webhook'
            ]);
            return ['success' => true, 'message' => 'Underpayment detected and payment marked as failed.']; // Success true as webhook is handled
        }
        // Handle overpayment if necessary
        if ($stripePaymentIntent->amount_received > $expectedAmountCents) {
             Log::warning('Stripe payment_intent.succeeded webhook: Overpayment detected.', [
                'payment_id' => $payment->id, 'expected_cents' => $expectedAmountCents, 'received_cents' => $stripePaymentIntent->amount_received
            ]);
             // Decide how to handle overpayment, for now, complete it and log
        }


        $this->updatePaymentStatus($payment, 'completed', [
            'gateway_transaction_id' => $stripePaymentIntent->latest_charge, // Store the charge ID as the transaction ID
            'stripe_event_type' => 'payment_intent.succeeded',
            'stripe_payment_intent_id' => $stripePaymentIntent->id,
            'captured_amount' => $stripePaymentIntent->amount_received / 100,
            'source' => 'webhook'
        ]);
        Log::info('PaymentService: Processed Stripe payment_intent.succeeded.', ['payment_id' => $payment->id]);
        return ['success' => true, 'message' => 'Stripe payment succeeded event processed.'];
    }

    protected function handleStripePaymentFailedEvent(\Stripe\PaymentIntent $stripePaymentIntent): array
    {
        $payment = $this->getPaymentByGatewayPaymentId($stripePaymentIntent->id, 'stripe');

        if (!$payment) {
            Log::warning('Stripe payment_intent.payment_failed webhook: No matching payment found.', ['payment_intent_id' => $stripePaymentIntent->id]);
            return ['success' => false, 'message' => 'Matching payment not found for failed intent.'];
        }
         if ($payment->status === 'failed') {
             Log::info('Stripe payment_intent.payment_failed: Payment already marked as failed.', ['payment_id' => $payment->id]);
            return ['success' => true, 'message' => 'Payment already failed.'];
        }

        $errorMessage = $stripePaymentIntent->last_payment_error ? $stripePaymentIntent->last_payment_error->message : 'Payment failed';
        $this->updatePaymentStatus($payment, 'failed', [
            'gateway_transaction_id' => $stripePaymentIntent->latest_charge,
            'stripe_event_type' => 'payment_intent.payment_failed',
            'error_message' => $errorMessage,
            'stripe_payment_intent_id' => $stripePaymentIntent->id,
            'source' => 'webhook'
        ]);
        Log::error('PaymentService: Processed Stripe payment_intent.payment_failed.', ['payment_id' => $payment->id, 'error' => $errorMessage]);
        return ['success' => true, 'message' => 'Stripe payment failed event processed.'];
    }

    protected function handleStripeChargeRefundedEvent(\Stripe\Charge $stripeCharge): array
    {
        // A charge can have multiple refunds. The event is for one specific refund occurrence.
        $payment = $this->getPaymentByGatewayPaymentId($stripeCharge->payment_intent, 'stripe');

        if (!$payment) {
            Log::warning('Stripe charge.refunded webhook: No matching payment found for payment_intent.', ['payment_intent_id' => $stripeCharge->payment_intent, 'charge_id' => $stripeCharge->id]);
            // Fallback: try finding by charge ID if you store that as gateway_transaction_id
            if ($stripeCharge->id) {
                $payment = $this->getPaymentByGatewayTransactionId($stripeCharge->id, 'stripe');
            }
            if (!$payment) {
                 Log::warning('Stripe charge.refunded webhook: No matching payment found for charge_id either.', ['charge_id' => $stripeCharge->id]);
                return ['success' => false, 'message' => 'Matching payment not found for refunded charge.'];
            }
        }

        $totalAmountRefundedOnPayment = (float) ($payment->metadata['total_refunded_amount'] ?? 0);
        $newlyRefundedAmount = 0;

        // Find the specific refund from the event if possible, or use charge's total refunded.
        // Stripe's charge.refunded event is for a specific refund.
        // The $stripeCharge object itself will have `amount_refunded` as total on this charge.
        // We need to find the *latest* refund amount if the webhook is for a single refund operation.
        // The $stripeCharge->refunds->data array contains all refund objects for this charge.
        // Let's assume the webhook is for the most recent refund on the charge.
        $latestStripeRefund = null;
        if ($stripeCharge->refunds && $stripeCharge->refunds->data) {
            // Find the refund that matches the event (often the latest one or by ID if available in event context)
            // For simplicity, if there's only one, use it. If multiple, this might need more specific logic
            // to identify WHICH refund this webhook event pertains to if not just the total.
            // However, charge.refunded usually means the charge's `amount_refunded` has been updated.
            // We should check if this specific refund has already been processed.
            $stripeRefundId = $stripeCharge->refunds->data[0]->id ?? null; // Example: get latest refund ID
            if ($stripeRefundId && isset($payment->metadata['processed_stripe_refund_ids']) && in_array($stripeRefundId, $payment->metadata['processed_stripe_refund_ids'])) {
                 Log::info('Stripe charge.refunded webhook: Refund ID already processed.', ['payment_id' => $payment->id, 'stripe_refund_id' => $stripeRefundId]);
                return ['success' => true, 'message' => 'Refund already processed.'];
            }
            // This is tricky: `amount_refunded` on charge is total. We need the amount of *this* specific refund event.
            // Often, the event payload itself (if Stripe sends the Refund object directly for `refund.updated` event)
            // would be more direct. For `charge.refunded`, we work with the charge.
            // Let's assume we are reconciling the total refunded amount on the charge.
            $newlyRefundedAmount = ($stripeCharge->amount_refunded / 100) - $totalAmountRefundedOnPayment;
        }


        if ($newlyRefundedAmount <= 0 && $stripeCharge->refunded) { // If total refunded on charge hasn't increased but it is refunded
             // This might be a duplicate event or a full refund was already processed.
             if ($payment->status === 'refunded' || $payment->status === 'partially_refunded') {
                Log::info('Stripe charge.refunded webhook: No new amount to refund or already refunded.', ['payment_id' => $payment->id, 'charge_total_refunded' => $stripeCharge->amount_refunded / 100]);
                return ['success' => true, 'message' => 'No new amount or already refunded.'];
             }
        }
        
        $currentTotalRefunded = (float)($payment->metadata['total_refunded_amount'] ?? 0);
        $amountForThisEvent = $stripeCharge->amount_refunded / 100; // This is the new total refunded on the charge

        $newOverallTotalRefunded = $amountForThisEvent; // The charge's amount_refunded is the new source of truth for total.

        $newStatus = $payment->status;
        if (abs($newOverallTotalRefunded - (float)$payment->amount) < 0.01) { // Compare with original payment amount
            $newStatus = 'refunded';
        } elseif ($newOverallTotalRefunded > 0) {
            $newStatus = 'partially_refunded';
        }

        $refundsMetadata = $payment->metadata['refunds'] ?? [];
        // Add details of this charge's refund status
        $refundsMetadata[] = [
            'stripe_charge_id' => $stripeCharge->id,
            'amount_refunded_on_charge_now' => $stripeCharge->amount_refunded / 100,
            'status' => $stripeCharge->status, // e.g., "succeeded"
            'refunded_at_webhook' => now()->toIso8601String(),
            // Add specific Stripe refund object IDs if available and needed for idempotency
            'stripe_refund_ids_on_charge' => array_map(function($r) { return $r->id; }, $stripeCharge->refunds->data ?? [])
        ];
        
        $processedStripeRefundIds = $payment->metadata['processed_stripe_refund_ids'] ?? [];
        if($stripeRefundId && !in_array($stripeRefundId, $processedStripeRefundIds)) {
            $processedStripeRefundIds[] = $stripeRefundId;
        }


        $this->updatePaymentStatus($payment, $newStatus, [
            'total_refunded_amount' => $newOverallTotalRefunded,
            'refunds' => $refundsMetadata, // Store all refund details
            'processed_stripe_refund_ids' => $processedStripeRefundIds,
            'stripe_event_type' => 'charge.refunded',
            'source' => 'webhook'
        ]);

        // Update order status
        $order = $payment->order;
        if ($order) {
            $orderStatus = ($newStatus === 'refunded') ? 'refunded' : ($newStatus === 'partially_refunded' ? 'partially_refunded' : $order->status);
            if ($order->status !== 'refunded' || ($order->status === 'partially_refunded' && $newStatus === 'refunded')) { // Update if not fully refunded yet, or becoming fully refunded
                 $order->update(['status' => $orderStatus]);
                 $order->notes = ($order->notes ? $order->notes . "\n" : '') . "Payment {$newStatus} via Stripe webhook. Charge: {$stripeCharge->id}. Total refunded on payment: {$newOverallTotalRefunded}.";
                 $order->save();
            }
        }

        Log::info('PaymentService: Processed Stripe charge.refunded.', ['payment_id' => $payment->id, 'charge_id' => $stripeCharge->id, 'total_refunded_on_payment' => $newOverallTotalRefunded]);
        return ['success' => true, 'message' => 'Stripe refund event processed.'];
    }

    /**
     * Get a payment gateway instance.
     */
    protected function getGateway(string $gateway): PaymentGateway
    {
        return PaymentGatewayFactory::create($gateway);
    }

    /**
     * Check if a retry should be attempted.
     */
    protected function shouldRetry(string $operationKey): bool
    {
        $attempts = Cache::get($operationKey . '_attempts', 0);
        if ($attempts >= self::MAX_RETRY_ATTEMPTS) {
            Cache::forget($operationKey . '_attempts'); // Clear on max attempts
            return false;
        }
        Cache::put($operationKey . '_attempts', $attempts + 1, now()->addMinutes(30));
        return true;
    }

    /**
     * Get the current retry attempt number.
     */
    protected function getRetryAttempt(string $operationKey): int
    {
        return Cache::get($operationKey . '_attempts', 0);
    }

    /**
     * Get available payment methods.
     */
    public function getAvailablePaymentMethods(): array
    {
        return [
            'stripe' => [
                'name' => 'Credit Card (Stripe)',
                'description' => 'Pay securely with your credit card via Stripe.',
                'icon' => 'fa-credit-card',
            ],
            'paypal' => [
                'name' => 'PayPal',
                'description' => 'Pay with your PayPal account.',
                'icon' => 'fa-paypal',
            ],
            'bank_transfer' => [
                'name' => 'Bank Transfer',
                'description' => 'Pay via bank transfer. Your order will be processed after we receive the payment.',
                'icon' => 'fa-university',
            ],
        ];
    }
}
