<?php

namespace App\Services;

use App\Models\EmailPreference;
use App\Models\Order;
use Illuminate\Mail\Mailable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Queue;

class EmailNotificationService
{
    /**
     * Send an email notification with error handling.
     */
    public function sendOrderEmail(string $email, Mailable $mailable, string $emailType, Order $order): bool
    {
        try {
            // Check if user wants to receive this type of email
            if (!$this->shouldSendEmail($email, $emailType)) {
                Log::info('Email skipped due to user preferences', [
                    'email' => $email,
                    'email_type' => $emailType,
                    'order_id' => $order->id,
                ]);
                return false;
            }

            // Check if email is valid
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                Log::warning('Invalid email address', [
                    'email' => $email,
                    'email_type' => $emailType,
                    'order_id' => $order->id,
                ]);
                return false;
            }

            // Queue the email
            Mail::to($email)->queue($mailable);

            Log::info('Email queued successfully', [
                'email' => $email,
                'email_type' => $emailType,
                'order_id' => $order->id,
                'order_number' => $order->order_number,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to queue email', [
                'email' => $email,
                'email_type' => $emailType,
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Try to send a fallback notification to admin
            $this->notifyAdminOfEmailFailure($email, $emailType, $order, $e);

            return false;
        }
    }

    /**
     * Check if the customer wants to receive a specific type of email.
     */
    private function shouldSendEmail(string $email, string $emailType): bool
    {
        try {
            $preference = EmailPreference::where('email', $email)->first();
            
            if (!$preference) {
                // If no preference exists, send all order-related emails by default
                return true;
            }

            return $preference->wantsEmail($emailType);
        } catch (\Exception $e) {
            Log::error('Error checking email preferences', [
                'email' => $email,
                'email_type' => $emailType,
                'error' => $e->getMessage(),
            ]);
            
            // Default to sending email if we can't check preferences
            return true;
        }
    }

    /**
     * Notify admin of email failure.
     */
    private function notifyAdminOfEmailFailure(string $customerEmail, string $emailType, Order $order, \Exception $exception): void
    {
        try {
            $adminEmail = config('mail.admin_email') ?? config('mail.from.address');
            
            if (!$adminEmail) {
                return;
            }

            $subject = "Email Notification Failure - Order #{$order->order_number}";
            $message = "Failed to send {$emailType} email to {$customerEmail} for order #{$order->order_number}.\n\n";
            $message .= "Error: {$exception->getMessage()}\n\n";
            $message .= "Order Details:\n";
            $message .= "- Order ID: {$order->id}\n";
            $message .= "- Customer: {$order->customer_name}\n";
            $message .= "- Email: {$order->customer_email}\n";
            $message .= "- Total: {$order->formatted_total}\n";
            $message .= "- Status: {$order->status}\n";

            Mail::raw($message, function ($mail) use ($adminEmail, $subject) {
                $mail->to($adminEmail)
                     ->subject($subject);
            });

        } catch (\Exception $e) {
            Log::critical('Failed to notify admin of email failure', [
                'customer_email' => $customerEmail,
                'email_type' => $emailType,
                'order_id' => $order->id,
                'original_error' => $exception->getMessage(),
                'notification_error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Retry failed email jobs.
     */
    public function retryFailedEmails(int $maxRetries = 3): array
    {
        $results = [];
        
        try {
            // Get failed jobs from the queue
            $failedJobs = Queue::getFailedJobs();
            
            foreach ($failedJobs as $job) {
                if ($this->isEmailJob($job) && $job['failed_at'] < now()->subMinutes(5)) {
                    try {
                        Queue::retry($job['id']);
                        $results['retried'][] = $job['id'];
                        
                        Log::info('Retried failed email job', [
                            'job_id' => $job['id'],
                            'payload' => $job['payload'],
                        ]);
                        
                    } catch (\Exception $e) {
                        $results['failed'][] = [
                            'job_id' => $job['id'],
                            'error' => $e->getMessage(),
                        ];
                        
                        Log::error('Failed to retry email job', [
                            'job_id' => $job['id'],
                            'error' => $e->getMessage(),
                        ]);
                    }
                }
            }
            
        } catch (\Exception $e) {
            Log::error('Error retrying failed emails', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
        
        return $results;
    }

    /**
     * Check if a job is an email job.
     */
    private function isEmailJob(array $job): bool
    {
        $payload = json_decode($job['payload'], true);
        $command = $payload['data']['command'] ?? '';
        
        return str_contains($command, 'Mail') || 
               str_contains($command, 'Email') ||
               str_contains($command, 'Notification');
    }

    /**
     * Get email statistics.
     */
    public function getEmailStats(int $days = 7): array
    {
        // This would typically query a dedicated email_logs table
        // For now, we'll return basic stats from logs
        return [
            'period' => "{$days} days",
            'total_sent' => 0, // Would be calculated from logs/database
            'total_failed' => 0,
            'success_rate' => 0,
            'most_common_failures' => [],
        ];
    }
}
