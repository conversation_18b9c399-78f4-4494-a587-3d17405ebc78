<?php

namespace App\Services\Gateways;

use App\Contracts\PaymentGateway;
use App\Models\Order;
use App\Models\Payment;
use Illuminate\Support\Facades\Log;
use Srmklive\PayPal\Services\PayPal as PayPalClient;

class SrmklivePayPalGateway implements PaymentGateway
{
    /**
     * The PayPal client instance.
     */
    protected PayPalClient $paypalClient;

    /**
     * Create a new PayPal gateway instance.
     */
    public function __construct()
    {
        try {
            // Initialize PayPal client directly with config
            $this->paypalClient = new PayPalClient(config('paypal'));
            $token = $this->paypalClient->getAccessToken();
            $this->paypalClient->setAccessToken($token);
        } catch (\Exception $e) {
            Log::error('Failed to initialize PayPal client', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Create a payment intent.
     */
    public function createPaymentIntent(Order $order, array $data = []): array
    {
        try {
            $returnUrl = $data['return_url'] ?? route('checkout.paypal.callback');
            $cancelUrl = $data['cancel_url'] ?? route('checkout.paypal.cancel', ['order_id' => $order->id]);
            $this->paypalClient->setRequestHeader("PayPal-Request-Id", \Illuminate\Support\Str::uuid());

            $orderData = [
                "intent" => "CAPTURE", // Ensures total amount is captured
                "purchase_units" => [
                    [
                        "invoice_id" => $order->tracking_number ?? $order->id,
                        "amount" => [
                            "currency_code" => strtoupper($order->currency),
                            "value" => round($order->total, 2), // Full amount
                        ],
                        'description' => "Order From " . config('app.name'),
                    ]
                ],
                "payment_source" => [
                    "paypal" => [
                        "experience_context" => [
                            'user_action' => 'PAY_NOW',
                            'payment_method_preference' => 'IMMEDIATE_PAYMENT_REQUIRED',
                            'cancel_url' => $cancelUrl,
                            'return_url' => $returnUrl
                        ]
                    ]
                ],
            ];

            $response = $this->paypalClient->createOrder($orderData);

            if (!isset($response['id'])) {
                Log::error('Failed to create PayPal order', [
                    'order_id' => $order->id, 'response' => $response,
                ]);
                return ['success' => false, 'message' => 'Failed to create PayPal order', 'error' => $response['error'] ?? 'Unknown error'];
            }

            $redirectLink = null;
            if (isset($response['links']) && is_array($response['links'])) {
                foreach ($response['links'] as $link) {
                    if (isset($link['rel']) && ($link['rel'] === 'approve' || $link['rel'] === 'payer-action') && isset($link['href'])) {
                        $redirectLink = $link['href'];
                        break;
                    }
                }
            }

            if (!$redirectLink) {
                Log::error('No redirect link (approve or payer-action) found in PayPal response', [
                    'order_id' => $order->id, 'response' => $response,
                ]);
                return ['success' => false, 'message' => 'No redirect link found in PayPal response', 'response' => $response];
            }

            $payment = Payment::create([
                'order_id' => $order->id,
                'payment_method' => 'paypal',
                'amount' => $order->total, // Expected full amount
                'currency' => $order->currency,
                'status' => 'pending',
                'gateway_payment_id' => $response['id'],
                'metadata' => ['paypal_order' => $response],
            ]);

            return [
                'success' => true, 'payment_id' => $response['id'],
                'redirect_url' => $redirectLink, 'payment' => $payment,
                'is_redirect' => true, 'response' => $response,
            ];
        } catch (\Exception $e) {
            Log::error('Error creating PayPal payment intent', [
                'order_id' => $order->id, 'error' => $e->getMessage(), 'trace' => $e->getTraceAsString(),
            ]);
            return ['success' => false, 'message' => 'An error occurred while creating the PayPal payment', 'error' => $e->getMessage()];
        }
    }

    /**
     * Execute a payment.
     */
    public function executePayment(string $paymentId, array $data = []): array
    {
        try {
            $payment = Payment::where('gateway_payment_id', $paymentId)->first();
            if (!$payment) {
                return ['success' => false, 'message' => 'Payment not found'];
            }

            if ($payment->status === 'completed') {
                return ['success' => true, 'message' => 'Payment already processed', 'payment' => $payment];
            }

            $orderDetails = $this->paypalClient->showOrderDetails($paymentId);
            if (!isset($orderDetails['status'])) {
                Log::error('Failed to retrieve PayPal order details', [
                    'payment_id' => $paymentId, 'response' => $orderDetails,
                ]);
                return ['success' => false, 'message' => 'Failed to retrieve PayPal order details', 'error' => $orderDetails['error'] ?? 'Unknown error'];
            }

            $validationResult = $this->validatePaymentAmountBeforeCapture($payment, $orderDetails);
            if (!$validationResult['success']) {
                return $validationResult; // Already logs and updates payment status to failed
            }

            if ($orderDetails['status'] === 'COMPLETED') {
                Log::info('PayPal order already captured (found via showOrderDetails)', [
                    'payment_id' => $paymentId, 'order_status' => $orderDetails['status'],
                ]);
                $captureId = $orderDetails['purchase_units'][0]['payments']['captures'][0]['id'] ?? null;
                $paypalOrderAmount = $validationResult['paypal_order_amount']; // Amount from orderDetails, validated by validatePaymentAmountBeforeCapture

                if ($captureId) {
                    $paymentMetadata = [
                        'paypal_capture_result' => $orderDetails,
                        'recovery_method' => 'order_status_check_before_capture_attempt',
                        'expected_amount' => (float) $payment->order->total,
                        'captured_amount' => $paypalOrderAmount,
                    ];

                    $expectedAmountForOrder = (float) $payment->order->total;
                    if ($paypalOrderAmount > ($expectedAmountForOrder + 0.01)) { // isOverpayment
                        $paymentMetadata['payment_type'] = 'overpayment';
                        $paymentMetadata['overpayment_amount'] = abs($paypalOrderAmount - $expectedAmountForOrder);
                        $paymentMetadata['requires_manual_review'] = true;
                    } else { // Exact match (underpayment already handled by validatePaymentAmountBeforeCapture)
                        $paymentMetadata['payment_type'] = 'full';
                        $paymentMetadata['amount_verified'] = true;
                    }
                    
                    $payment->update([
                        'status' => 'completed',
                        'amount' => $paypalOrderAmount,
                        'gateway_transaction_id' => $captureId,
                        'processed_at' => now(),
                        'metadata' => array_merge($payment->metadata ?? [], $paymentMetadata),
                    ]);

                    $order = $payment->order;
                    if ($order && $order->status === 'pending') {
                        $order->update(['status' => 'processing']);
                        if ($paymentMetadata['payment_type'] === 'overpayment') {
                             $order->notes = ($order->notes ? $order->notes . "\n" : '') .
                                  "Overpayment detected (order already completed on PayPal): {$payment->currency} {$paypalOrderAmount} (expected {$expectedAmountForOrder}) on " . now()->format('Y-m-d H:i:s') . " - Requires manual review";
                             $order->save();
                        }
                    }
                    return ['success' => true, 'message' => 'Payment already captured and verified', 'payment' => $payment, 'capture_result' => $orderDetails];
                } else {
                     Log::error('PayPal order COMPLETED but no capture ID found in orderDetails.', [
                        'payment_id' => $paymentId, 'orderDetails' => $orderDetails,
                     ]);
                     // This scenario is problematic; proceed cautiously or fail.
                     // For now, this will likely fall through to "not APPROVED" check or attempt capture again if status is misreported.
                }
            }

            if ($orderDetails['status'] !== 'APPROVED') {
                Log::warning('PayPal order not in capturable state', [
                    'payment_id' => $paymentId, 'order_status' => $orderDetails['status'],
                ]);
                return ['success' => false, 'message' => "PayPal order is in {$orderDetails['status']} state and cannot be captured", 'order_status' => $orderDetails['status']];
            }

            $response = $this->paypalClient->capturePaymentOrder($paymentId);

            if (!isset($response['status']) || $response['status'] !== 'COMPLETED') {
                Log::error('Failed to capture PayPal payment', [
                    'payment_id' => $paymentId, 'response' => $response,
                ]);
                $payment->update([
                    'status' => 'failed', 'processed_at' => now(),
                    'metadata' => array_merge($payment->metadata ?? [], [
                        'paypal_capture_failed_result' => $response,
                        'failure_reason' => 'Capture API call failed or did not complete',
                    ]),
                ]);
                return ['success' => false, 'message' => 'Failed to capture PayPal payment', 'error' => $response['error'] ?? $response['message'] ?? 'Unknown error during capture'];
            }

            $captureId = $response['purchase_units'][0]['payments']['captures'][0]['id'] ?? null;
            $capturedAmount = (float) ($response['purchase_units'][0]['payments']['captures'][0]['amount']['value'] ?? 0);
            $capturedCurrency = strtoupper($response['purchase_units'][0]['payments']['captures'][0]['amount']['currency_code'] ?? '');

            $order = $payment->order;
            $expectedAmount = (float) $order->total;
            $expectedCurrency = strtoupper($order->currency);

            if ($capturedCurrency !== $expectedCurrency) {
                Log::error('PayPal currency mismatch detected after capture', [
                    'payment_id' => $paymentId, 'order_id' => $order->id,
                    'expected_currency' => $expectedCurrency, 'captured_currency' => $capturedCurrency,
                ]);
                $payment->update([
                    'status' => 'failed', 'amount' => $capturedAmount, 'gateway_transaction_id' => $captureId, 'processed_at' => now(),
                    'metadata' => array_merge($payment->metadata ?? [], [
                        'paypal_capture_result' => $response, 'payment_type' => 'currency_mismatch_error',
                        'expected_currency' => $expectedCurrency, 'captured_currency' => $capturedCurrency,
                    ]),
                ]);
                if ($order) {
                    $order->update([
                        'status' => 'payment_failed',
                        'notes' => ($order->notes ? $order->notes . "\n" : '') . "Payment failed: Currency mismatch. Expected {$expectedCurrency}, received {$capturedCurrency}."
                    ]);
                }
                return ['success' => false, 'message' => 'Currency mismatch detected', 'error' => "Expected {$expectedCurrency}, received {$capturedCurrency}"];
            }

            $amountDifference = $capturedAmount - $expectedAmount;
            $isUnderpayment = $capturedAmount < ($expectedAmount - 0.01);
            $isOverpayment = $capturedAmount > ($expectedAmount + 0.01);

            if ($isUnderpayment) {
                Log::error('PayPal underpayment detected after capture', [
                    'payment_id' => $paymentId, 'order_id' => $order->id,
                    'expected_amount' => $expectedAmount, 'captured_amount' => $capturedAmount, 'difference' => $amountDifference,
                ]);
                $payment->update([
                    'status' => 'failed', 'amount' => $capturedAmount, 'gateway_transaction_id' => $captureId, 'processed_at' => now(),
                    'metadata' => array_merge($payment->metadata ?? [], [
                        'paypal_capture_result' => $response, 'payment_type' => 'underpayment_error',
                        'expected_amount' => $expectedAmount, 'captured_amount' => $capturedAmount,
                        'amount_difference' => $amountDifference, 'rejection_reason' => 'Underpayment detected. Full amount required.',
                    ]),
                ]);
                if ($order && in_array($order->status, ['pending', 'processing'])) {
                    $order->update([
                        'status' => 'payment_failed',
                        'notes' => ($order->notes ? $order->notes . "\n" : '') . "Payment failed: Underpayment. Captured {$capturedCurrency} {$capturedAmount} of {$expectedAmount}."
                    ]);
                }
                return [
                    'success' => false, 'message' => 'Underpayment detected. Full payment required.',
                    'payment' => $payment, 'capture_result' => $response, 'payment_type' => 'underpayment_error',
                    'captured_amount' => $capturedAmount, 'expected_amount' => $expectedAmount,
                ];
            } elseif ($isOverpayment) {
                Log::warning('PayPal overpayment detected after capture', [
                    'payment_id' => $paymentId, 'order_id' => $order->id,
                    'expected_amount' => $expectedAmount, 'captured_amount' => $capturedAmount, 'overpayment' => $amountDifference,
                ]);
                $payment->update([
                    'status' => 'completed', 'amount' => $capturedAmount, 'gateway_transaction_id' => $captureId, 'processed_at' => now(),
                    'metadata' => array_merge($payment->metadata ?? [], [
                        'paypal_capture_result' => $response, 'payment_type' => 'overpayment',
                        'expected_amount' => $expectedAmount, 'captured_amount' => $capturedAmount,
                        'overpayment_amount' => $amountDifference, 'requires_manual_review' => true,
                    ]),
                ]);
                if ($order && $order->status === 'pending') {
                    $order->update([
                        'status' => 'processing',
                        'notes' => ($order->notes ? $order->notes . "\n" : '') . "Overpayment received: {$capturedCurrency} {$capturedAmount} (expected {$expectedAmount}) - Requires manual review."
                    ]);
                }
            } else { // Exact amount
                $payment->update([
                    'status' => 'completed', 'gateway_transaction_id' => $captureId, 'processed_at' => now(),
                    'metadata' => array_merge($payment->metadata ?? [], [
                        'paypal_capture_result' => $response, 'payment_type' => 'full', 'amount_verified' => true,
                    ]),
                ]);
                if ($order && $order->status === 'pending') {
                    $order->update(['status' => 'processing']);
                }
            }

            return ['success' => true, 'message' => 'Payment executed successfully', 'payment' => $payment, 'capture_result' => $response];
        } catch (\Exception $e) {
            Log::error('Error executing PayPal payment', [
                'payment_id' => $paymentId, 'error' => $e->getMessage(), 'trace' => $e->getTraceAsString(),
            ]);
            $isConnectionError = (strpos($e->getMessage(), 'cURL error') !== false || strpos($e->getMessage(), 'Connection') !== false || strpos($e->getMessage(), 'timeout') !== false);

            if ($isConnectionError && isset($payment)) {
                try {
                    $orderResponse = $this->paypalClient->showOrderDetails($paymentId);
                    if (isset($orderResponse['status']) && $orderResponse['status'] === 'COMPLETED') {
                        $captureId = $orderResponse['purchase_units'][0]['payments']['captures'][0]['id'] ?? null;
                        $recoveredAmount = (float) ($orderResponse['purchase_units'][0]['amount']['value'] ?? 0);
                        
                        $order = $payment->order;
                        $expectedAmount = (float) $order->total;
                        $paymentMetadata = [
                            'paypal_capture_result' => $orderResponse, 'recovery_method' => 'connection_error_verification',
                            'expected_amount' => $expectedAmount, 'captured_amount' => $recoveredAmount,
                        ];

                        if ($recoveredAmount < ($expectedAmount - 0.01)) {
                            Log::error('PayPal underpayment detected during connection error recovery', [
                                'payment_id' => $paymentId, 'order_id' => $order->id,
                                'expected_amount' => $expectedAmount, 'captured_amount' => $recoveredAmount,
                            ]);
                            $payment->update([
                                'status' => 'failed', 'amount' => $recoveredAmount, 'gateway_transaction_id' => $captureId, 'processed_at' => now(),
                                'metadata' => array_merge($payment->metadata ?? [], $paymentMetadata, [
                                    'payment_type' => 'underpayment_error_recovery', 'rejection_reason' => 'Underpayment detected during recovery.',
                                ]),
                            ]);
                            if ($order) $order->update(['status' => 'payment_failed']);
                            return ['success' => false, 'message' => 'Payment recovered but found to be an underpayment.', 'payment' => $payment];
                        } elseif ($recoveredAmount > ($expectedAmount + 0.01)) {
                            $paymentMetadata['payment_type'] = 'overpayment_recovery';
                            $paymentMetadata['overpayment_amount'] = abs($recoveredAmount - $expectedAmount);
                            $paymentMetadata['requires_manual_review'] = true;
                        } else {
                            $paymentMetadata['payment_type'] = 'full_recovery';
                            $paymentMetadata['amount_verified'] = true;
                        }

                        $payment->update([
                            'status' => 'completed', 'amount' => $recoveredAmount, 'gateway_transaction_id' => $captureId, 'processed_at' => now(),
                            'metadata' => array_merge($payment->metadata ?? [], $paymentMetadata),
                        ]);
                        if ($order && $order->status === 'pending') {
                            $order->update(['status' => 'processing']);
                            if (str_contains($paymentMetadata['payment_type'], 'overpayment')) {
                                 $order->notes = ($order->notes ? $order->notes . "\n" : '') .
                                      "Overpayment detected during recovery: {$order->currency} {$recoveredAmount} (expected {$expectedAmount}) - Requires manual review.";
                                 $order->save();
                            }
                        }
                        return ['success' => true, 'message' => 'Payment verified after connection error', 'payment' => $payment, 'capture_result' => $orderResponse];
                    }
                } catch (\Exception $verifyException) {
                    Log::error('Error verifying PayPal payment after connection error', [
                        'payment_id' => $paymentId, 'error' => $verifyException->getMessage(), 'trace' => $verifyException->getTraceAsString(),
                    ]);
                }
            }
            return ['success' => false, 'message' => 'An error occurred while executing the PayPal payment', 'error' => $e->getMessage(), 'is_connection_error' => $isConnectionError ?? false];
        }
    }

    public function verifyPayment(string $paymentId): array
    {
        try {
            $payment = Payment::where('gateway_payment_id', $paymentId)->first();
            if (!$payment) {
                return ['success' => false, 'message' => 'Payment not found'];
            }

            if ($payment->status === 'completed') {
                return ['success' => true, 'message' => 'Payment already verified as completed', 'payment' => $payment, 'status' => 'completed'];
            }

            $result = $this->paypalClient->showOrderDetails($paymentId);
            if (!isset($result['status'])) {
                Log::error('Failed to retrieve PayPal order details for verification', ['payment_id' => $paymentId, 'response' => $result]);
                return ['success' => false, 'message' => 'Failed to retrieve PayPal order details', 'error' => $result['error'] ?? 'Unknown error'];
            }

            $paypalStatus = $result['status'];
            $currentAppStatus = $payment->status;
            $newAppStatus = $currentAppStatus;
            $order = $payment->order;

            switch ($paypalStatus) {
                case 'COMPLETED':
                    $newAppStatus = 'completed';
                    $captureDetails = $result['purchase_units'][0]['payments']['captures'][0] ?? null;
                    $paypalOrderAmount = (float) ($result['purchase_units'][0]['amount']['value'] ?? 0);
                    $paypalOrderCurrency = strtoupper($result['purchase_units'][0]['amount']['currency_code'] ?? '');
                    $expectedAmount = (float) $order->total;
                    $expectedCurrency = strtoupper($order->currency);

                    if ($captureDetails && $paypalOrderCurrency === $expectedCurrency) {
                        $isUnderpayment = $paypalOrderAmount < ($expectedAmount - 0.01);
                        $isOverpayment = $paypalOrderAmount > ($expectedAmount + 0.01);
                        
                        $paymentUpdateData = [
                            'gateway_transaction_id' => $captureDetails['id'], 'processed_at' => now(), 'amount' => $paypalOrderAmount,
                            'metadata' => array_merge($payment->metadata ?? [], [
                                'paypal_verification_result' => $result, 'verified_capture_details' => $captureDetails,
                                'expected_amount' => $expectedAmount, 'captured_amount' => $paypalOrderAmount, // Added for clarity
                            ]),
                        ];

                        if ($isUnderpayment) {
                            $newAppStatus = 'failed';
                            $paymentUpdateData['metadata']['payment_type'] = 'underpayment_error_verification';
                            $paymentUpdateData['metadata']['rejection_reason'] = 'Underpayment detected during verification.';
                            Log::error('Underpayment detected during payment verification', ['payment_id' => $payment->id, 'order_id' => $order->id, 'expected' => $expectedAmount, 'actual' => $paypalOrderAmount]);
                            if ($order) $order->update(['status' => 'payment_failed']);
                        } elseif ($isOverpayment) {
                            $paymentUpdateData['metadata']['payment_type'] = 'overpayment_verification';
                            $paymentUpdateData['metadata']['overpayment_amount'] = abs($paypalOrderAmount - $expectedAmount);
                            $paymentUpdateData['metadata']['requires_manual_review'] = true;
                            if ($order && $order->status === 'pending') $order->update(['status' => 'processing']);
                        } else {
                            $paymentUpdateData['metadata']['payment_type'] = 'full_verification';
                            if ($order && $order->status === 'pending') $order->update(['status' => 'processing']);
                        }
                        $payment->update(array_merge(['status' => $newAppStatus], $paymentUpdateData)); // Merge status with other data
                    } elseif ($paypalOrderCurrency !== $expectedCurrency) {
                        $newAppStatus = 'failed';
                        Log::error('Currency mismatch detected during payment verification', ['payment_id' => $payment->id, 'order_id' => $order->id, 'expected_currency' => $expectedCurrency, 'actual_currency' => $paypalOrderCurrency]);
                        $payment->update(['status' => 'failed', 'metadata' => array_merge($payment->metadata ?? [], ['paypal_verification_result' => $result, 'failure_reason' => 'Currency mismatch during verification'])]);
                        if ($order) $order->update(['status' => 'payment_failed']);
                    } else {
                        Log::warning('PayPal order COMPLETED but capture details missing or inconsistent during verification.', ['payment_id' => $payment->id, 'response' => $result]);
                        $payment->update(['status' => 'completed', 'metadata' => array_merge($payment->metadata ?? [], ['paypal_verification_result' => $result, 'verification_issue' => 'COMPLETED status but capture details problematic.'])]);
                    }
                    break;
                case 'APPROVED': case 'SAVED': case 'PAYER_ACTION_REQUIRED': case 'CREATED':
                    $newAppStatus = 'pending';
                    break;
                case 'VOIDED':
                    $newAppStatus = 'cancelled';
                    if ($order && $order->status === 'pending') $order->update(['status' => 'cancelled']);
                    break;
                default:
                    $newAppStatus = 'failed';
                    if ($order && $order->status === 'pending') $order->update(['status' => 'payment_failed']);
                    break;
            }

            if ($currentAppStatus !== $newAppStatus && $paypalStatus !== 'COMPLETED') {
                 $payment->update(['status' => $newAppStatus, 'metadata' => array_merge($payment->metadata ?? [], ['paypal_verification_result' => $result, 'status_updated_via_verify' => true])]);
            } elseif ($currentAppStatus === $newAppStatus && $paypalStatus !== 'COMPLETED' && !isset($payment->metadata['paypal_verification_result'])) { // Only update if not already there or changed
                 $payment->update(['metadata' => array_merge($payment->metadata ?? [], ['paypal_verification_result' => $result, 'status_confirmed_via_verify' => true])]);
            }
            $payment->refresh();

            return ['success' => true, 'message' => 'Payment verification status: ' . $payment->status, 'payment' => $payment, 'status' => $payment->status, 'verification_result' => $result];
        } catch (\Exception $e) {
            Log::error('Error verifying PayPal payment', ['payment_id' => $paymentId, 'error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return ['success' => false, 'message' => 'An error occurred while verifying the PayPal payment', 'error' => $e->getMessage()];
        }
    }

    public function refundPayment(Payment $payment, ?float $amount = null, ?string $reason = null): array
    {
        try {
            if (!in_array($payment->status, ['completed', 'partially_refunded'])) { // Allow refunding completed or partially_refunded payments
                return ['success' => false, 'message' => 'Payment cannot be refunded. Current status: ' . $payment->status];
            }
            if (!$payment->gateway_transaction_id) {
                return ['success' => false, 'message' => 'No gateway transaction ID (capture ID) found for this payment.'];
            }

            $captureId = $payment->gateway_transaction_id;
            $invoiceId = 'refund-' . $payment->id . '-' . \Illuminate\Support\Str::uuid(); // Unique invoice ID for the refund transaction
            $refundAmount = round($amount ?? (float)$payment->amount, 2);
            $noteToPayer = $reason ?? 'Refund for order #' . $payment->order_id;

            if ($refundAmount <= 0) {
                 return ['success' => false, 'message' => 'Refund amount must be positive.'];
            }
            // Consider checking if $refundAmount exceeds refundable amount on $payment

            $response = $this->paypalClient->refundCapturedPayment($captureId, $invoiceId, $refundAmount, $noteToPayer);

            if (!isset($response['status']) || !in_array($response['status'], ['COMPLETED', 'PENDING'])) {
                Log::error('Failed to process PayPal refund or refund is not completed/pending', [
                    'payment_id' => $payment->id, 'transaction_id' => $captureId, 'response' => $response,
                ]);
                return ['success' => false, 'message' => 'Failed to process PayPal refund. Status: ' . ($response['status'] ?? 'Unknown'), 'error' => $response['message'] ?? $response['details'][0]['description'] ?? 'Unknown error'];
            }

            $currentTotalRefunded = (float)($payment->metadata['total_refunded_amount'] ?? 0);
            $newTotalRefunded = $currentTotalRefunded + $refundAmount;
            
            $newStatus = $payment->status;
            if (abs($newTotalRefunded - (float)$payment->amount) < 0.01) {
                $newStatus = 'refunded';
            } elseif ($newTotalRefunded > 0) {
                $newStatus = 'partially_refunded';
            }

            $refundsMetadata = $payment->metadata['refunds'] ?? [];
            $refundsMetadata[] = $response; // Add current refund object to the list

            $payment->update([
                'status' => $newStatus,
                'metadata' => array_merge($payment->metadata ?? [], [
                    'refund_result' => $response, // Last refund result
                    'refunds' => $refundsMetadata, // All refund results
                    'total_refunded_amount' => $newTotalRefunded,
                    'last_refund_amount' => $refundAmount, 'last_refund_reason' => $reason,
                    'last_refund_id' => $response['id'] ?? null, 'refunded_at' => now(),
                ]),
            ]);

            $order = $payment->order;
            if ($order) {
                $orderStatus = ($newStatus === 'refunded') ? 'refunded' : 'partially_refunded';
                if ($order->status !== 'refunded') {
                    $order->update(['status' => $orderStatus]);
                    $order->notes = ($order->notes ? $order->notes . "\n" : '') . "Payment {$newStatus}: {$payment->currency} {$refundAmount} on " . now()->format('Y-m-d H:i:s') . ". Refund ID: " . ($response['id'] ?? 'N/A');
                    $order->save();
                }
            }
            return ['success' => true, 'message' => 'Payment refund processed. Status: ' . $response['status'], 'payment' => $payment, 'refund_result' => $response];
        } catch (\Exception $e) {
            Log::error('Error refunding PayPal payment', ['payment_id' => $payment->id, 'error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return ['success' => false, 'message' => 'An error occurred while refunding the PayPal payment', 'error' => $e->getMessage()];
        }
    }

    public function validateWebhook(array $payload, string $signature, string $signatureHeader): bool
    {
        try {
            $webhookId = config('paypal.' . config('paypal.mode') . '.webhook_id');
            if (empty($webhookId)) {
                Log::warning('PayPal webhook ID is not configured');
                return false;
            }

            $parsedHeaders = [];
            $rawHeaders = explode(';', $signatureHeader); // PayPal headers are semi-colon separated
            foreach ($rawHeaders as $header) {
                $parts = explode('=', trim($header), 2);
                if (count($parts) === 2) {
                    $parsedHeaders[trim($parts[0])] = trim($parts[1]);
                }
            }

            if (empty($parsedHeaders['PAYPAL-AUTH-ALGO']) || empty($parsedHeaders['PAYPAL-CERT-URL']) ||
                empty($parsedHeaders['PAYPAL-TRANSMISSION-ID']) || empty($parsedHeaders['PAYPAL-TRANSMISSION-TIME']) ||
                empty($signature)) { // $signature is PAYPAL-TRANSMISSION-SIG
                Log::warning('Malformed PayPal webhook signature header components.', ['parsed_keys' => array_keys($parsedHeaders), 'signature_present' => !empty($signature)]);
                return false;
            }

            $verifyData = [
                'auth_algo' => $parsedHeaders['PAYPAL-AUTH-ALGO'], 'cert_url' => $parsedHeaders['PAYPAL-CERT-URL'],
                'transmission_id' => $parsedHeaders['PAYPAL-TRANSMISSION-ID'], 'transmission_sig' => $signature,
                'transmission_time' => $parsedHeaders['PAYPAL-TRANSMISSION-TIME'], 'webhook_id' => $webhookId,
                'webhook_event' => $payload
            ];
            
            $verificationResponse = $this->paypalClient->verifyWebHook($verifyData);
            $isValid = isset($verificationResponse['verification_status']) && $verificationResponse['verification_status'] === 'SUCCESS';

            if (!$isValid) {
                Log::warning('Invalid PayPal webhook signature', [
                    'event_type' => $payload['event_type'] ?? 'unknown', 'event_id' => $payload['id'] ?? null,
                    'verification_status' => $verificationResponse['verification_status'] ?? 'unknown',
                    'response_details' => $verificationResponse['message'] ?? ($verificationResponse['name'] ?? ($verificationResponse['details'][0]['description'] ?? 'No details')),
                ]);
            }
            return $isValid;
        } catch (\Exception $e) {
            Log::error('Error validating PayPal webhook', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString(), 'event_type' => $payload['event_type'] ?? 'unknown']);
            return false;
        }
    }

    public function processWebhookEvent(array $payload): array
    {
        try {
            $eventType = $payload['event_type'] ?? null;
            $resource = $payload['resource'] ?? [];
            $eventId = $payload['id'] ?? null;

            if (!$eventType) {
                return ['success' => false, 'message' => 'Invalid webhook payload: missing event_type'];
            }
            Log::info('Processing PayPal webhook event', ['event_type' => $eventType, 'event_id' => $eventId]);

            $cacheKey = "paypal_webhook_event_processed_{$eventId}";
            if ($eventId && cache()->has($cacheKey)) {
                Log::info('PayPal webhook event already processed (idempotency check)', ['event_id' => $eventId, 'event_type' => $eventType]);
                return ['success' => true, 'message' => 'Event already processed', 'event_type' => $eventType];
            }

            $result = match ($eventType) {
                'PAYMENT.CAPTURE.COMPLETED' => $this->handlePaymentCaptureCompleted($resource, $eventId),
                'PAYMENT.CAPTURE.DENIED' => $this->handlePaymentCaptureDenied($resource, $eventId),
                'PAYMENT.CAPTURE.REFUNDED' => $this->handlePaymentCaptureRefunded($resource, $eventId),
                'PAYMENT.CAPTURE.PENDING' => $this->handlePaymentCapturePending($resource, $eventId),
                'PAYMENT.CAPTURE.REVERSED' => $this->handlePaymentCaptureReversed($resource, $eventId),
                'CHECKOUT.ORDER.APPROVED' => $this->handleCheckoutOrderApproved($resource, $eventId),
                default => ['success' => true, 'message' => "Event type '{$eventType}' acknowledged but not specifically processed.", 'event_type' => $eventType],
            };

            if ($eventId && ($result['success'] ?? false)) {
                cache()->put($cacheKey, true, now()->addDays(7));
            } elseif ($eventId && !($result['success'] ?? false)) {
                 Log::warning('Failed to process PayPal webhook event handler, will not mark as processed for potential retry', ['event_id' => $eventId, 'event_type' => $eventType, 'result' => $result]);
            }
            return $result;
        } catch (\Exception $e) {
            Log::error('Error processing PayPal webhook event', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString(), 'event_type' => $payload['event_type'] ?? 'unknown', 'event_id' => $payload['id'] ?? null]);
            return ['success' => false, 'message' => 'An error occurred while processing the webhook event', 'error' => $e->getMessage()];
        }
    }

    protected function handlePaymentCaptureCompleted(array $resource, ?string $eventId = null): array
    {
        $captureId = $resource['id'] ?? null;
        $paypalOrderId = null;
        if (isset($resource['links'])) {
            foreach ($resource['links'] as $link) {
                if (isset($link['rel'], $link['href']) && $link['rel'] === 'up' && str_contains($link['href'], '/v2/checkout/orders/')) {
                    $parts = explode('/', $link['href']);
                    $paypalOrderId = end($parts);
                    break;
                }
            }
        }
        if (!$paypalOrderId && isset($resource['invoice_id'])) { // Fallback to invoice_id if it was our Order ID/tracking_number
            $order = Order::where('tracking_number', $resource['invoice_id'])->orWhere('id', $resource['invoice_id'])->first();
            if ($order) {
                $payment = $order->payments()->where('payment_method', 'paypal')->whereNotNull('gateway_payment_id')->latest()->first();
                if ($payment) $paypalOrderId = $payment->gateway_payment_id;
            }
        }

        $capturedAmount = (float) ($resource['amount']['value'] ?? 0);
        $capturedCurrency = strtoupper($resource['amount']['currency_code'] ?? '');

        if (!$captureId || !$paypalOrderId) {
            Log::error('Webhook PAYMENT.CAPTURE.COMPLETED: Missing capture_id or unable to determine PayPal Order ID.', ['resource' => $resource, 'eventId' => $eventId]);
            return ['success' => false, 'message' => 'Invalid capture data: missing critical IDs.'];
        }

        $payment = Payment::where('gateway_payment_id', $paypalOrderId)->first();
        if (!$payment) {
            Log::warning('Webhook PAYMENT.CAPTURE.COMPLETED: Payment not found for PayPal Order ID.', ['paypal_order_id' => $paypalOrderId, 'capture_id' => $captureId, 'eventId' => $eventId]);
            return ['success' => false, 'message' => 'Payment not found for PayPal Order ID: ' . $paypalOrderId];
        }
        
        if ($payment->status === 'completed' && $payment->gateway_transaction_id === $captureId) {
            Log::info('Webhook PAYMENT.CAPTURE.COMPLETED: Already processed.', ['payment_id' => $payment->id, 'capture_id' => $captureId]);
            return ['success' => true, 'message' => 'Payment already completed.'];
        }

        $order = $payment->order;
        $expectedAmount = (float) $order->total;
        $expectedCurrency = strtoupper($order->currency);

        if ($capturedCurrency !== $expectedCurrency) {
            Log::error('Webhook PAYMENT.CAPTURE.COMPLETED: Currency mismatch.', ['payment_id' => $payment->id, 'expected' => $expectedCurrency, 'captured' => $capturedCurrency]);
            $payment->update(['status' => 'failed', 'gateway_transaction_id' => $captureId, 'processed_at' => now(), 'amount' => $capturedAmount, 'metadata' => array_merge($payment->metadata ?? [], ['paypal_webhook_event_id' => $eventId, 'paypal_capture_details' => $resource, 'failure_reason' => 'currency_mismatch_webhook'])]);
            if ($order) $order->update(['status' => 'payment_failed']);
            return ['success' => true, 'message' => 'Currency mismatch handled, payment failed.'];
        }

        $amountDifference = $capturedAmount - $expectedAmount;
        $isUnderpayment = $capturedAmount < ($expectedAmount - 0.01);
        $isOverpayment = $capturedAmount > ($expectedAmount + 0.01);

        $paymentUpdateData = [
            'gateway_transaction_id' => $captureId, 'processed_at' => now(), 'amount' => $capturedAmount,
            'metadata' => array_merge($payment->metadata ?? [], [
                'paypal_webhook_event_id' => $eventId, 'paypal_capture_details' => $resource, 'source' => 'webhook',
                'expected_amount' => $expectedAmount, 'captured_amount' => $capturedAmount,
            ]),
        ];

        if ($isUnderpayment) {
            Log::error('Webhook PAYMENT.CAPTURE.COMPLETED: Underpayment detected.', ['payment_id' => $payment->id, 'expected' => $expectedAmount, 'captured' => $capturedAmount]);
            $paymentUpdateData['status'] = 'failed';
            $paymentUpdateData['metadata']['payment_type'] = 'underpayment_error';
            $paymentUpdateData['metadata']['amount_difference'] = $amountDifference;
            $paymentUpdateData['metadata']['rejection_reason'] = 'Underpayment detected via webhook.';
            $payment->update($paymentUpdateData);
            if ($order && in_array($order->status, ['pending', 'processing'])) {
                $order->update(['status' => 'payment_failed', 'notes' => ($order->notes ? $order->notes . "\n" : '') . "Payment failed via webhook: Underpayment. Captured {$capturedCurrency} {$capturedAmount} of {$expectedAmount}."]);
            }
            return ['success' => true, 'message' => 'Underpayment handled, payment failed.'];
        } elseif ($isOverpayment) {
            Log::warning('Webhook PAYMENT.CAPTURE.COMPLETED: Overpayment detected.', ['payment_id' => $payment->id, 'expected' => $expectedAmount, 'captured' => $capturedAmount]);
            $paymentUpdateData['status'] = 'completed';
            $paymentUpdateData['metadata']['payment_type'] = 'overpayment';
            $paymentUpdateData['metadata']['overpayment_amount'] = $amountDifference;
            $paymentUpdateData['metadata']['requires_manual_review'] = true;
            $payment->update($paymentUpdateData);
            if ($order && in_array($order->status, ['pending', 'payment_failed'])) {
                $order->update(['status' => 'processing', 'notes' => ($order->notes ? $order->notes . "\n" : '') . "Overpayment received via webhook: {$capturedCurrency} {$capturedAmount} (expected {$expectedAmount}) - Requires manual review."]);
            }
        } else { // Exact amount
            $paymentUpdateData['status'] = 'completed';
            $paymentUpdateData['metadata']['payment_type'] = 'full';
            $paymentUpdateData['metadata']['amount_verified'] = true;
            $payment->update($paymentUpdateData);
            if ($order && in_array($order->status, ['pending', 'payment_failed'])) {
                $order->update(['status' => 'processing']);
            }
        }
        return ['success' => true, 'message' => 'Payment capture completed event processed.', 'payment_id' => $payment->id];
    }

    protected function handlePaymentCaptureDenied(array $resource, ?string $eventId = null): array
    {
        $captureAttemptId = $resource['id'] ?? null; // ID of the capture attempt that was denied
        $paypalOrderId = null;
        if (isset($resource['links'])) {
            foreach ($resource['links'] as $link) {
                if (isset($link['rel'], $link['href']) && $link['rel'] === 'up' && str_contains($link['href'], '/v2/checkout/orders/')) {
                    $parts = explode('/', $link['href']);
                    $paypalOrderId = end($parts);
                    break;
                }
            }
        }

        if (!$paypalOrderId) {
            Log::error('Webhook PAYMENT.CAPTURE.DENIED: Missing PayPal Order ID.', ['resource' => $resource, 'eventId' => $eventId]);
            return ['success' => false, 'message' => 'Invalid denied capture data: missing PayPal Order ID.'];
        }

        $payment = Payment::where('gateway_payment_id', $paypalOrderId)->first();
        if (!$payment) {
            Log::warning('Webhook PAYMENT.CAPTURE.DENIED: Payment not found.', ['paypal_order_id' => $paypalOrderId, 'eventId' => $eventId]);
            return ['success' => false, 'message' => 'Payment not found for PayPal Order ID: ' . $paypalOrderId];
        }

        $denialReason = $resource['status_details']['reason'] ?? 'Unknown';
        $payment->update([
            'status' => 'failed',
            // gateway_transaction_id might not be set if capture was never successful. $captureAttemptId is for the denied attempt.
            'processed_at' => now(),
            'metadata' => array_merge($payment->metadata ?? [], [
                'paypal_webhook_event_id' => $eventId, 'paypal_capture_denied_details' => $resource,
                'denial_reason' => $denialReason, 'denied_capture_attempt_id' => $captureAttemptId, 'source' => 'webhook',
            ]),
        ]);

        $order = $payment->order;
        if ($order && !in_array($order->status, ['failed', 'payment_failed', 'cancelled'])) {
            $order->update(['status' => 'payment_failed', 'notes' => ($order->notes ? $order->notes . "\n" : '') . "Payment capture denied by PayPal. Reason: {$denialReason}."]);
        }
        return ['success' => true, 'message' => 'Payment capture denied event processed.'];
    }

    protected function handlePaymentCaptureRefunded(array $resource, ?string $eventId = null): array
    {
        $refundId = $resource['id'] ?? null;
        $status = $resource['status'] ?? null;
        if ($status !== 'COMPLETED') {
            Log::info('Webhook PAYMENT.CAPTURE.REFUNDED: Non-COMPLETED refund status.', ['status' => $status, 'eventId' => $eventId]);
            return ['success' => true, 'message' => "Refund event with status {$status} acknowledged."];
        }
        
        $originalCaptureId = null;
        if (isset($resource['links'])) {
            foreach ($resource['links'] as $link) {
                if (isset($link['rel'], $link['href']) && $link['rel'] === 'up' && str_contains($link['href'], '/v2/payments/captures/')) {
                    $parts = explode('/', $link['href']);
                    $originalCaptureId = end($parts);
                    break;
                }
            }
        }

        if (!$refundId || !$originalCaptureId) {
            Log::error('Webhook PAYMENT.CAPTURE.REFUNDED: Missing refund_id or original_capture_id.', ['resource' => $resource, 'eventId' => $eventId]);
            return ['success' => false, 'message' => 'Invalid refund data: missing IDs.'];
        }

        $payment = Payment::where('gateway_transaction_id', $originalCaptureId)->first();
        if (!$payment) {
            Log::warning('Webhook PAYMENT.CAPTURE.REFUNDED: Payment not found for original capture ID.', ['original_capture_id' => $originalCaptureId, 'eventId' => $eventId]);
            return ['success' => false, 'message' => 'Payment not found for capture ID: ' . $originalCaptureId];
        }

        $refundAmount = (float)($resource['amount']['value'] ?? 0);
        $paymentAmount = (float)$payment->amount; // This is the original captured amount (or last updated amount)

        $currentTotalRefunded = (float)($payment->metadata['total_refunded_amount'] ?? 0);
        // Ensure we don't double-count if webhook is re-sent for the same refundId
        $alreadyProcessedRefund = false;
        if(isset($payment->metadata['refunds']) && is_array($payment->metadata['refunds'])) {
            foreach($payment->metadata['refunds'] as $prevRefund) {
                if(isset($prevRefund['id']) && $prevRefund['id'] === $refundId) {
                    $alreadyProcessedRefund = true;
                    break;
                }
            }
        }

        if($alreadyProcessedRefund) {
            Log::info('Webhook PAYMENT.CAPTURE.REFUNDED: Refund already processed.', ['refund_id' => $refundId, 'payment_id' => $payment->id]);
            return ['success' => true, 'message' => 'Refund already processed.'];
        }

        $newTotalRefunded = $currentTotalRefunded + $refundAmount;
        $newStatus = $payment->status;
        if (abs($newTotalRefunded - $paymentAmount) < 0.01) {
            $newStatus = 'refunded';
        } elseif ($newTotalRefunded > 0 && $newTotalRefunded < $paymentAmount) {
            $newStatus = 'partially_refunded';
        } // If $newTotalRefunded > $paymentAmount, it's an issue, but PayPal should prevent this.

        $refundsMetadata = $payment->metadata['refunds'] ?? [];
        $refundsMetadata[] = $resource;

        $payment->update([
            'status' => $newStatus,
            'metadata' => array_merge($payment->metadata ?? [], [
                'paypal_webhook_event_id' => $eventId, 'paypal_refund_details' => $resource, 'refunds' => $refundsMetadata,
                'total_refunded_amount' => $newTotalRefunded, 'last_refund_id_webhook' => $refundId, 'source' => 'webhook',
            ]),
        ]);

        $order = $payment->order;
        if ($order) {
            $orderStatus = ($newStatus === 'refunded') ? 'refunded' : 'partially_refunded';
            if ($order->status !== 'refunded') {
                 $order->update(['status' => $orderStatus]);
                 $order->notes = ($order->notes ? $order->notes . "\n" : '') . "Payment {$newStatus} via webhook: {$resource['amount']['currency_code']} {$refundAmount}. Refund ID: {$refundId}.";
                 $order->save();
            }
        }
        return ['success' => true, 'message' => 'Payment refund event processed.'];
    }

    protected function handlePaymentCapturePending(array $resource, ?string $eventId = null): array
    {
        $captureId = $resource['id'] ?? null;
        $paypalOrderId = null;
        if (isset($resource['links'])) {
            foreach ($resource['links'] as $link) {
                if (isset($link['rel'], $link['href']) && $link['rel'] === 'up' && str_contains($link['href'], '/v2/checkout/orders/')) {
                    $parts = explode('/', $link['href']);
                    $paypalOrderId = end($parts);
                    break;
                }
            }
        }
        $reason = $resource['status_details']['reason'] ?? 'Unknown reason';

        if (!$paypalOrderId) {
            Log::error('Webhook PAYMENT.CAPTURE.PENDING: Missing PayPal Order ID.', ['resource' => $resource, 'eventId' => $eventId]);
            return ['success' => false, 'message' => 'Invalid pending capture data: missing PayPal Order ID.'];
        }
        
        $payment = Payment::where('gateway_payment_id', $paypalOrderId)->first();
        if (!$payment) {
            Log::warning('Webhook PAYMENT.CAPTURE.PENDING: Payment not found.', ['paypal_order_id' => $paypalOrderId, 'eventId' => $eventId]);
            return ['success' => false, 'message' => 'Payment not found for PayPal Order ID: ' . $paypalOrderId];
        }

        if (!in_array($payment->status, ['completed', 'failed', 'cancelled', 'refunded', 'reversed'])) {
            $payment->update([
                'status' => 'pending', // Or a more specific 'capture_pending'
                'gateway_transaction_id' => $payment->gateway_transaction_id ?? $captureId, // This is capture_id, might not be final txn_id
                'metadata' => array_merge($payment->metadata ?? [], [
                    'paypal_webhook_event_id' => $eventId, 'paypal_capture_pending_details' => $resource,
                    'pending_reason' => $reason, 'source' => 'webhook',
                ]),
            ]);
            $order = $payment->order;
            if ($order && !in_array($order->status, ['pending', 'on_hold'])) {
                $order->update(['status' => 'pending', 'notes' => ($order->notes ? $order->notes . "\n" : '') . "Payment capture pending. Reason: {$reason}."]);
            }
        } else {
            Log::info('Webhook PAYMENT.CAPTURE.PENDING: Payment already in a final state.', ['payment_id' => $payment->id, 'status' => $payment->status]);
        }
        return ['success' => true, 'message' => 'Payment capture pending event processed.', 'pending_reason' => $reason];
    }

    protected function handlePaymentCaptureReversed(array $resource, ?string $eventId = null): array
    {
        $reversalId = $resource['id'] ?? null;
        $originalCaptureId = null;
        if (isset($resource['links'])) {
            foreach ($resource['links'] as $link) {
                if (isset($link['rel'], $link['href']) && $link['rel'] === 'up' && str_contains($link['href'], '/v2/payments/captures/')) {
                    $parts = explode('/', $link['href']);
                    $originalCaptureId = end($parts);
                    break;
                }
            }
        }
        $reason = $resource['status_details']['reason'] ?? 'Unknown reason';

        if (!$reversalId || !$originalCaptureId) {
            Log::error('Webhook PAYMENT.CAPTURE.REVERSED: Missing reversal_id or original_capture_id.', ['resource' => $resource, 'eventId' => $eventId]);
            return ['success' => false, 'message' => 'Invalid reversal data: missing IDs.'];
        }

        $payment = Payment::where('gateway_transaction_id', $originalCaptureId)->first();
        if (!$payment) {
            Log::warning('Webhook PAYMENT.CAPTURE.REVERSED: Payment not found for original capture ID.', ['original_capture_id' => $originalCaptureId, 'eventId' => $eventId]);
            return ['success' => false, 'message' => 'Payment not found for capture ID: ' . $originalCaptureId];
        }

        $payment->update([
            'status' => 'reversed',
            'processed_at' => now(),
            'metadata' => array_merge($payment->metadata ?? [], [
                'paypal_webhook_event_id' => $eventId, 'paypal_reversal_details' => $resource,
                'reversal_id' => $reversalId, 'reversal_reason' => $reason, 'source' => 'webhook',
            ]),
        ]);
        $order = $payment->order;
        if ($order) {
            $order->update(['status' => 'chargeback', 'notes' => ($order->notes ? $order->notes . "\n" : '') . "Payment reversed (e.g., chargeback). Reason: {$reason}. Reversal ID: {$reversalId}."]);
        }
        return ['success' => true, 'message' => 'Payment capture reversed event processed.', 'reversal_reason' => $reason];
    }

    protected function handleCheckoutOrderApproved(array $resource, ?string $eventId = null): array
    {
        $paypalOrderId = $resource['id'] ?? null;
        if (!$paypalOrderId) {
            Log::error('Webhook CHECKOUT.ORDER.APPROVED: Missing PayPal Order ID.', ['resource' => $resource, 'eventId' => $eventId]);
            return ['success' => false, 'message' => 'Invalid order approved data: missing PayPal Order ID.'];
        }

        $payment = Payment::where('gateway_payment_id', $paypalOrderId)->first();
        if (!$payment) {
            Log::warning('Webhook CHECKOUT.ORDER.APPROVED: Payment not found.', ['paypal_order_id' => $paypalOrderId, 'eventId' => $eventId]);
            return ['success' => false, 'message' => 'Payment not found for PayPal Order ID: ' . $paypalOrderId];
        }

        if ($payment->status === 'pending') {
            $payment->update([
                'metadata' => array_merge($payment->metadata ?? [], [
                    'paypal_webhook_event_id' => $eventId, 'paypal_order_approved_webhook' => $resource,
                    'payer_email' => $resource['payer']['email_address'] ?? null, 'source' => 'webhook_order_approved',
                ]),
            ]);
        } else {
             Log::info('Webhook CHECKOUT.ORDER.APPROVED: Payment not in pending state.', ['payment_id' => $payment->id, 'status' => $payment->status]);
        }
        return ['success' => true, 'message' => 'Checkout order approved event acknowledged.'];
    }

    protected function validatePaymentAmountBeforeCapture(Payment $payment, array $orderDetails): array
    {
        $order = $payment->order;
        $expectedAmount = (float) round($order->total, 2);
        $expectedCurrency = strtoupper($order->currency);

        $purchaseUnit = $orderDetails['purchase_units'][0] ?? null;
        if (!$purchaseUnit || !isset($purchaseUnit['amount'])) {
            Log::error('PayPal pre-capture validation: Missing purchase_units or amount data.', ['payment_id' => $payment->gateway_payment_id, 'orderDetails' => $orderDetails]);
            return ['success' => false, 'message' => 'Invalid PayPal order structure for pre-capture validation.'];
        }

        $paypalOrderAmount = (float) ($purchaseUnit['amount']['value'] ?? 0);
        $paypalOrderCurrency = strtoupper($purchaseUnit['amount']['currency_code'] ?? '');

        Log::info('PayPal pre-capture validation initiated.', [
            'payment_id' => $payment->gateway_payment_id, 'order_id' => $order->id,
            'expected_amount' => $expectedAmount, 'paypal_order_amount' => $paypalOrderAmount,
            'paypal_order_status' => $orderDetails['status'] ?? 'unknown',
        ]);

        if ($paypalOrderCurrency !== $expectedCurrency) {
            Log::error('PayPal pre-capture currency mismatch.', ['payment_id' => $payment->gateway_payment_id, 'expected' => $expectedCurrency, 'paypal_order' => $paypalOrderCurrency]);
            $payment->update(['status' => 'failed', 'metadata' => array_merge($payment->metadata ?? [], ['failure_reason' => 'pre_capture_currency_mismatch', 'expected_currency' => $expectedCurrency, 'paypal_order_currency' => $paypalOrderCurrency, 'validation_stage' => 'pre_capture'])]);
            if ($order) $order->update(['status' => 'payment_failed']);
            return ['success' => false, 'message' => 'Currency mismatch detected before capture.', 'error' => "Expected {$expectedCurrency}, PayPal order is for {$paypalOrderCurrency}."];
        }

        $isUnderpayment = $paypalOrderAmount < ($expectedAmount - 0.01);
        $isOverpayment = $paypalOrderAmount > ($expectedAmount + 0.01);

        if ($isUnderpayment) {
            Log::error('PayPal pre-capture underpayment detected. Full amount required.', ['payment_id' => $payment->gateway_payment_id, 'expected' => $expectedAmount, 'paypal_order' => $paypalOrderAmount]);
            $payment->update(['status' => 'failed', 'metadata' => array_merge($payment->metadata ?? [], ['failure_reason' => 'pre_capture_underpayment_not_allowed', 'expected_amount' => $expectedAmount, 'attempted_paypal_order_amount' => $paypalOrderAmount, 'validation_stage' => 'pre_capture'])]);
            if ($order) $order->update(['status' => 'payment_failed']);
            return ['success' => false, 'message' => 'Potential underpayment detected. Full payment amount required.', 'error' => "Expected {$expectedCurrency} {$expectedAmount}, PayPal order amount is {$paypalOrderCurrency} {$paypalOrderAmount}."];
        }

        if ($isOverpayment) {
            Log::warning('PayPal pre-capture overpayment detected. Will proceed with capture.', ['payment_id' => $payment->gateway_payment_id, 'expected' => $expectedAmount, 'paypal_order' => $paypalOrderAmount]);
        }

        return [
            'success' => true, 'message' => 'Pre-capture payment amount validation passed.',
            'paypal_order_amount' => $paypalOrderAmount, 'paypal_order_currency' => $paypalOrderCurrency,
            'is_overpayment' => $isOverpayment,
        ];
    }
}