<?php

namespace App\Services;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\ProductVariant;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OptimizedCartService extends CartService
{
    private const CACHE_TTL = 3600; // 1 hour
    private const CACHE_PREFIX = 'cart:';

    /**
     * Get cart by ID with optimized loading and caching.
     */
    public function getCartById(string $cartId): ?Cart
    {
        return Cache::remember(
            self::CACHE_PREFIX . $cartId,
            self::CACHE_TTL,
            function () use ($cartId) {
                return $this->getOptimizedCart($cartId);
            }
        );
    }

    /**
     * Get cart by session ID with caching.
     */
    public function getCartBySessionId(string $sessionId): ?Cart
    {
        $cacheKey = self::CACHE_PREFIX . 'session:' . $sessionId;
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($sessionId) {
            $cart = $this->model->where('session_id', $sessionId)->first();
            
            if ($cart) {
                return $this->getOptimizedCart($cart->id);
            }
            
            return null;
        });
    }

    /**
     * Get cart by user ID with caching.
     */
    public function getCartByUserId(string $userId): ?Cart
    {
        $cacheKey = self::CACHE_PREFIX . 'user:' . $userId;
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($userId) {
            $cart = $this->model->where('user_id', $userId)->first();
            
            if ($cart) {
                return $this->getOptimizedCart($cart->id);
            }
            
            return null;
        });
    }

    /**
     * Get optimized cart with selective eager loading.
     */
    private function getOptimizedCart(string $cartId): ?Cart
    {
        $cart = $this->model->with([
            'items' => function ($query) {
                $query->select([
                    'id', 'cart_id', 'product_variant_id', 
                    'quantity', 'unit_price', 'created_at', 'updated_at'
                ]);
            },
            'items.productVariant' => function ($query) {
                $query->select([
                    'id', 'product_id', 'sku', 'price', 'name', 'is_active'
                ]);
            },
            'items.productVariant.product' => function ($query) {
                $query->select([
                    'id', 'name', 'slug', 'is_active'
                ]);
            },
            'items.productVariant.inventoryItem' => function ($query) {
                $query->select([
                    'id', 'product_variant_id', 'quantity_on_hand', 
                    'quantity_reserved', 'track_inventory', 'allow_backorder'
                ]);
            }
        ])->find($cartId);

        return $cart instanceof Cart ? $cart : null;
    }

    /**
     * Add item to cart with cache invalidation.
     */
    public function addItemToCart(Cart $cart, string $variantId, int $quantity = 1): ?CartItem
    {
        $result = parent::addItemToCart($cart, $variantId, $quantity);
        
        if ($result) {
            $this->invalidateCartCache($cart);
        }
        
        return $result;
    }

    /**
     * Update cart item quantity with cache invalidation.
     */
    public function updateCartItemQuantity(CartItem $cartItem, int $quantity): CartItem
    {
        $result = parent::updateCartItemQuantity($cartItem, $quantity);
        
        $this->invalidateCartCache($cartItem->cart);
        
        return $result;
    }

    /**
     * Remove item from cart with cache invalidation.
     */
    public function removeItemFromCart(CartItem $cartItem): bool
    {
        $cart = $cartItem->cart;
        $result = parent::removeItemFromCart($cartItem);
        
        if ($result) {
            $this->invalidateCartCache($cart);
        }
        
        return $result;
    }

    /**
     * Clear cart with cache invalidation.
     */
    public function clearCart(Cart $cart): bool
    {
        $result = parent::clearCart($cart);
        
        if ($result) {
            $this->invalidateCartCache($cart);
        }
        
        return $result;
    }

    /**
     * Invalidate all cache entries for a cart.
     */
    private function invalidateCartCache(Cart $cart): void
    {
        $keys = [
            self::CACHE_PREFIX . $cart->id,
        ];

        if ($cart->user_id) {
            $keys[] = self::CACHE_PREFIX . 'user:' . $cart->user_id;
        }

        if ($cart->session_id) {
            $keys[] = self::CACHE_PREFIX . 'session:' . $cart->session_id;
        }

        Cache::forget($keys);
    }

    /**
     * Get cart for display with minimal data loading.
     */
    public function getCartForDisplay(string $cartId): ?Cart
    {
        return Cache::remember(
            self::CACHE_PREFIX . 'display:' . $cartId,
            self::CACHE_TTL,
            function () use ($cartId) {
                return $this->model->with([
                    'items:id,cart_id,product_variant_id,quantity,unit_price',
                    'items.productVariant:id,product_id,sku,price,name',
                    'items.productVariant.product:id,name,slug'
                ])->find($cartId);
            }
        );
    }

    /**
     * Get cart summary for header/mini-cart display.
     */
    public function getCartSummary(string $cartId): array
    {
        return Cache::remember(
            self::CACHE_PREFIX . 'summary:' . $cartId,
            self::CACHE_TTL,
            function () use ($cartId) {
                $cart = $this->model->withCount('items')
                    ->find($cartId);

                if (!$cart) {
                    return [
                        'total_items' => 0,
                        'subtotal' => 0,
                        'formatted_subtotal' => '$0.00'
                    ];
                }

                $subtotal = $cart->items()->sum(DB::raw('quantity * unit_price'));

                return [
                    'total_items' => $cart->items_count,
                    'subtotal' => $subtotal,
                    'formatted_subtotal' => '$' . number_format($subtotal, 2)
                ];
            }
        );
    }

    /**
     * Validate cart items for price changes and availability.
     */
    public function validateCart(Cart $cart): array
    {
        $issues = [];
        
        foreach ($cart->items as $item) {
            $variant = $item->productVariant;
            
            // Check if product/variant is still active
            if (!$variant->is_active || !$variant->product->is_active) {
                $issues[] = [
                    'type' => 'unavailable',
                    'item_id' => $item->id,
                    'message' => "'{$variant->product->name}' is no longer available"
                ];
                continue;
            }
            
            // Check for price changes
            if ($item->unit_price != $variant->price) {
                $issues[] = [
                    'type' => 'price_change',
                    'item_id' => $item->id,
                    'old_price' => $item->unit_price,
                    'new_price' => $variant->price,
                    'message' => "Price changed for '{$variant->product->name}'"
                ];
            }
            
            // Check inventory availability
            if ($variant->inventoryItem && $variant->inventoryItem->track_inventory) {
                $available = $variant->inventoryItem->quantity_on_hand - $variant->inventoryItem->quantity_reserved;
                
                if ($available < $item->quantity) {
                    $issues[] = [
                        'type' => 'insufficient_stock',
                        'item_id' => $item->id,
                        'requested' => $item->quantity,
                        'available' => max(0, $available),
                        'message' => "Only {$available} units available for '{$variant->product->name}'"
                    ];
                }
            }
        }
        
        return $issues;
    }

    /**
     * Get recently viewed products for cart recommendations.
     */
    public function getCartRecommendations(Cart $cart, int $limit = 4): array
    {
        if ($cart->items->isEmpty()) {
            return [];
        }

        // Get categories of items in cart
        $categoryIds = $cart->items->map(function ($item) {
            return $item->productVariant->product->category_id;
        })->unique()->values()->toArray();

        // Get product IDs already in cart
        $cartProductIds = $cart->items->map(function ($item) {
            return $item->productVariant->product_id;
        })->toArray();

        // Find related products
        $recommendations = \App\Models\Product::whereIn('category_id', $categoryIds)
            ->whereNotIn('id', $cartProductIds)
            ->where('is_active', true)
            ->with(['variants' => function ($query) {
                $query->where('is_active', true)->orderBy('price')->limit(1);
            }])
            ->inRandomOrder()
            ->limit($limit)
            ->get();

        return $recommendations->toArray();
    }

    /**
     * Warm up cart cache after login.
     */
    public function warmCartCache(User $user): void
    {
        $cart = $this->getCartByUserId($user->id);
        
        if ($cart) {
            // Pre-load display and summary data
            $this->getCartForDisplay($cart->id);
            $this->getCartSummary($cart->id);
        }
    }
}
