<?php

namespace App\Services;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\ProductVariant;
use Illuminate\Support\Facades\Log;

class CartValidationService
{
    /**
     * Validate entire cart for issues.
     */
    public function validateCart(Cart $cart): array
    {
        $issues = [];
        
        foreach ($cart->items as $item) {
            $itemIssues = $this->validateCartItem($item);
            $issues = array_merge($issues, $itemIssues);
        }
        
        return [
            'valid' => empty($issues),
            'issues' => $issues,
            'summary' => $this->generateValidationSummary($issues)
        ];
    }

    /**
     * Validate individual cart item.
     */
    public function validateCartItem(CartItem $item): array
    {
        $issues = [];
        $variant = $item->productVariant;
        
        if (!$variant) {
            $issues[] = [
                'type' => 'variant_not_found',
                'severity' => 'error',
                'item_id' => $item->id,
                'message' => 'Product variant no longer exists',
                'action' => 'remove'
            ];
            return $issues;
        }

        $product = $variant->product;
        
        if (!$product) {
            $issues[] = [
                'type' => 'product_not_found',
                'severity' => 'error',
                'item_id' => $item->id,
                'message' => 'Product no longer exists',
                'action' => 'remove'
            ];
            return $issues;
        }

        // Check product availability
        if (!$product->is_active) {
            $issues[] = [
                'type' => 'product_inactive',
                'severity' => 'error',
                'item_id' => $item->id,
                'product_name' => $product->getTranslation('name', app()->getLocale()),
                'message' => "'{$product->getTranslation('name', app()->getLocale())}' is no longer available",
                'action' => 'remove'
            ];
        }

        // Check variant availability
        if (!$variant->is_active) {
            $issues[] = [
                'type' => 'variant_inactive',
                'severity' => 'error',
                'item_id' => $item->id,
                'product_name' => $product->getTranslation('name', app()->getLocale()),
                'variant_name' => $variant->getTranslation('name', app()->getLocale()),
                'message' => "This variant of '{$product->getTranslation('name', app()->getLocale())}' is no longer available",
                'action' => 'remove'
            ];
        }

        // Check for price changes
        if ($item->unit_price != $variant->price) {
            $priceDifference = $variant->price - $item->unit_price;
            $percentageChange = ($priceDifference / $item->unit_price) * 100;
            
            $issues[] = [
                'type' => 'price_change',
                'severity' => abs($percentageChange) > 10 ? 'warning' : 'info',
                'item_id' => $item->id,
                'product_name' => $product->getTranslation('name', app()->getLocale()),
                'old_price' => $item->unit_price,
                'new_price' => $variant->price,
                'difference' => $priceDifference,
                'percentage_change' => round($percentageChange, 2),
                'message' => $priceDifference > 0 
                    ? "Price increased by $" . number_format($priceDifference, 2)
                    : "Price decreased by $" . number_format(abs($priceDifference), 2),
                'action' => 'update_price'
            ];
        }

        // Check inventory availability
        $inventoryIssues = $this->validateInventory($item, $variant);
        $issues = array_merge($issues, $inventoryIssues);

        return $issues;
    }

    /**
     * Validate inventory for cart item.
     */
    private function validateInventory(CartItem $item, ProductVariant $variant): array
    {
        $issues = [];
        $inventory = $variant->inventoryItem;
        
        if (!$inventory) {
            // No inventory tracking
            return $issues;
        }

        if (!$inventory->track_inventory) {
            // Inventory tracking disabled
            return $issues;
        }

        $availableQuantity = $inventory->quantity_on_hand - $inventory->quantity_reserved;
        $product = $variant->product;

        if ($availableQuantity < $item->quantity) {
            if ($availableQuantity <= 0) {
                $issues[] = [
                    'type' => 'out_of_stock',
                    'severity' => 'error',
                    'item_id' => $item->id,
                    'product_name' => $product->getTranslation('name', app()->getLocale()),
                    'requested_quantity' => $item->quantity,
                    'available_quantity' => 0,
                    'message' => "'{$product->getTranslation('name', app()->getLocale())}' is out of stock",
                    'action' => $inventory->allow_backorder ? 'backorder' : 'remove'
                ];
            } else {
                $issues[] = [
                    'type' => 'insufficient_stock',
                    'severity' => 'warning',
                    'item_id' => $item->id,
                    'product_name' => $product->getTranslation('name', app()->getLocale()),
                    'requested_quantity' => $item->quantity,
                    'available_quantity' => $availableQuantity,
                    'message' => "Only {$availableQuantity} units available for '{$product->getTranslation('name', app()->getLocale())}'",
                    'action' => 'reduce_quantity'
                ];
            }
        }

        // Check for low stock warning
        if ($inventory->low_stock_threshold && $availableQuantity <= $inventory->low_stock_threshold) {
            $issues[] = [
                'type' => 'low_stock',
                'severity' => 'info',
                'item_id' => $item->id,
                'product_name' => $product->getTranslation('name', app()->getLocale()),
                'available_quantity' => $availableQuantity,
                'message' => "Only {$availableQuantity} units left in stock",
                'action' => 'none'
            ];
        }

        return $issues;
    }

    /**
     * Generate validation summary.
     */
    private function generateValidationSummary(array $issues): array
    {
        $summary = [
            'total_issues' => count($issues),
            'errors' => 0,
            'warnings' => 0,
            'info' => 0,
            'actions_required' => []
        ];

        foreach ($issues as $issue) {
            $summary[$issue['severity']]++;
            
            if (!in_array($issue['action'], $summary['actions_required'])) {
                $summary['actions_required'][] = $issue['action'];
            }
        }

        return $summary;
    }

    /**
     * Auto-fix cart issues where possible.
     */
    public function autoFixCart(Cart $cart): array
    {
        $validation = $this->validateCart($cart);
        $fixed = [];
        $failed = [];

        if ($validation['valid']) {
            return ['fixed' => $fixed, 'failed' => $failed];
        }

        foreach ($validation['issues'] as $issue) {
            try {
                switch ($issue['action']) {
                    case 'update_price':
                        $this->updateItemPrice($issue['item_id'], $issue['new_price']);
                        $fixed[] = $issue;
                        break;
                        
                    case 'reduce_quantity':
                        $this->updateItemQuantity($issue['item_id'], $issue['available_quantity']);
                        $fixed[] = $issue;
                        break;
                        
                    case 'remove':
                        $this->removeItem($issue['item_id']);
                        $fixed[] = $issue;
                        break;
                        
                    default:
                        // No auto-fix available
                        $failed[] = $issue;
                        break;
                }
            } catch (\Exception $e) {
                Log::error('Failed to auto-fix cart issue', [
                    'issue' => $issue,
                    'error' => $e->getMessage()
                ]);
                $failed[] = $issue;
            }
        }

        return ['fixed' => $fixed, 'failed' => $failed];
    }

    /**
     * Update cart item price.
     */
    private function updateItemPrice(string $itemId, float $newPrice): void
    {
        CartItem::where('id', $itemId)->update(['unit_price' => $newPrice]);
    }

    /**
     * Update cart item quantity.
     */
    private function updateItemQuantity(string $itemId, int $newQuantity): void
    {
        CartItem::where('id', $itemId)->update(['quantity' => $newQuantity]);
    }

    /**
     * Remove cart item.
     */
    private function removeItem(string $itemId): void
    {
        CartItem::where('id', $itemId)->delete();
    }

    /**
     * Check if cart needs validation (based on last update time).
     */
    public function needsValidation(Cart $cart): bool
    {
        // Validate if cart hasn't been validated in the last 5 minutes
        return $cart->updated_at < now()->subMinutes(5);
    }

    /**
     * Get user-friendly validation messages.
     */
    public function getValidationMessages(array $issues): array
    {
        $messages = [];
        
        foreach ($issues as $issue) {
            switch ($issue['type']) {
                case 'price_change':
                    if ($issue['difference'] > 0) {
                        $messages[] = [
                            'type' => 'warning',
                            'message' => "The price of {$issue['product_name']} has increased by $" . number_format($issue['difference'], 2)
                        ];
                    } else {
                        $messages[] = [
                            'type' => 'success',
                            'message' => "Good news! The price of {$issue['product_name']} has decreased by $" . number_format(abs($issue['difference']), 2)
                        ];
                    }
                    break;
                    
                case 'insufficient_stock':
                    $messages[] = [
                        'type' => 'warning',
                        'message' => "We only have {$issue['available_quantity']} units of {$issue['product_name']} available. Your cart has been updated."
                    ];
                    break;
                    
                case 'out_of_stock':
                    $messages[] = [
                        'type' => 'error',
                        'message' => "{$issue['product_name']} is currently out of stock and has been removed from your cart."
                    ];
                    break;
                    
                case 'product_inactive':
                case 'variant_inactive':
                    $messages[] = [
                        'type' => 'error',
                        'message' => "{$issue['product_name']} is no longer available and has been removed from your cart."
                    ];
                    break;
            }
        }
        
        return $messages;
    }
}
