<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Illuminate\Database\Eloquent\Collection;

class ProductController extends Controller
{
    /**
     * Display a listing of the products.
     */
    public function index(Request $request): View
    {
        try {
            $query = Product::with(['category', 'variants.inventoryItem', 'media'])
                ->where('products.is_active', true);

            // Filter by category if provided and not empty
            if ($request->has('category') && !empty($request->input('category'))) {
                $categoryId = (string) $request->input('category');
                $query->whereRaw("CAST(products.category_id AS TEXT) = ?", [$categoryId]);
            }

            // Filter by search term if provided
            if ($request->has('search') && !empty($request->input('search'))) {
                $search = $request->input('search');
                $searchPattern = '%' . $search . '%';

                $query->where(function($q) use ($searchPattern) {
                    $q->whereRaw("(products.name->>'en')::text ILIKE ?", [$searchPattern])
                      ->orWhereRaw("(products.description->>'en')::text ILIKE ?", [$searchPattern])
                      ->orWhere('products.code', 'ILIKE', $searchPattern);
                });
            }

            // Filter by price range if provided
            if ($request->has('min_price') && !empty($request->input('min_price'))) {
                $minPrice = (float) $request->input('min_price');
                $query->whereHas('variants', function($q) use ($minPrice) {
                    $q->where('price', '>=', $minPrice);
                });
            }

            if ($request->has('max_price') && !empty($request->input('max_price'))) {
                $maxPrice = (float) $request->input('max_price');
                $query->whereHas('variants', function($q) use ($maxPrice) {
                    $q->where('price', '<=', $maxPrice);
                });
            }

            // Filter by availability
            if ($request->has('in_stock') && $request->input('in_stock') === '1') {
                $query->whereHas('variants.inventoryItem', function($q) {
                    $q->where('quantity_on_hand', '>', 0)
                      ->orWhere('allow_backorder', true);
                });
            }

            // Sort products
            $sortBy = $request->input('sort', 'newest');

        switch ($sortBy) {
            case 'price_low':
                // Join with variants to sort by minimum price
                $query->join('product_variants', 'products.id', '=', 'product_variants.product_id')
                      ->select('products.*')
                      ->groupBy('products.id')
                      ->orderBy(DB::raw('MIN(product_variants.price)'), 'asc');
                break;
            case 'price_high':
                // Join with variants to sort by maximum price
                $query->join('product_variants', 'products.id', '=', 'product_variants.product_id')
                      ->select('products.*')
                      ->groupBy('products.id')
                      ->orderBy(DB::raw('MAX(product_variants.price)'), 'desc');
                break;
            case 'name':
                // Sort by name using PostgreSQL's JSON operators with explicit casting
                $query->orderByRaw("(products.name->>'en')::text ASC");
                break;
            case 'newest':
            default:
                $query->orderBy('products.created_at', 'desc');
                break;
        }

        // Featured products first (only if not sorting by price)
        if (!in_array($sortBy, ['price_low', 'price_high'])) {
            $query->orderBy('products.is_featured', 'desc');
        }

            // Get products per page from request or default to 12
            $perPage = $request->input('per_page', 12);
            $perPage = in_array($perPage, [12, 24, 48]) ? $perPage : 12;

            $products = $query->paginate($perPage)->withQueryString();

            // Get all categories for the filter sidebar
            $categories = Category::where('parent_id', null)
                ->with('children')
                ->orderBy('name->en')
                ->get();

            // Get price range for filters
            $priceRange = $this->getPriceRange();

            // Get current user's wishlist items if authenticated
            $wishlistItems = [];
            if (Auth::check()) {
                $wishlistItems = Auth::user()->wishlistItems()->pluck('product_id')->toArray();
            }

            return view('store.products.index', [
                'products' => $products,
                'categories' => $categories,
                'currentCategory' => $request->input('category'),
                'search' => $request->input('search'),
                'sortBy' => $sortBy,
                'minPrice' => $request->input('min_price'),
                'maxPrice' => $request->input('max_price'),
                'inStock' => $request->input('in_stock'),
                'perPage' => $perPage,
                'priceRange' => $priceRange,
                'wishlistItems' => $wishlistItems,
                'totalProducts' => $products->total(),
            ]);
        } catch (\Exception $e) {
            Log::error('Error loading products page', [
                'error' => $e->getMessage(),
                'request' => $request->except(['_token', 'password']),
                'trace' => $e->getTraceAsString(),
            ]);

            // Return an empty result set in case of error
            return view('store.products.index', [
                'products' => collect([])->paginate(12),
                'categories' => collect([]),
                'search' => $request->input('search'),
                'sortBy' => 'newest',
            ])->with('error', 'We encountered an issue while loading products. Please try again or contact support if the problem persists.');
        }
    }

    /**
     * Display the specified product with optimized caching and queries.
     */
    public function show(string $slug): View
    {
        if (empty($slug)) {
            abort(404, 'The requested product was not found.');
        }

        Log::info('Product show method called', ['slug' => $slug]);

        try {
            // Cache the product query for 30 minutes
            $cacheKey = "product_detail_{$slug}";
            $product = Cache::remember($cacheKey, 1800, function () use ($slug) {
                return Product::whereRaw("(products.slug->>'en')::text = ?", [$slug])
                    ->with([
                        'category:id,name,slug',
                        'variants' => function ($query) {
                            $query->where('is_active', true)
                                  ->with('inventoryItem:id,product_variant_id,quantity_on_hand,quantity_reserved,track_inventory,allow_backorder')
                                  ->orderBy('price');
                        }
                    ])
                    ->where('is_active', true)
                    ->first();
            });

            if (!$product) {
                return $this->handleProductNotFound($slug);
            }

            Log::info('Found product by slug', ['slug' => $slug, 'product_id' => $product->id]);

            // Calculate stock status
            $totalQuantityOnHand = $product->variants->sum(function ($variant) {
                return $variant->inventoryItem ? $variant->inventoryItem->quantity_on_hand : 0;
            });

            $allowBackorder = $product->variants->contains(function ($variant) {
                return $variant->inventoryItem ? $variant->inventoryItem->allow_backorder : false;
            });

            $isInStock = $totalQuantityOnHand > 0 || $allowBackorder;
            $stockQuantity = $totalQuantityOnHand;
            $stockMessage = '';

            if ($isInStock) {
                if ($allowBackorder && $totalQuantityOnHand <= 0) {
                    $stockMessage = 'Available for backorder';
                } elseif ($totalQuantityOnHand > 10) {
                    $stockMessage = 'In stock';
                } elseif ($totalQuantityOnHand > 0) {
                    $stockMessage = 'Only ' . $totalQuantityOnHand . ' left';
                } else {
                    $stockMessage = 'Out of stock'; // Should not happen if isInStock is true, but as a fallback
                }
            } else {
                $stockMessage = 'Out of stock';
            }

            // Get related products with caching
            $relatedProducts = $this->getRelatedProducts($product);

            return view('store.products.show', [
                'product' => $product,
                'relatedProducts' => $relatedProducts,
                'isInStock' => $isInStock,
                'stockQuantity' => $stockQuantity,
                'stockMessage' => $stockMessage,
            ]);
        } catch (\Exception $e) {
            Log::error('Error loading product page', [
                'slug' => $slug,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return view('errors.500', [
                'message' => 'We\'re experiencing technical difficulties. Please try again later.',
            ]);
        }
    }

    /**
     * Handle product not found scenario with cached similar products.
     */
    private function handleProductNotFound(string $slug): View
    {
        Log::warning('Product not found', [
            'slug' => $slug,
            'ip' => request()->ip(),
        ]);

        // Cache similar products for better performance
        $similarProducts = Cache::remember('similar_products_' . md5($slug), 300, function () {
            return Product::where('is_active', true)
                ->with('category:id,name')
                ->inRandomOrder()
                ->take(4)
                ->get(['id', 'name', 'slug', 'category_id']);
        });

        return view('errors.404', [
            'message' => 'We\'re sorry, but the product you\'re looking for cannot be found. It may have been moved or is no longer available.',
            'similarProducts' => $similarProducts,
        ]);
    }

    /**
     * Get related products with caching for improved performance.
     */
    private function getRelatedProducts(Product $product): Collection
    {
        if (!$product->category) {
            return collect([]);
        }

        $cacheKey = "related_products_{$product->id}_{$product->category_id}";

        return Cache::remember($cacheKey, 1800, function () use ($product) {
            return $product->category->products()
                ->where('id', '!=', $product->id)
                ->where('is_active', true)
                ->with('variants:id,product_id,price')
                ->inRandomOrder()
                ->take(4)
                ->get(['id', 'name', 'slug', 'category_id']);
        });
    }

    /**
     * Get price range for filtering.
     */
    private function getPriceRange(): array
    {
        $priceStats = DB::table('product_variants')
            ->join('products', 'product_variants.product_id', '=', 'products.id')
            ->where('products.is_active', true)
            ->where('product_variants.is_active', true)
            ->selectRaw('MIN(product_variants.price) as min_price, MAX(product_variants.price) as max_price')
            ->first();

        return [
            'min' => $priceStats->min_price ?? 0,
            'max' => $priceStats->max_price ?? 1000,
        ];
    }
}
