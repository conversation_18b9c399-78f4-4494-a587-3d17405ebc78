<?php

namespace App\Http\Controllers;

use App\Http\Requests\WishlistRequest;
use App\Models\Product;
use App\Models\Wishlist;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class WishlistController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the user's wishlist.
     */
    public function index(): View
    {
        $user = Auth::user();

        // Get wishlist items with product details and media
        $wishlistItems = $user->wishlistItems()
            ->with([
                'category:id,name',
                'variants' => function ($query) {
                    $query->select('id', 'product_id', 'price', 'compare_at_price')
                          ->orderBy('price');
                },
                'media'
            ])
            ->where('is_active', true)
            ->paginate(12);

        // Calculate statistics
        $totalItems = $user->wishlists()->count();
        $totalValue = $this->calculateWishlistValue($user->id);

        return view('wishlist.index', compact('wishlistItems', 'totalItems', 'totalValue'));
    }

    /**
     * Add a product to the wishlist.
     */
    public function store(WishlistRequest $request): JsonResponse
    {

        $user = Auth::user();
        $productId = $request->input('product_id');

        try {
            DB::beginTransaction();

            // Check if product exists and is active
            $product = Product::where('id', $productId)
                             ->where('is_active', true)
                             ->first();

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product not found or is not available.',
                ], 404);
            }

            // Check if already in wishlist
            if ($user->hasInWishlist($productId)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product is already in your wishlist.',
                    'in_wishlist' => true,
                ], 409);
            }

            // Add to wishlist
            $user->addToWishlist($productId);

            // Clear cache
            $this->clearWishlistCache($user->id);

            DB::commit();

            // Log the action
            Log::info('Product added to wishlist', [
                'user_id' => $user->id,
                'product_id' => $productId,
                'product_name' => $product->getTranslation('name', app()->getLocale()),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product added to your wishlist!',
                'in_wishlist' => true,
                'wishlist_count' => $user->wishlists()->count(),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to add product to wishlist', [
                'user_id' => $user->id,
                'product_id' => $productId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to add product to wishlist. Please try again.',
            ], 500);
        }
    }

    /**
     * Remove a product from the wishlist.
     */
    public function destroy(string $productId): JsonResponse
    {
        $user = Auth::user();

        try {
            DB::beginTransaction();

            // Check if product is in wishlist
            if (!$user->hasInWishlist($productId)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product is not in your wishlist.',
                    'in_wishlist' => false,
                ], 404);
            }

            // Remove from wishlist
            $user->removeFromWishlist($productId);

            // Clear cache
            $this->clearWishlistCache($user->id);

            DB::commit();

            // Log the action
            Log::info('Product removed from wishlist', [
                'user_id' => $user->id,
                'product_id' => $productId,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product removed from your wishlist.',
                'in_wishlist' => false,
                'wishlist_count' => $user->wishlists()->count(),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to remove product from wishlist', [
                'user_id' => $user->id,
                'product_id' => $productId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to remove product from wishlist. Please try again.',
            ], 500);
        }
    }

    /**
     * Toggle a product in the wishlist.
     */
    public function toggle(WishlistRequest $request): JsonResponse
    {

        $user = Auth::user();
        $productId = $request->input('product_id');

        try {
            DB::beginTransaction();

            // Check if product exists and is active
            $product = Product::where('id', $productId)
                             ->where('is_active', true)
                             ->first();

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product not found or is not available.',
                ], 404);
            }

            // Toggle wishlist
            $result = $user->toggleWishlist($productId);

            // Clear cache
            $this->clearWishlistCache($user->id);

            DB::commit();

            // Log the action
            Log::info('Product wishlist toggled', [
                'user_id' => $user->id,
                'product_id' => $productId,
                'action' => $result['action'],
                'product_name' => $product->getTranslation('name', app()->getLocale()),
            ]);

            $message = $result['action'] === 'added'
                ? 'Product added to your wishlist!'
                : 'Product removed from your wishlist.';

            return response()->json([
                'success' => true,
                'message' => $message,
                'action' => $result['action'],
                'in_wishlist' => $result['in_wishlist'],
                'wishlist_count' => $user->wishlists()->count(),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to toggle product in wishlist', [
                'user_id' => $user->id,
                'product_id' => $productId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update wishlist. Please try again.',
            ], 500);
        }
    }

    /**
     * Get wishlist status for a product.
     */
    public function status(string $productId): JsonResponse
    {
        $user = Auth::user();

        $inWishlist = $user->hasInWishlist($productId);

        return response()->json([
            'in_wishlist' => $inWishlist,
            'wishlist_count' => $user->wishlists()->count(),
        ]);
    }

    /**
     * Clear all items from the wishlist.
     */
    public function clear(): JsonResponse
    {
        $user = Auth::user();

        try {
            DB::beginTransaction();

            $count = $user->wishlists()->count();
            $user->wishlistItems()->detach();

            // Clear cache
            $this->clearWishlistCache($user->id);

            DB::commit();

            Log::info('Wishlist cleared', [
                'user_id' => $user->id,
                'items_removed' => $count,
            ]);

            return response()->json([
                'success' => true,
                'message' => "Removed {$count} items from your wishlist.",
                'wishlist_count' => 0,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to clear wishlist', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to clear wishlist. Please try again.',
            ], 500);
        }
    }

    /**
     * Calculate the total value of items in the wishlist.
     */
    private function calculateWishlistValue(string $userId): float
    {
        return Cache::remember("wishlist_value_{$userId}", 300, function () use ($userId) {
            $wishlistItems = Wishlist::where('user_id', $userId)
                ->join('products', 'wishlists.product_id', '=', 'products.id')
                ->where('products.is_active', true)
                ->pluck('wishlists.product_id');

            if ($wishlistItems->isEmpty()) {
                return 0.0;
            }

            // Get minimum price for each product
            $totalValue = 0.0;
            foreach ($wishlistItems as $productId) {
                $minPrice = DB::table('product_variants')
                    ->where('product_id', $productId)
                    ->min('price');

                if ($minPrice) {
                    $totalValue += (float) $minPrice;
                }
            }

            return $totalValue;
        });
    }

    /**
     * Clear wishlist-related cache for a user.
     */
    private function clearWishlistCache(string $userId): void
    {
        Cache::forget("wishlist_value_{$userId}");
        Cache::forget("wishlist_count_{$userId}");
    }
}
