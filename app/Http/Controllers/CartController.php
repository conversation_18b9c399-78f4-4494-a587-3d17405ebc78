<?php

namespace App\Http\Controllers;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\ProductVariant;
use App\Services\CartService;
use App\Services\OptimizedCartService;
use App\Services\CartValidationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\AddToCartRequest;

class CartController extends Controller
{
    protected OptimizedCartService $cartService;
    protected CartValidationService $validationService;

    /**
     * Create a new controller instance.
     */
    public function __construct(OptimizedCartService $cartService, CartValidationService $validationService)
    {
        $this->cartService = $cartService;
        $this->validationService = $validationService;
    }

    /**
     * Display the cart.
     */
    public function index(): View
    {
        $cart = $this->getCurrentCart();

        // Validate cart if it has items
        $validation = null;
        $validationMessages = [];

        if (!$cart->items->isEmpty()) {
            $validation = $this->validationService->validateCart($cart);

            if (!$validation['valid']) {
                // Auto-fix issues where possible
                $autoFix = $this->validationService->autoFixCart($cart);

                // Refresh cart after auto-fixes
                $cart = $cart->fresh(['items.productVariant.product']);

                // Get user-friendly messages for remaining issues
                if (!empty($autoFix['failed'])) {
                    $validationMessages = $this->validationService->getValidationMessages($autoFix['failed']);
                }

                // Log validation issues for analytics
                if (!empty($autoFix['fixed']) || !empty($autoFix['failed'])) {
                    Log::info('Cart validation issues found', [
                        'cart_id' => $cart->id,
                        'user_id' => $cart->user_id,
                        'fixed_issues' => count($autoFix['fixed']),
                        'failed_issues' => count($autoFix['failed'])
                    ]);
                }
            }
        }

        return view('store.cart.index', [
            'cart' => $cart,
            'validation' => $validation,
            'validationMessages' => $validationMessages,
        ]);
    }

    /**
     * Add a product to the cart.
     */
    public function add(AddToCartRequest $request): RedirectResponse|JsonResponse
    {
        $validated = $request->validated();

        $variant = ProductVariant::findOrFail($validated['product_variant_id']);

        $buyNow = $request->has('buy_now') && $request->input('buy_now') == '1';

        // Check if the variant is active
        if (!$variant->is_active) {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'We\'re sorry, but this product is currently unavailable. Please check back later or browse our other products.',
                ], 400);
            }

            return redirect()->back()->with('error', 'We\'re sorry, but this product is currently unavailable. Please check back later or browse our other products.');
        }

        // Check inventory if tracking is enabled
        if ($variant->inventoryItem && $variant->inventoryItem->track_inventory) {
            $availableQuantity = $variant->inventoryItem->quantity_on_hand - $variant->inventoryItem->quantity_reserved;

            if ($availableQuantity < $validated['quantity'] && !$variant->inventoryItem->allow_backorder) {
                if ($request->wantsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'We only have ' . $availableQuantity . ' of this item in stock. Please adjust your quantity or contact us for restock information.',
                    ], 400);
                }

                return redirect()->back()->with('error', 'We only have ' . $availableQuantity . ' of this item in stock. Please adjust your quantity or contact us for restock information.');
            }
        }

        try {
            // Get or create cart
            $cart = $this->getCurrentCart();

            // Check if the item is already in the cart
            /** @var CartItem|null $existingItem */
            $existingItem = $cart->items()->where('product_variant_id', $variant->id)->first();

            if ($existingItem) {
                // Update quantity
                $this->cartService->updateCartItemQuantity(
                    $existingItem,
                    $existingItem->quantity + $validated['quantity']
                );

                $message = 'Item quantity updated in your cart.';
            } else {
                // Add new item
                $this->cartService->addItemToCart(
                    $cart,
                    $variant->id, // Pass the variant ID, not the entire variant object
                    $validated['quantity']
                );

                $message = 'Item added to your cart.';
            }

            // Refresh cart data
            $freshCart = $cart->fresh();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'cart' => [
                        'total_items' => $freshCart->total_items,
                        'subtotal' => $freshCart->subtotal,
                    ],
                    'redirect_to_checkout' => $buyNow,
                    'checkout_url' => $buyNow ? route('checkout.index') : null,
                ]);
            }

            // If buy_now is true, redirect to checkout
            if ($buyNow) {
                return redirect()->route('checkout.index')->with('success', $message);
            }

            return redirect()->back()->with('success', $message);
        } catch (\Exception $e) {
            // Log the error for debugging purposes
            Log::error('Failed to add item to cart: ' . $e->getMessage());
            // Log::debug($e);
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'We couldn\'t add this item to your cart. Please try again or contact support if the issue persists.',
                ], 500);
            }

            return redirect()->back()->with('error', 'We couldn\'t add this item to your cart. Please try again or contact support if the issue persists.');
        }
    }

    /**
     * Update cart item quantity.
     */
    public function update(Request $request, CartItem $cartItem): RedirectResponse|JsonResponse
    {
        $validated = $request->validate([
            'quantity' => 'required|integer|min:0|max:100',
        ]);

        // Verify that the cart item belongs to the current cart
        $cart = $this->getCurrentCart();

        if ($cartItem->cart_id !== $cart->id) {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'We couldn\'t find the specified item in your cart. It may have been removed. Please refresh your cart and try again..',
                ], 404);
            }

            return redirect()->route('cart.index')->with('error', 'We couldn\'t find the specified item in your cart. It may have been removed. Please refresh your cart and try again..');
        }

        try {
            if ($validated['quantity'] === 0) {
                // Remove item
                $this->cartService->removeItemFromCart($cartItem);
                $message = 'Item removed from your cart.';
            } else {
                // Update quantity
                $this->cartService->updateCartItemQuantity($cartItem, $validated['quantity']);
                $message = 'Cart updated.';
            }

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'cart' => [
                        'total_items' => $cart->fresh()->total_items,
                        'subtotal' => $cart->fresh()->subtotal,
                    ],
                ]);
            }

            return redirect()->route('cart.index')->with('success', $message);
        } catch (\Exception $e) {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'We couldn\'t update your cart. Please try again. If the problem continues, please contact our support team.',
                ], 500);
            }

            return redirect()->route('cart.index')->with('error', 'We couldn\'t update your cart. Please try again. If the problem continues, please contact our support team.');
        }
    }

    /**
     * Remove an item from the cart.
     */
    public function remove(Request $request, CartItem $cartItem): RedirectResponse|JsonResponse
    {
        // Verify that the cart item belongs to the current cart
        $cart = $this->getCurrentCart();

        if ($cartItem->cart_id !== $cart->id) {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'We couldn\'t find the specified item in your cart. It may have been removed. Please refresh your cart and try again..',
                ], 404);
            }

            return redirect()->route('cart.index')->with('error', 'We couldn\'t find the specified item in your cart. It may have been removed. Please refresh your cart and try again..');
        }

        try {
            $this->cartService->removeItemFromCart($cartItem);

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Item removed from your cart.',
                    'cart' => [
                        'total_items' => $cart->fresh()->total_items,
                        'subtotal' => $cart->fresh()->subtotal,
                    ],
                ]);
            }

            return redirect()->route('cart.index')->with('success', 'Item removed from your cart.');
        } catch (\Exception $e) {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'We couldn\'t remove this item from your cart. Please try again or contact support if the issue persists.',
                ], 500);
            }

            return redirect()->route('cart.index')->with('error', 'We couldn\'t remove this item from your cart. Please try again or contact support if the issue persists.');
        }
    }

    /**
     * Clear the cart.
     */
    public function clear(Request $request): RedirectResponse|JsonResponse
    {
        try {
            $cart = $this->getCurrentCart();
            $this->cartService->clearCart($cart);

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Cart cleared.',
                ]);
            }

            return redirect()->route('cart.index')->with('success', 'Cart cleared.');
        } catch (\Exception $e) {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'We couldn\'t clear your cart. Please try again or contact support if you need assistance.',
                ], 500);
            }

            return redirect()->route('cart.index')->with('error', 'We couldn\'t clear your cart. Please try again or contact support if you need assistance.');
        }
    }

    /**
     * Get cart summary for AJAX requests (header cart, etc.).
     */
    public function summary(): JsonResponse
    {
        $cart = $this->getCurrentCart();

        if ($cart->items->isEmpty()) {
            return response()->json([
                'success' => true,
                'cart' => [
                    'total_items' => 0,
                    'subtotal' => 0,
                    'formatted_subtotal' => '$0.00',
                    'items' => []
                ]
            ]);
        }

        $summary = $this->cartService->getCartSummary($cart->id);

        // Get basic item info for mini-cart display
        $items = $cart->items->map(function ($item) {
            return [
                'id' => $item->id,
                'name' => $item->productVariant->product->getTranslation('name', app()->getLocale()),
                'variant_name' => $item->productVariant->getTranslation('name', app()->getLocale()),
                'quantity' => $item->quantity,
                'unit_price' => $item->unit_price,
                'formatted_price' => '$' . number_format($item->unit_price, 2),
                'subtotal' => $item->quantity * $item->unit_price,
                'formatted_subtotal' => '$' . number_format($item->quantity * $item->unit_price, 2),
                'image_url' => $item->productVariant->product->getFirstMediaUrl('thumbnail') ?: '/images/placeholder.jpg'
            ];
        });

        return response()->json([
            'success' => true,
            'cart' => array_merge($summary, ['items' => $items])
        ]);
    }

    /**
     * Validate cart and return issues.
     */
    public function validateCart(): JsonResponse
    {
        $cart = $this->getCurrentCart();

        if ($cart->items->isEmpty()) {
            return response()->json([
                'success' => true,
                'valid' => true,
                'issues' => []
            ]);
        }

        $validation = $this->validationService->validateCart($cart);

        return response()->json([
            'success' => true,
            'valid' => $validation['valid'],
            'issues' => $validation['issues'],
            'summary' => $validation['summary'],
            'messages' => $this->validationService->getValidationMessages($validation['issues'])
        ]);
    }

    /**
     * Get cart recommendations.
     */
    public function recommendations(): JsonResponse
    {
        $cart = $this->getCurrentCart();
        $recommendations = $this->cartService->getCartRecommendations($cart);

        return response()->json([
            'success' => true,
            'recommendations' => $recommendations
        ]);
    }

    /**
     * Get the current cart for the user or session.
     */
    protected function getCurrentCart(): Cart
    {
        if (Auth::check()) {
            // Get or create cart for authenticated user
            return $this->cartService->getOrCreateCartForUser(Auth::user());
        } else {
            // Get or create cart for guest session
            $sessionId = Session::getId();
            return $this->cartService->getOrCreateCartForSession($sessionId);
        }
    }
}
