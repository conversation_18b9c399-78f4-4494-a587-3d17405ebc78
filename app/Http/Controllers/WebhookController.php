<?php

namespace App\Http\Controllers;

use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class WebhookController extends Controller
{
    /**
     * The payment service instance.
     */
    protected PaymentService $paymentService;

    /**
     * Create a new controller instance.
     */
    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;

        // Disable CSRF protection for webhooks
        $this->middleware('webhook.signature');
    }

    /**
     * Handle incoming PayPal webhook requests.
     *
     * Note: Ensure this route is excluded from CSRF protection if applicable.
     */
    public function handlePayPalWebhookRequest(Request $request): JsonResponse
    {
        Log::info('PayPal Webhook Request Received.');
        try {
            $payloadArray = $request->json()->all(); // PayPal webhooks are JSON

            if (empty($payloadArray) || !is_array($payloadArray)) {
                Log::error('PayPal Webhook: Empty or invalid JSON payload.');
                return response()->json(['error' => 'Invalid payload.'], 400);
            }

            // PayPal headers are case-insensitive, but using canonical names is good practice.
            // The SrmklivePayPalGateway expects the actual signature and a concatenated string of other header values.
            $signature = $request->header('Paypal-Transmission-Sig');
            $authAlgo = $request->header('Paypal-Auth-Algo');
            $certUrl = $request->header('Paypal-Cert-Url');
            $transmissionId = $request->header('Paypal-Transmission-Id');
            $transmissionTime = $request->header('Paypal-Transmission-Time');

            if (!$signature || !$authAlgo || !$certUrl || !$transmissionId || !$transmissionTime) {
                Log::error('PayPal Webhook: Missing one or more required signature headers.', [
                    'SIG' => !empty($signature), 'ALGO' => !empty($authAlgo),
                    'CERT' => !empty($certUrl), 'ID' => !empty($transmissionId),
                    'TIME' => !empty($transmissionTime),
                ]);
                return response()->json(['error' => 'Missing signature headers.'], 400);
            }

            // Delegate to PaymentService
            $result = $this->paymentService->processPayPalWebhook(
                $payloadArray,
                $signature,
                $authAlgo,
                $certUrl,
                $transmissionId,
                $transmissionTime
            );

            if ($result['success']) {
                // PayPal expects a 200 OK to acknowledge receipt.
                return response()->json(['status' => 'success', 'message' => $result['message'] ?? 'Webhook processed.']);
            } else {
                // Log the specific error from the service
                Log::error('PayPal Webhook processing failed by service.', [
                    'error' => $result['message'] ?? 'Unknown service error',
                    'details' => $result['error_details'] ?? null
                ]);
                // Return 400 or 500 based on the nature of the error.
                // If it's a validation error or known issue, 400. If unexpected, 500.
                return response()->json(['status' => 'failed', 'message' => $result['message'] ?? 'Webhook processing failed.'], 400);
            }

        } catch (\Exception $e) {
            Log::error('Critical error in PaymentController::handlePayPalWebhookRequest: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
            ]);
            // Return 500 for unexpected server errors.
            return response()->json(['error' => 'Server error processing webhook.'], 500);
        }
    }

    /**
     * Handle incoming Stripe webhook requests.
     *
     * Note: Ensure this route is excluded from CSRF protection if applicable.
     */
    public function handleStripeWebhookRequest(Request $request): JsonResponse
    {
        Log::info('Stripe Webhook Request Received.');
        try {
            $rawPayload = $request->getContent();
            $stripeSignatureHeader = $request->header('Stripe-Signature');

            if (empty($stripeSignatureHeader)) {
                Log::error('Stripe Webhook: Missing Stripe-Signature header.');
                return response()->json(['error' => 'Missing signature header.'], 400);
            }

            // Delegate to PaymentService
            $result = $this->paymentService->processStripeWebhook($rawPayload, $stripeSignatureHeader);

            if ($result['success']) {
                return response()->json(['status' => 'success', 'message' => $result['message'] ?? 'Webhook processed.']);
            } else {
                Log::error('Stripe Webhook processing failed by service.', ['error' => $result['message'] ?? 'Unknown service error']);
                return response()->json(['status' => 'failed', 'message' => $result['message'] ?? 'Webhook processing failed.'], 400);
            }

        } catch (\Exception $e) {
            Log::error('Critical error in PaymentController::handleStripeWebhookRequest: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
            ]);
            return response()->json(['error' => 'Server error processing webhook.'], 500);
        }
    }
}
