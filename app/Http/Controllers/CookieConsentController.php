<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;

class CookieConsentController extends Controller
{
    public function index()
    {
        return view('pages.cookie-settings');
    }

    public function update(Request $request)
    {
        $performance = $request->input('performance_cookies') === 'true';
        $functionality = $request->input('functionality_cookies') === 'true';
        $targeting = $request->input('targeting_advertising_cookies') === 'true';

        $minutes = 365 * 24 * 60;
        $path = config('session.path');
        $domain = config('session.domain');
        $secure = config('session.secure');
        $sameSite = config('session.same_site', 'lax');

        Cookie::queue(
            'cookie_performance_consent',
            $performance ? 'true' : 'false',
            $minutes, $path, $domain, $secure, false, false, $sameSite
        );
        Cookie::queue(
            'cookie_functionality_consent',
            $functionality ? 'true' : 'false',
            $minutes, $path, $domain, $secure, false, false, $sameSite
        );
        Cookie::queue(
            'cookie_targeting_advertising_consent',
            $targeting ? 'true' : 'false',
            $minutes, $path, $domain, $secure, false, false, $sameSite
        );

        return redirect()->back()->with('success', 'Your cookie preferences have been updated.');
    }

    public function acceptAll(Request $request)
    {
        $minutes = 365 * 24 * 60;
        $path = config('session.path');
        $domain = config('session.domain');
        $secure = config('session.secure');
        $sameSite = config('session.same_site', 'lax');

        Cookie::queue('cookie_performance_consent', 'true', $minutes, $path, $domain, $secure, false, false, $sameSite);
        Cookie::queue('cookie_functionality_consent', 'true', $minutes, $path, $domain, $secure, false, false, $sameSite);
        Cookie::queue('cookie_targeting_advertising_consent', 'true', $minutes, $path, $domain, $secure, false, false, $sameSite);

        if ($request->ajax() || $request->wantsJson()) {
            return response()->json(['success' => true, 'message' => 'All cookies accepted.']);
        }

        return redirect()->back()->with('success', 'All cookies have been accepted.');
    }

    public function declineAll(Request $request)
    {
        $minutes = 365 * 24 * 60;
        $path = config('session.path');
        $domain = config('session.domain');
        $secure = config('session.secure');
        $sameSite = config('session.same_site', 'lax');

        Cookie::queue('cookie_performance_consent', 'false', $minutes, $path, $domain, $secure, false, false, $sameSite);
        Cookie::queue('cookie_functionality_consent', 'false', $minutes, $path, $domain, $secure, false, false, $sameSite);
        Cookie::queue('cookie_targeting_advertising_consent', 'false', $minutes, $path, $domain, $secure, false, false, $sameSite);

        if ($request->ajax() || $request->wantsJson()) {
            return response()->json(['success' => true, 'message' => 'All non-essential cookies declined.']);
        }

        return redirect()->back()->with('success', 'All non-essential cookies have been declined.');
    }
}