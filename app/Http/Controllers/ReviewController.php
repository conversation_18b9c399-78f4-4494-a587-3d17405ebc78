<?php

namespace App\Http\Controllers;

use App\Http\Requests\ReviewRequest;
use App\Models\Product;
use App\Models\Review;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ReviewController extends Controller
{
    /**
     * Display reviews for a specific product.
     */
    public function index(Request $request, Product $product): JsonResponse
    {
        try {
            $perPage = $request->input('per_page', 10);
            $sortBy = $request->input('sort', 'newest');
            $filterRating = $request->input('rating');
            $verifiedOnly = $request->boolean('verified_only');

            $query = $product->approvedReviews()->with(['user']);

            // Apply filters
            if ($filterRating) {
                $query->where('rating', $filterRating);
            }

            if ($verifiedOnly) {
                $query->where('verified_purchase', true);
            }

            // Apply sorting
            switch ($sortBy) {
                case 'oldest':
                    $query->orderBy('created_at', 'asc');
                    break;
                case 'rating_high':
                    $query->orderBy('rating', 'desc')->orderBy('created_at', 'desc');
                    break;
                case 'rating_low':
                    $query->orderBy('rating', 'asc')->orderBy('created_at', 'desc');
                    break;
                case 'helpful':
                    $query->orderBy('helpful_count', 'desc')->orderBy('created_at', 'desc');
                    break;
                default: // newest
                    $query->orderBy('created_at', 'desc');
                    break;
            }

            $reviews = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'reviews' => $reviews->items(),
                'pagination' => [
                    'current_page' => $reviews->currentPage(),
                    'last_page' => $reviews->lastPage(),
                    'per_page' => $reviews->perPage(),
                    'total' => $reviews->total(),
                    'has_more' => $reviews->hasMorePages(),
                ],
                'stats' => [
                    'average_rating' => $product->average_rating,
                    'total_reviews' => $product->reviews_count,
                    'rating_distribution' => $product->rating_distribution,
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching reviews: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to load reviews. Please try again.',
            ], 500);
        }
    }

    /**
     * Store a new review.
     */
    public function store(ReviewRequest $request, Product $product): JsonResponse
    {
        try {
            $user = Auth::user();

            // Check if user can review this product
            if (!$product->canBeReviewedBy($user->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already reviewed this product.',
                ], 422);
            }

            DB::beginTransaction();

            // Check if user has purchased this product
            $verifiedPurchase = $product->isPurchasedBy($user->id);

            // Handle image uploads
            $imageUrls = [];
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $image) {
                    $path = $image->store('reviews', 'public');
                    $imageUrls[] = Storage::url($path);
                }
            }

            // Create the review
            $review = Review::create([
                'product_id' => $product->id,
                'user_id' => $user->id,
                'rating' => $request->rating,
                'title' => $request->title,
                'content' => $request->content,
                'verified_purchase' => $verifiedPurchase,
                'is_approved' => false, // Reviews need approval
                'images' => $imageUrls,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Thank you for your review! It will be published after moderation.',
                'review' => $review->load('user'),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating review: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to submit review. Please try again.',
            ], 500);
        }
    }

    /**
     * Update an existing review.
     */
    public function update(ReviewRequest $request, Review $review): JsonResponse
    {
        try {
            $user = Auth::user();

            // Check if user owns this review and can edit it
            if (!$review->canBeEditedBy($user)) {
                return response()->json([
                    'success' => false,
                    'message' => 'You can only edit your own reviews within 24 hours of posting.',
                ], 403);
            }

            DB::beginTransaction();

            // Handle image uploads
            $imageUrls = $review->images ?? [];
            if ($request->hasFile('images')) {
                // Delete old images
                foreach ($imageUrls as $imageUrl) {
                    $path = str_replace('/storage/', '', $imageUrl);
                    Storage::disk('public')->delete($path);
                }

                // Upload new images
                $imageUrls = [];
                foreach ($request->file('images') as $image) {
                    $path = $image->store('reviews', 'public');
                    $imageUrls[] = Storage::url($path);
                }
            }

            // Update the review
            $review->update([
                'rating' => $request->rating,
                'title' => $request->title,
                'content' => $request->content,
                'images' => $imageUrls,
                'is_approved' => false, // Re-approval needed after edit
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Review updated successfully! It will be re-reviewed for approval.',
                'review' => $review->fresh()->load('user'),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating review: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to update review. Please try again.',
            ], 500);
        }
    }

    /**
     * Delete a review.
     */
    public function destroy(Review $review): JsonResponse
    {
        try {
            $user = Auth::user();

            // Check if user owns this review or is admin
            if ($review->user_id !== $user->id && !$user->is_admin) {
                return response()->json([
                    'success' => false,
                    'message' => 'You can only delete your own reviews.',
                ], 403);
            }

            // Delete associated images
            if ($review->images) {
                foreach ($review->images as $imageUrl) {
                    $path = str_replace('/storage/', '', $imageUrl);
                    Storage::disk('public')->delete($path);
                }
            }

            $review->delete();

            return response()->json([
                'success' => true,
                'message' => 'Review deleted successfully.',
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting review: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete review. Please try again.',
            ], 500);
        }
    }

    /**
     * Mark a review as helpful.
     */
    public function markHelpful(Review $review): JsonResponse
    {
        try {
            $review->incrementHelpful();

            return response()->json([
                'success' => true,
                'message' => 'Thank you for your feedback!',
                'helpful_count' => $review->fresh()->helpful_count,
            ]);

        } catch (\Exception $e) {
            Log::error('Error marking review as helpful: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to process your feedback. Please try again.',
            ], 500);
        }
    }

    /**
     * Get review form data for a product.
     */
    public function getFormData(Product $product): JsonResponse
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'You must be logged in to write a review.',
                ], 401);
            }

            $canReview = $product->canBeReviewedBy($user->id);
            $hasPurchased = $product->isPurchasedBy($user->id);
            $existingReview = $product->reviews()->where('user_id', $user->id)->first();

            return response()->json([
                'success' => true,
                'can_review' => $canReview,
                'has_purchased' => $hasPurchased,
                'existing_review' => $existingReview,
                'product' => [
                    'id' => $product->id,
                    'name' => $product->getTranslation('name', app()->getLocale()),
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting review form data: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to load review form. Please try again.',
            ], 500);
        }
    }
}
