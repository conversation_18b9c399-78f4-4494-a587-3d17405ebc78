<?php

namespace App\Http\Controllers;

use App\Models\EmailPreference;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class EmailPreferenceController extends Controller
{
    /**
     * Show the email preferences form.
     */
    public function show(Request $request, string $token): View
    {
        $preference = EmailPreference::where('unsubscribe_token', $token)->firstOrFail();
        
        return view('email.preferences', compact('preference'));
    }

    /**
     * Update email preferences.
     */
    public function update(Request $request, string $token): RedirectResponse
    {
        $preference = EmailPreference::where('unsubscribe_token', $token)->firstOrFail();
        
        $validated = $request->validate([
            'order_confirmation' => 'boolean',
            'payment_confirmed' => 'boolean',
            'payment_failed' => 'boolean',
            'order_processing' => 'boolean',
            'order_shipped' => 'boolean',
            'order_delivered' => 'boolean',
            'order_cancelled' => 'boolean',
            'marketing_emails' => 'boolean',
        ]);

        // Convert null values to false for checkboxes
        foreach ($validated as $key => $value) {
            $validated[$key] = $value ?? false;
        }

        $preference->update($validated);

        return redirect()->back()->with('success', 'Your email preferences have been updated successfully.');
    }

    /**
     * Unsubscribe from all emails.
     */
    public function unsubscribe(Request $request): View|RedirectResponse
    {
        $email = $request->query('email');
        $token = $request->query('token');

        if ($token) {
            $preference = EmailPreference::where('unsubscribe_token', $token)->first();
        } elseif ($email) {
            $preference = EmailPreference::where('email', $email)->first();
        } else {
            return redirect()->route('home')->with('error', 'Invalid unsubscribe link.');
        }

        if (!$preference) {
            return redirect()->route('home')->with('error', 'Email preference not found.');
        }

        if ($request->isMethod('post')) {
            $preference->unsubscribeFromAll();
            return view('email.unsubscribed', compact('preference'));
        }

        return view('email.unsubscribe', compact('preference'));
    }

    /**
     * Resubscribe to emails.
     */
    public function resubscribe(Request $request, string $token): RedirectResponse
    {
        $preference = EmailPreference::where('unsubscribe_token', $token)->firstOrFail();
        
        $preference->update([
            'order_confirmation' => true,
            'payment_confirmed' => true,
            'payment_failed' => true,
            'order_processing' => true,
            'order_shipped' => true,
            'order_delivered' => true,
            'order_cancelled' => true,
            'unsubscribed_at' => null,
        ]);

        return redirect()->route('email.preferences', $token)
            ->with('success', 'You have been resubscribed to order notification emails.');
    }
}
