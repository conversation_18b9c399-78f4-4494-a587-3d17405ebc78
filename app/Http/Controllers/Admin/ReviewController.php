<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Review;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ReviewController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Display a listing of the reviews.
     */
    public function index(Request $request)
    {
        $query = Review::with(['user', 'product', 'approvedBy'])
            ->latest();

        // Apply filters
        if ($request->has('status')) {
            switch ($request->status) {
                case 'pending':
                    $query->where('is_approved', false);
                    break;
                case 'approved':
                    $query->where('is_approved', true);
                    break;
                case 'featured':
                    $query->where('is_featured', true);
                    break;
            }
        }

        if ($request->has('product_id')) {
            $query->where('product_id', $request->product_id);
        }

        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Apply search
        if ($search = $request->search) {
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $request->input('sort_by', 'newest');
        switch ($sortBy) {
            case 'oldest':
                $query->oldest();
                break;
            case 'rating_high':
                $query->orderBy('rating', 'desc')->latest();
                break;
            case 'rating_low':
                $query->orderBy('rating', 'asc')->latest();
                break;
            case 'helpful':
                $query->orderBy('helpful_count', 'desc')->latest();
                break;
            default: // newest
                $query->latest();
        }

        $reviews = $query->paginate($request->per_page ?? 15);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'reviews' => $reviews,
            ]);
        }

        return view('admin.reviews.index', compact('reviews'));
    }

    /**
     * Show the form for editing the specified review.
     */
    public function edit(Review $review)
    {
        $review->load(['user', 'product', 'approvedBy']);
        return view('admin.reviews.edit', compact('review'));
    }

    /**
     * Approve the specified review.
     */
    public function approve(Review $review): JsonResponse|RedirectResponse
    {
        try {
            DB::beginTransaction();
            
            $review->approve(auth()->user());
            
            DB::commit();
            
            if (request()->wantsJson()) {
                return response()->json(['success' => true, 'message' => 'Review approved successfully.', 'review' => $review->fresh(['user', 'approvedBy'])], 200);
            }

            return redirect()->route('admin.reviews.index')->with('success', 'Review approved successfully.');
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error approving review: ' . $e->getMessage());
            
            if (request()->wantsJson()) {
                return response()->json(['success' => false, 'message' => 'Failed to approve review. Please try again.'], 500);
            }

            return back()->with('error', 'Failed to approve review. Please try again.');
        }
    }

    /**
     * Reject the specified review.
     */
    public function reject(Request $request, Review $review): JsonResponse|RedirectResponse
    {
        try {
            DB::beginTransaction();
            
            $review->update([
                'is_approved' => false,
                'rejection_reason' => $request->input('rejection_reason'),
                'approved_at' => null,
                'approved_by' => auth()->user()->id, // Record who rejected it
            ]);
            
            DB::commit();
            
            if ($request->wantsJson()) {
                return response()->json(['success' => true, 'message' => 'Review rejected successfully.'], 200);
            }

            return back()->with('success', 'Review rejected successfully.');
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error rejecting review: ' . $e->getMessage());
            
            if ($request->wantsJson()) {
                return response()->json(['success' => false, 'message' => 'Failed to reject review. Please try again.'], 500);
            }

            return back()->with('error', 'Failed to reject review. Please try again.');
        }
    }

    /**
     * Toggle featured status of the review.
     */
    public function toggleFeatured(Review $review): JsonResponse|RedirectResponse
    {
        try {
            $review->update([
                'is_featured' => !$review->is_featured,
            ]);
            
            if (request()->wantsJson()) {
                return response()->json(['success' => true, 'message' => $review->is_featured ? 'Review marked as featured.' : 'Review removed from featured.'], 200);
            }

            return redirect()->route('admin.reviews.index')->with('success', $review->is_featured ? 'Review marked as featured.' : 'Review removed from featured.');
            
        } catch (\Exception $e) {
            Log::error('Error toggling featured status: ' . $e->getMessage());
            
            if (request()->wantsJson()) {
                return response()->json(['success' => false, 'message' => 'Failed to update featured status. Please try again.'], 500);
            }

            return redirect()->route('admin.reviews.index')->with('error', 'Failed to update featured status. Please try again.');
        }
    }

    /**
     * Remove the specified review from storage.
     */
    public function destroy(Review $review)
    {
        try {
            DB::beginTransaction();
            
            // Delete associated images
            if ($review->images) {
                foreach ($review->images as $imageUrl) {
                    $path = str_replace('/storage/', '', $imageUrl);
                    \Storage::disk('public')->delete($path);
                }
            }
            
            $review->delete();
            
            DB::commit();
            
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Review deleted successfully.',
                ]);
            }
            
            return redirect()->route('admin.reviews.index')
                ->with('success', 'Review deleted successfully.');
                
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting review: ' . $e->getMessage());
            
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete review. Please try again.',
                ], 500);
            }
            
            return back()->with('error', 'Failed to delete review. Please try again.');
        }
    }
}
