<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class SettingController extends Controller
{
    /**
     * Display the settings page.
     */
    public function index(): View
    {
        // Group settings by category
        $generalSettings = Setting::where('category', 'general')->get();
        $contactSettings = Setting::where('category', 'contact')->get();
        $emailSettings = Setting::where('category', 'email')->get();
        $socialSettings = Setting::where('category', 'social')->get();
        $paymentSettings = Setting::where('category', 'payments')->get();

        return view('admin.settings.index', [
            'generalSettings' => $generalSettings,
            'contactSettings' => $contactSettings,
            'emailSettings' => $emailSettings,
            'socialSettings' => $socialSettings,
            'paymentSettings' => $paymentSettings,
        ]);
    }

    /**
     * Update the settings.
     */
    public function update(Request $request): RedirectResponse
    {
        $settings = $request->except('_token', '_method');

        // Flatten nested arrays (PHP converts dots in field names to nested arrays)
        $flattenedSettings = [];
        $this->flattenArray($settings, $flattenedSettings);

        // Handle checkbox for partial payments (if not checked, it won't be in the request)
        // Note: PHP converts dots in field names to underscores, so we need to handle both versions
        if (!isset($flattenedSettings['payments.partial_payments_enabled']) && !isset($flattenedSettings['payments_partial_payments_enabled'])) {
            $flattenedSettings['payments.partial_payments_enabled'] = 'false';
        } else {
            // If we have the underscore version, convert it to the dot version
            if (isset($flattenedSettings['payments_partial_payments_enabled'])) {
                $flattenedSettings['payments.partial_payments_enabled'] = $flattenedSettings['payments_partial_payments_enabled'];
                unset($flattenedSettings['payments_partial_payments_enabled']); // Remove the underscore version
            }
        }

        // Handle other payment fields that might have dots converted to underscores
        $paymentFieldMappings = [
            'payments_partial_payment_minimum_percentage' => 'payments.partial_payment_minimum_percentage',
            'payments_partial_payment_grace_period_days' => 'payments.partial_payment_grace_period_days',
        ];

        foreach ($paymentFieldMappings as $underscoreKey => $dotKey) {
            if (isset($flattenedSettings[$underscoreKey])) {
                $flattenedSettings[$dotKey] = $flattenedSettings[$underscoreKey];
                unset($flattenedSettings[$underscoreKey]);
            }
        }

        // Validate flattened settings
        $rules = [];

        if (isset($flattenedSettings['payments.partial_payment_minimum_percentage'])) {
            $rules['payments.partial_payment_minimum_percentage'] = 'sometimes|integer|min:1|max:99';
        }

        if (isset($flattenedSettings['payments.partial_payment_grace_period_days'])) {
            $rules['payments.partial_payment_grace_period_days'] = 'sometimes|integer|min:1|max:365';
        }

        // Add validation for other settings as needed
        $rules = array_merge($rules, [
            'company_name' => 'sometimes|required|string|max:255',
            'site_name' => 'sometimes|required|string|max:255',
            'contact_email' => 'sometimes|required|email|max:255',
            'contact_phone' => 'sometimes|required|string|max:50',
        ]);

        // Create a new request with flattened data for validation
        $validationRequest = new Request($flattenedSettings);

        try {
            $validationRequest->validate($rules);
        } catch (\Illuminate\Validation\ValidationException $e) {
            throw $e;
        }

        foreach ($flattenedSettings as $key => $value) {

            // Determine the appropriate category and type for the setting
            $category = 'general';
            $type = 'text';

            if (str_starts_with($key, 'payments.')) {
                $category = 'payments';
                if ($key === 'payments.partial_payments_enabled') {
                    $type = 'boolean';
                } elseif (in_array($key, ['payments.partial_payment_minimum_percentage', 'payments.partial_payment_grace_period_days'])) {
                    $type = 'integer';
                }
            } elseif (str_starts_with($key, 'contact_')) {
                $category = 'contact';
                if ($key === 'contact_email') {
                    $type = 'email';
                }
            } elseif (str_starts_with($key, 'mail_')) {
                $category = 'email';
            } elseif (str_contains($key, '_url')) {
                $category = 'social';
                $type = 'url';
            } elseif (str_contains($key, '_color')) {
                $type = 'color';
            }

            Setting::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $value,
                    'category' => $category,
                    'type' => $type,
                ]
            );
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'Settings updated successfully. ' . count($flattenedSettings) . ' settings processed.');
    }

    /**
     * Flatten nested arrays with dot notation keys.
     *
     * @param array $array
     * @param array &$result
     * @param string $prefix
     * @return void
     */
    private function flattenArray(array $array, array &$result, string $prefix = ''): void
    {
        foreach ($array as $key => $value) {
            $newKey = $prefix === '' ? $key : $prefix . '.' . $key;

            if (is_array($value)) {
                $this->flattenArray($value, $result, $newKey);
            } else {
                $result[$newKey] = $value;
            }
        }
    }
}
