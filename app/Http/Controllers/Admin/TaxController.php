<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TaxClass;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class TaxController extends Controller
{
    /**
     * Display a listing of tax classes.
     */
    public function index(Request $request): View
    {
        $search = $request->get('search');

        $query = TaxClass::query();

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('country', 'like', "%{$search}%")
                  ->orWhere('state', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%")
                  ->orWhere('zip', 'like', "%{$search}%");
            });
        }

        $taxClasses = $query->orderBy('priority', 'desc')
                           ->orderBy('name')
                           ->paginate(15)
                           ->withQueryString();

        return view('admin.taxes.index', compact('taxClasses', 'search'));
    }

    /**
     * Show the form for creating a new tax class.
     */
    public function create(): View
    {
        return view('admin.taxes.create');
    }

    /**
     * Store a newly created tax class in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:2',
            'state' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'zip' => 'nullable|string|max:20',
            'rate' => 'required|numeric|min:0|max:100',
            'is_global' => 'boolean',
            'priority' => 'nullable|integer|min:0',
            'on_shipping' => 'boolean',
        ]);

        TaxClass::create([
            'name' => $request->name,
            'country' => $request->country,
            'state' => $request->state,
            'city' => $request->city,
            'zip' => $request->zip,
            'rate' => $request->rate,
            'is_global' => $request->boolean('is_global', false),
            'priority' => $request->priority,
            'on_shipping' => $request->boolean('on_shipping', true),
        ]);

        return redirect()->route('admin.taxes.index')
            ->with('success', 'Tax class created successfully.');
    }

    /**
     * Display the specified tax class.
     */
    public function show(TaxClass $tax): View
    {
        return view('admin.taxes.show', compact('tax'));
    }

    /**
     * Show the form for editing the specified tax class.
     */
    public function edit(TaxClass $tax): View
    {
        return view('admin.taxes.edit', compact('tax'));
    }

    /**
     * Update the specified tax class in storage.
     */
    public function update(Request $request, TaxClass $tax): RedirectResponse
    {
        $request->validate([
            'name' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:2',
            'state' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'zip' => 'nullable|string|max:20',
            'rate' => 'required|numeric|min:0|max:100',
            'is_global' => 'boolean',
            'priority' => 'nullable|integer|min:0',
            'on_shipping' => 'boolean',
        ]);

        $tax->update([
            'name' => $request->name,
            'country' => $request->country,
            'state' => $request->state,
            'city' => $request->city,
            'zip' => $request->zip,
            'rate' => $request->rate,
            'is_global' => $request->boolean('is_global'),
            'priority' => $request->priority,
            'on_shipping' => $request->boolean('on_shipping'),
        ]);

        return redirect()->route('admin.taxes.index')
            ->with('success', 'Tax class updated successfully.');
    }

    /**
     * Remove the specified tax class from storage.
     */
    public function destroy(TaxClass $tax): RedirectResponse
    {
        // Check if tax class is being used by any products
        if ($tax->products()->exists()) {
            return redirect()->route('admin.taxes.index')
                ->with('error', 'Cannot delete tax class that is being used by products.');
        }

        $tax->delete();

        return redirect()->route('admin.taxes.index')
            ->with('success', 'Tax class deleted successfully.');
    }
}
