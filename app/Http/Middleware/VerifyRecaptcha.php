<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use <PERSON>hskoh<PERSON>\NoCaptcha\Facades\NoCaptcha;
use Illuminate\Support\Facades\Log;

class VerifyRecaptcha
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Skip validation if not in production and CAPTCHA is disabled in config
        if (config('app.env') !== 'production' && !config('captcha.secret')) {
            return $next($request);
        }

        // Skip for GET requests
        if ($request->isMethod('get')) {
            return $next($request);
        }

        try {
            $response = $request->input('g-recaptcha-response');
            
            if (empty($response)) {
                Log::warning('CAPTCHA validation failed: No response token');
                return back()->withErrors(['captcha' => 'CAPTCHA verification failed. Please try again.'])->withInput();
            }

            $verifyResponse = NoCaptcha::verifyResponse($response, $request->ip());
            
            if (!$verifyResponse->isSuccess()) {
                Log::warning('CAPTCHA validation failed', [
                    'errors' => $verifyResponse->getErrorCodes(),
                    'ip' => $request->ip(),
                    'url' => $request->fullUrl()
                ]);
                return back()->withErrors(['captcha' => 'CAPTCHA verification failed. Please try again.'])->withInput();
            }

            return $next($request);
            
        } catch (\Exception $e) {
            Log::error('CAPTCHA validation error: ' . $e->getMessage());
            // In production, you might want to fail closed (reject) or fail open (allow)
            // For security, it's better to fail closed in production
            if (config('app.env') === 'production') {
                return back()->withErrors(['captcha' => 'CAPTCHA verification service error. Please try again later.'])->withInput();
            }
            // In development, allow the request to proceed
            return $next($request);
        }
    }
}
