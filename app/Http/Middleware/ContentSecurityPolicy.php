<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ContentSecurityPolicy
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only set CSP for HTML responses
        if (method_exists($response, 'header') && $response->headers) {
            // For development environments, be more permissive
            if (app()->environment(['local', 'development'])) {
                $formActionSources = "'self' 'unsafe-inline' http: https:";
            } else {
                // For production, be more restrictive
                $appUrl = config('app.url');
                $formActionSources = "'self' {$appUrl}";
            }

            $csp = [
                "default-src 'self';",
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google-analytics.com http://127.0.0.1:5173;",
                "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.bunny.net https://cdnjs.cloudflare.com http://127.0.0.1:5173;",
                "img-src 'self' data: https: http:;",
                "font-src 'self' data: https://fonts.gstatic.com https://fonts.bunny.net https://cdnjs.cloudflare.com;",
                "frame-src 'self';",
                "connect-src 'self' wss://127.0.0.1:5173 ws://127.0.0.1:5173 https://www.google-analytics.com http://127.0.0.1:5173;",
                "form-action {$formActionSources};",
                "object-src 'none';",
                "base-uri 'self';",
                "frame-ancestors 'self';",
                "worker-src 'self' blob:;",
                "media-src 'self';",
                "manifest-src 'self';",
            ];

            // Set CSP for all responses
            $response->headers->set('Content-Security-Policy', implode(' ', $csp));

            // Add debug header in development
            if (app()->environment(['local', 'development'])) {
                $response->headers->set('X-CSP-Debug', 'form-action: ' . $formActionSources);
            }
        }

        return $response;
    }
}
