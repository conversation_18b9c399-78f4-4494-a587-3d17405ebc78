<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ContentSecurityPolicy
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only set CSP for HTML responses
        if (method_exists($response, 'header') && $response->headers) {
            $csp = [
                "default-src 'self';",
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google.com https://www.gstatic.com https://www.gstatic.com/recapta/ https://www.recaptcha.net https://www.recaptcha.com https://www.google-analytics.com http://127.0.0.1:5173;",
                "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.bunny.net https://cdnjs.cloudflare.com http://127.0.0.1:5173;",
                "img-src 'self' data: https: http: https://www.gstatic.com/recapta/ https://www.recaptcha.net https://www.recaptcha.com;",
                "font-src 'self' data: https://fonts.gstatic.com https://fonts.bunny.net https://cdnjs.cloudflare.com;",
                "frame-src 'self' https://www.google.com https://www.recaptcha.net https://recaptcha.google.com https://www.recaptcha.com;",
                "connect-src 'self' wss://127.0.0.1:5173 ws://127.0.0.1:5173 https://www.google.com https://www.gstatic.com https://www.recaptcha.net https://www.recaptcha.com https://www.google-analytics.com http://127.0.0.1:5173;",
                "form-action 'self' https://www.google.com;",
                "object-src 'none';",
                "base-uri 'self';",
                "frame-ancestors 'self';",
                "worker-src 'self' blob:;",
                "media-src 'self';",
                "manifest-src 'self';",
            ];

            // Only set CSP for HTML responses
            if (str_contains($response->headers->get('Content-Type') ?? '', 'text/html')) {
                $response->headers->set('Content-Security-Policy', implode(' ', $csp));
            }
        }

        return $response;
    }
}
