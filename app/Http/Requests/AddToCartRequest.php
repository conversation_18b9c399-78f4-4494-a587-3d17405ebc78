<?php

namespace App\Http\Requests;

use App\Models\ProductVariant;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AddToCartRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'product_variant_id' => [
                'required',
                'string',
                'exists:product_variants,id',
                Rule::exists('product_variants', 'id')->where(function ($query) {
                    return $query->where('is_active', true);
                }),
            ],
            'quantity' => [
                'required',
                'integer',
                'min:1',
                'max:100',
            ],
            'buy_now' => [
                'sometimes',
                'boolean',
            ],
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            if ($this->product_variant_id) {
                $this->validateProductVariant($validator);
            }
        });
    }

    /**
     * Validate the product variant and its availability.
     */
    private function validateProductVariant($validator): void
    {
        $variant = ProductVariant::with(['product', 'inventoryItem'])
            ->find($this->product_variant_id);

        if (!$variant) {
            $validator->errors()->add('product_variant_id', 'The selected product variant does not exist.');
            return;
        }

        // Check if variant is active
        if (!$variant->is_active) {
            $validator->errors()->add('product_variant_id', 'This product variant is no longer available.');
            return;
        }

        // Check if parent product is active
        if (!$variant->product || !$variant->product->is_active) {
            $validator->errors()->add('product_variant_id', 'This product is no longer available.');
            return;
        }

        // Check stock availability
        $this->validateStock($validator, $variant);
    }

    /**
     * Validate stock availability for the variant.
     */
    private function validateStock($validator, ProductVariant $variant): void
    {
        if (!$variant->inventoryItem || !$variant->inventoryItem->track_inventory) {
            return; // No inventory tracking, assume available
        }

        $inventoryItem = $variant->inventoryItem;
        $availableStock = $inventoryItem->quantity_on_hand - $inventoryItem->quantity_reserved;
        $requestedQuantity = $this->quantity;

        // Check if enough stock is available
        if ($availableStock < $requestedQuantity) {
            if ($inventoryItem->allow_backorder) {
                // Allow backorder but warn about stock
                if ($availableStock > 0) {
                    $backorderQuantity = $requestedQuantity - $availableStock;
                    $validator->errors()->add('quantity',
                        "Only {$availableStock} items in stock. {$backorderQuantity} items will be backordered."
                    );
                } else {
                    $validator->errors()->add('quantity',
                        "This item is out of stock. All {$requestedQuantity} items will be backordered."
                    );
                }
            } else {
                // No backorder allowed
                if ($availableStock <= 0) {
                    $validator->errors()->add('quantity', 'This item is currently out of stock.');
                } else {
                    $validator->errors()->add('quantity',
                        "Only {$availableStock} items available in stock. Please reduce the quantity."
                    );
                }
            }
        }
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'product_variant_id.required' => 'Please select a product variant.',
            'product_variant_id.string' => 'Invalid product variant format.',
            'product_variant_id.exists' => 'The selected product variant is invalid or inactive.',
            'quantity.required' => 'Please specify a quantity.',
            'quantity.integer' => 'Quantity must be a whole number.',
            'quantity.min' => 'Quantity must be at least 1.',
            'quantity.max' => 'Maximum quantity per order is 100 items.',
            'buy_now.boolean' => 'Invalid buy now option.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure quantity is an integer
        if ($this->has('quantity')) {
            $this->merge([
                'quantity' => (int) $this->quantity,
            ]);
        }

        // Ensure buy_now is a boolean
        if ($this->has('buy_now')) {
            $this->merge([
                'buy_now' => filter_var($this->buy_now, FILTER_VALIDATE_BOOLEAN),
            ]);
        }
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        if ($this->wantsJson()) {
            $response = response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);

            throw new \Illuminate\Validation\ValidationException($validator, $response);
        }

        parent::failedValidation($validator);
    }
}
