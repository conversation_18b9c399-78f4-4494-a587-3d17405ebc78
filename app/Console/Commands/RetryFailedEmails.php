<?php

namespace App\Console\Commands;

use App\Services\EmailNotificationService;
use Illuminate\Console\Command;

class RetryFailedEmails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:retry-failed {--max-retries=3 : Maximum number of retries per job}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retry failed email notification jobs';

    /**
     * Execute the console command.
     */
    public function handle(EmailNotificationService $emailService)
    {
        $this->info('Starting to retry failed email jobs...');

        $maxRetries = (int) $this->option('max-retries');
        $results = $emailService->retryFailedEmails($maxRetries);

        $retriedCount = count($results['retried'] ?? []);
        $failedCount = count($results['failed'] ?? []);

        if ($retriedCount > 0) {
            $this->info("Successfully retried {$retriedCount} failed email jobs.");
        }

        if ($failedCount > 0) {
            $this->warn("Failed to retry {$failedCount} email jobs.");

            foreach ($results['failed'] as $failed) {
                $this->line("  - Job {$failed['job_id']}: {$failed['error']}");
            }
        }

        if ($retriedCount === 0 && $failedCount === 0) {
            $this->info('No failed email jobs found to retry.');
        }

        $this->info('Email retry process completed.');

        return Command::SUCCESS;
    }
}
