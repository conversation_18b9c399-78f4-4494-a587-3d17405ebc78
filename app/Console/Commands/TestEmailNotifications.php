<?php

namespace App\Console\Commands;

use App\Events\OrderCancelled;
use App\Events\OrderDelivered;
use App\Events\OrderPlaced;
use App\Events\OrderShipped;
use App\Events\OrderStatusChanged;
use App\Events\PaymentConfirmed;
use App\Events\PaymentFailed;
use App\Models\Order;
use App\Models\Payment;
use Illuminate\Console\Command;

class TestEmailNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test {--email= : Email address to send test emails to} {--event= : Specific event to test}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email notification system with sample data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->option('email') ?: $this->ask('Enter email address to send test emails to');
        $specificEvent = $this->option('event');

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->error('Invalid email address provided.');
            return Command::FAILURE;
        }

        $this->info("Testing email notifications for: {$email}");

        // Get a sample order or create one for testing
        $order = $this->getOrCreateTestOrder($email);
        $payment = $this->getOrCreateTestPayment($order);

        if (!$order) {
            $this->error('Could not create or find test order.');
            return Command::FAILURE;
        }

        $events = [
            'order_placed' => fn() => OrderPlaced::dispatch($order),
            'payment_confirmed' => fn() => PaymentConfirmed::dispatch($order, $payment),
            'payment_failed' => fn() => PaymentFailed::dispatch($order, $payment, 'Test payment failure'),
            'order_processing' => fn() => OrderStatusChanged::dispatch($order, 'pending', 'processing'),
            'order_shipped' => fn() => OrderShipped::dispatch($order, 'TEST123456789'),
            'order_delivered' => fn() => OrderDelivered::dispatch($order),
            'order_cancelled' => fn() => OrderCancelled::dispatch($order, 'Test cancellation'),
        ];

        if ($specificEvent) {
            if (!isset($events[$specificEvent])) {
                $this->error("Unknown event: {$specificEvent}");
                $this->line('Available events: ' . implode(', ', array_keys($events)));
                return Command::FAILURE;
            }

            $this->info("Testing {$specificEvent} event...");
            $events[$specificEvent]();
            $this->info("Event dispatched successfully!");
        } else {
            $this->info('Testing all email notification events...');

            foreach ($events as $eventName => $eventDispatcher) {
                $this->line("Dispatching {$eventName} event...");
                $eventDispatcher();
                sleep(1); // Small delay between events
            }

            $this->info('All events dispatched successfully!');
        }

        $this->info('Email notifications have been queued. Check your email and application logs.');
        $this->line('To process the queue immediately, run: php artisan queue:work');

        return Command::SUCCESS;
    }

    /**
     * Get or create a test order.
     */
    private function getOrCreateTestOrder(string $email): ?Order
    {
        // Try to find an existing order first
        $order = Order::where('customer_email', $email)->first();

        if ($order) {
            $this->line("Using existing order: {$order->order_number}");
            return $order;
        }

        $this->line('Creating test order...');

        try {
            // Create a minimal test order
            $order = Order::create([
                'order_number' => 'TEST-' . now()->format('YmdHis'),
                'customer_email' => $email,
                'customer_name' => 'Test Customer',
                'status' => 'pending',
                'subtotal' => 99.99,
                'shipping_cost' => 9.99,
                'tax_amount' => 8.99,
                'discount_amount' => 0.00,
                'total' => 118.97,
                'currency' => 'USD',
            ]);

            $this->line("Created test order: {$order->order_number}");
            return $order;

        } catch (\Exception $e) {
            $this->error("Failed to create test order: {$e->getMessage()}");
            return null;
        }
    }

    /**
     * Get or create a test payment.
     */
    private function getOrCreateTestPayment(Order $order): Payment
    {
        $payment = $order->payments()->first();

        if ($payment) {
            return $payment;
        }

        return Payment::create([
            'order_id' => $order->id,
            'payment_method' => 'stripe',
            'status' => 'pending',
            'amount' => $order->total,
            'currency' => $order->currency,
        ]);
    }
}
