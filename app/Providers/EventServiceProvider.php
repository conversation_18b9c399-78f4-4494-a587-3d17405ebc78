<?php

namespace App\Providers;

use App\Events\OrderCancelled;
use App\Events\OrderDelivered;
use App\Events\OrderPlaced;
use App\Events\OrderShipped;
use App\Events\OrderStatusChanged;
use App\Events\PaymentConfirmed;
use App\Events\PaymentFailed;
use App\Listeners\SendOrderNotificationEmails;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        PaymentConfirmed::class => [
            [SendOrderNotificationEmails::class, 'handlePaymentConfirmed'],
        ],
        PaymentFailed::class => [
            [SendOrderNotificationEmails::class, 'handlePaymentFailed'],
        ],
        OrderStatusChanged::class => [
            [SendOrderNotificationEmails::class, 'handleOrderStatusChanged'],
        ],
        OrderShipped::class => [
            [SendOrderNotificationEmails::class, 'handleOrderShipped'],
        ],
        OrderDelivered::class => [
            [SendOrderNotificationEmails::class, 'handleOrderDelivered'],
        ],
        OrderCancelled::class => [
            [SendOrderNotificationEmails::class, 'handleOrderCancelled'],
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
