<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;

class RecaptchaServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Merge the package configuration with the application configuration
        $this->mergeConfigFrom(
            __DIR__.'/../../config/captcha.php', 'captcha'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration file
        $this->publishes([
            __DIR__.'/../../config/captcha.php' => config_path('captcha.php'),
        ], 'recaptcha-config');

        // Publish JavaScript assets
        $this->publishes([
            __DIR__.'/../../public/js/recaptcha.js' => public_path('js/recaptcha.js'),
        ], 'recaptcha-assets');

        // Register the recaptcha directive
        Blade::directive('recaptcha', function ($expression) {
            return "<?php echo app('recaptcha')->render($expression); ?>";
        });
    }
}
