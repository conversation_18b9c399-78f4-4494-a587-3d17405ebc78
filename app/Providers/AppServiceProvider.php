<?php

namespace App\Providers;

use App\Helpers\ContentBlockHelper;
use App\Helpers\NotificationHelper;
use App\Listeners\MergeGuestCartWithUserCart;
use App\Services\CartService;
use App\Services\OptimizedCartService;
use App\Services\CartValidationService;
use App\Services\CartEventService;
use Illuminate\Auth\Events\Login;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register cart services
        $this->app->singleton(CartValidationService::class);
        $this->app->singleton(CartEventService::class);

        // Bind OptimizedCartService to CartService interface
        $this->app->bind(CartService::class, OptimizedCartService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register notification helper as a singleton
        $this->app->singleton('notification', function () {
            return new NotificationHelper();
        });

        // Register content block helper as a singleton
        $this->app->singleton('content_block_helper', function () {
            return new ContentBlockHelper();
        });

        // Add a notification directive for Blade
        Blade::directive('notify', function ($expression) {
            return "<?php echo app('notification')->{$expression}; ?>";
        });

        // Register event listeners
        Event::listen(Login::class, MergeGuestCartWithUserCart::class);

        if (config('app.env') === 'production') {
            URL::forceScheme('https');
        }
    }
}
