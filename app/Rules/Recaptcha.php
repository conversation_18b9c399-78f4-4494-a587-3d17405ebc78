<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Anhskohbo\NoCaptcha\Facades\NoCaptcha;
use Illuminate\Support\Facades\Log;

class Recaptcha implements ValidationRule
{
    /**
     * The error message that should be returned when validation fails.
     *
     * @var string
     */
    protected $message = 'The reCAPTCHA verification failed. Please try again.';

    /**
     * Create a new rule instance.
     *
     * @param  string|null  $message
     * @return void
     */
    public function __construct(?string $message = null)
    {
        if ($message) {
            $this->message = $message;
        }
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Skip validation in local environment if no secret key is set
        if (app()->environment('local') && !config('captcha.secret')) {
            return;
        }

        // Skip empty values (handled by required rule)
        if (empty($value)) {
            $fail('The reCAPTCHA response is required.');
            return;
        }

        try {
            Log::debug('reCAPTCHA validation started', [
                'recaptcha_response' => !empty($value) ? 'present' : 'empty',
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent()
            ]);

            $verifyResponse = NoCaptcha::verifyResponse($value, request()->ip());
            
            Log::debug('reCAPTCHA verification response', [
                'response_type' => gettype($verifyResponse),
                'response_value' => $verifyResponse,
                'is_object' => is_object($verifyResponse),
                'is_bool' => is_bool($verifyResponse)
            ]);
            
            // Handle both object and boolean responses
            $isValid = (is_object($verifyResponse) && method_exists($verifyResponse, 'isSuccess') && $verifyResponse->isSuccess())
                    || $verifyResponse === true;
                    
            if (!$isValid) {
                $errorData = [
                    'ip' => request()->ip(),
                    'uri' => request()->path()
                ];
                
                if (is_object($verifyResponse) && method_exists($verifyResponse, 'getErrorCodes')) {
                    $errorData['errors'] = $verifyResponse->getErrorCodes();
                } else {
                    $errorData['error'] = 'reCAPTCHA verification failed';
                }
                
                Log::warning('reCAPTCHA validation failed', $errorData);
                $fail($this->message);
            }
        } catch (\Exception $e) {
            Log::error('reCAPTCHA verification error: ' . $e->getMessage(), [
                'exception' => $e,
                'ip' => request()->ip(),
                'uri' => request()->path()
            ]);
            
            // In production, fail the validation
            if (app()->environment('production')) {
                $fail('An error occurred during reCAPTCHA verification. Please try again.');
            }
        }
    }
}
