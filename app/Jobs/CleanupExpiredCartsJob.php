<?php

namespace App\Jobs;

use App\Models\Cart;
use App\Services\CartService;
use App\Services\InventoryService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CleanupExpiredCartsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Execute the job.
     *
     * @param  \App\Services\CartService  $cartService
     * @param  \App\Services\InventoryService  $inventoryService
     * @return void
     */
    public function handle(CartService $cartService, InventoryService $inventoryService)
    {
        Log::info('Starting cleanup of expired carts');
        
        // Find carts that haven't been updated in the last 24 hours
        $expiredCarts = Cart::where('updated_at', '<', now()->subHours(24))
            ->where('status', 'active')
            ->get();
        
        $count = 0;
        
        foreach ($expiredCarts as $cart) {
            try {
                Log::info('Cleaning up expired cart', [
                    'cart_id' => $cart->id,
                    'user_id' => $cart->user_id,
                    'last_updated' => $cart->updated_at->toIso8601String(),
                ]);
                
                // Release reserved stock for each cart item
                foreach ($cart->items as $item) {
                    $variant = $item->productVariant;
                    
                    if ($variant) {
                        // Find any stock reservations for this cart item
                        $reservations = $variant->stockReservations()
                            ->where('cart_id', $cart->id)
                            ->get();
                        
                        foreach ($reservations as $reservation) {
                            $inventoryService->releaseReservedStock($reservation);
                        }
                    }
                }
                
                // Mark the cart as expired
                $cart->update(['status' => 'expired']);
                
                $count++;
            } catch (\Exception $e) {
                Log::error('Error cleaning up expired cart', [
                    'cart_id' => $cart->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }
        
        Log::info('Completed cleanup of expired carts', [
            'total_carts' => $expiredCarts->count(),
            'successfully_cleaned' => $count,
        ]);

        Log::info('Starting cleanup of empty carts');

        // Find carts that have no items and are older than 1 hour
        $emptyCarts = Cart::doesntHave('items')
            ->where('created_at', '<', now()->subHour())
            ->get();

        $emptyCartCount = 0;

        foreach ($emptyCarts as $cart) {
            try {
                Log::info('Deleting empty cart', [
                    'cart_id' => $cart->id,
                    'session_id' => $cart->session_id,
                    'user_id' => $cart->user_id,
                    'created_at' => $cart->created_at->toIso8601String(),
                ]);

                // Release any associated stock reservations (though there shouldn't be any for empty carts)
                foreach ($cart->stockReservations as $reservation) {
                    $inventoryService->releaseReservedStock($reservation);
                }

                $cart->delete();
                $emptyCartCount++;
            } catch (\Exception $e) {
                Log::error('Error deleting empty cart', [
                    'cart_id' => $cart->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }

        Log::info('Completed cleanup of empty carts', [
            'total_empty_carts_found' => $emptyCarts->count(),
            'successfully_deleted' => $emptyCartCount,
        ]);
    }
}
