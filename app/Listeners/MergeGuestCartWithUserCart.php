<?php

namespace App\Listeners;

use App\Models\Cart;
use App\Services\CartService;
use Illuminate\Auth\Events\Login;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class MergeGuestCartWithUserCart
{
    /**
     * Create the event listener.
     */
    public function __construct(
        protected CartService $cartService
    ) {}

    /**
     * Track which carts have been processed to prevent duplicate merges
     *
     * @var array
     */
    private static $processedCartIds = [];

    /**
     * Handle the event.
     */
    public function handle(Login $event): void
    {
        try {
            // Get the authenticated user from the event
            $user = $event->user;

            // Find all guest carts that might belong to this session
            // We need to query directly since the session ID might have changed
            $guestCarts = Cart::whereNull('user_id')
                ->where('status', 'active')
                ->orderBy('updated_at', 'desc')
                ->take(5) // Limit to recent carts
                ->get();

            if ($guestCarts->isEmpty()) {
                Log::info('No guest carts found to merge');
                return;
            }

            // Get the most recently updated guest cart
            $guestCart = $guestCarts->first();

            // If the guest cart is found but has no items, delete it and return
            if ($guestCart && $guestCart->items->isEmpty()) {
                Log::info('Deleting empty guest cart during login merge attempt', [
                    'cart_id' => $guestCart->id,
                    'session_id' => $guestCart->session_id,
                ]);
                $guestCart->delete();
                return;
            }

            // Check if we've already processed this cart
            if ($guestCart && in_array($guestCart->id, self::$processedCartIds)) {
                Log::info('Cart already processed', [
                    'cart_id' => $guestCart->id,
                    'user_id' => $user->id,
                ]);
                return;
            }

            // Add this cart to the processed list
            self::$processedCartIds[] = $guestCart->id;

            $sessionId = $guestCart->session_id;

            // Log the merge attempt
            Log::info('Attempting to merge guest cart into user cart', [
                'session_id' => $sessionId,
                'user_id' => $user->id,
                'cart_id' => $guestCart->id,
            ]);

            // Merge the guest cart into the user's cart
            $userCart = $this->cartService->mergeGuestCartIntoUserCart($sessionId, $user);

            // Log the result
            if ($userCart) {
                Log::info('Guest cart merged into user cart', [
                    'session_id' => $sessionId,
                    'user_id' => $user->id,
                    'cart_id' => $userCart->id,
                    'items_count' => $userCart->items->count(),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error merging guest cart into user cart', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
