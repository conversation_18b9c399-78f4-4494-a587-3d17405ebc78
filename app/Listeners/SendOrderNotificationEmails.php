<?php

namespace App\Listeners;

use App\Events\OrderCancelled;
use App\Events\OrderDelivered;
use App\Events\OrderShipped;
use App\Events\OrderStatusChanged;
use App\Events\PaymentConfirmed;
use App\Events\PaymentFailed;
use App\Mail\OrderCancelledEmail;
use App\Mail\OrderDeliveredEmail;
use App\Mail\OrderProcessingEmail;
use App\Mail\OrderShippedEmail;
use App\Mail\PaymentConfirmedEmail;
use App\Mail\PaymentFailedEmail;
use App\Models\EmailPreference;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendOrderNotificationEmails implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Check if the customer wants to receive a specific type of email.
     */
    private function shouldSendEmail(string $email, string $emailType): bool
    {
        $preference = EmailPreference::where('email', $email)->first();

        if (!$preference) {
            // If no preference exists, send all order-related emails by default
            return true;
        }

        return $preference->wantsEmail($emailType);
    }

    /**
     * Handle payment confirmed events.
     */
    public function handlePaymentConfirmed(PaymentConfirmed $event): void
    {
        if (!$this->shouldSendEmail($event->order->customer_email, 'payment_confirmed')) {
            Log::info('Payment confirmed email skipped due to user preferences', [
                'order_id' => $event->order->id,
                'customer_email' => $event->order->customer_email,
            ]);
            return;
        }

        try {
            Mail::to($event->order->customer_email)
                ->queue(new PaymentConfirmedEmail($event->order, $event->payment));

            Log::info('Payment confirmed email queued', [
                'order_id' => $event->order->id,
                'order_number' => $event->order->order_number,
                'customer_email' => $event->order->customer_email,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to queue payment confirmed email', [
                'order_id' => $event->order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Handle payment failed events.
     */
    public function handlePaymentFailed(PaymentFailed $event): void
    {
        if (!$this->shouldSendEmail($event->order->customer_email, 'payment_failed')) {
            Log::info('Payment failed email skipped due to user preferences', [
                'order_id' => $event->order->id,
                'customer_email' => $event->order->customer_email,
            ]);
            return;
        }

        try {
            Mail::to($event->order->customer_email)
                ->queue(new PaymentFailedEmail($event->order, $event->payment, $event->reason));

            Log::info('Payment failed email queued', [
                'order_id' => $event->order->id,
                'order_number' => $event->order->order_number,
                'customer_email' => $event->order->customer_email,
                'reason' => $event->reason,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to queue payment failed email', [
                'order_id' => $event->order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Handle order status changed events.
     */
    public function handleOrderStatusChanged(OrderStatusChanged $event): void
    {
        try {
            // Send appropriate email based on the new status
            switch ($event->newStatus) {
                case 'processing':
                    if ($this->shouldSendEmail($event->order->customer_email, 'order_processing')) {
                        Mail::to($event->order->customer_email)
                            ->queue(new OrderProcessingEmail($event->order));
                    } else {
                        Log::info('Order processing email skipped due to user preferences', [
                            'order_id' => $event->order->id,
                            'customer_email' => $event->order->customer_email,
                        ]);
                        return;
                    }
                    break;
                // Other status changes are handled by specific events
            }

            Log::info('Order status changed email queued', [
                'order_id' => $event->order->id,
                'order_number' => $event->order->order_number,
                'previous_status' => $event->previousStatus,
                'new_status' => $event->newStatus,
                'customer_email' => $event->order->customer_email,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to queue order status changed email', [
                'order_id' => $event->order->id,
                'previous_status' => $event->previousStatus,
                'new_status' => $event->newStatus,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Handle order shipped events.
     */
    public function handleOrderShipped(OrderShipped $event): void
    {
        if (!$this->shouldSendEmail($event->order->customer_email, 'order_shipped')) {
            Log::info('Order shipped email skipped due to user preferences', [
                'order_id' => $event->order->id,
                'customer_email' => $event->order->customer_email,
            ]);
            return;
        }

        try {
            Mail::to($event->order->customer_email)
                ->queue(new OrderShippedEmail($event->order, $event->trackingNumber));

            Log::info('Order shipped email queued', [
                'order_id' => $event->order->id,
                'order_number' => $event->order->order_number,
                'customer_email' => $event->order->customer_email,
                'tracking_number' => $event->trackingNumber,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to queue order shipped email', [
                'order_id' => $event->order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Handle order delivered events.
     */
    public function handleOrderDelivered(OrderDelivered $event): void
    {
        if (!$this->shouldSendEmail($event->order->customer_email, 'order_delivered')) {
            Log::info('Order delivered email skipped due to user preferences', [
                'order_id' => $event->order->id,
                'customer_email' => $event->order->customer_email,
            ]);
            return;
        }

        try {
            Mail::to($event->order->customer_email)
                ->queue(new OrderDeliveredEmail($event->order));

            Log::info('Order delivered email queued', [
                'order_id' => $event->order->id,
                'order_number' => $event->order->order_number,
                'customer_email' => $event->order->customer_email,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to queue order delivered email', [
                'order_id' => $event->order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Handle order cancelled events.
     */
    public function handleOrderCancelled(OrderCancelled $event): void
    {
        if (!$this->shouldSendEmail($event->order->customer_email, 'order_cancelled')) {
            Log::info('Order cancelled email skipped due to user preferences', [
                'order_id' => $event->order->id,
                'customer_email' => $event->order->customer_email,
            ]);
            return;
        }

        try {
            Mail::to($event->order->customer_email)
                ->queue(new OrderCancelledEmail($event->order, $event->reason));

            Log::info('Order cancelled email queued', [
                'order_id' => $event->order->id,
                'order_number' => $event->order->order_number,
                'customer_email' => $event->order->customer_email,
                'reason' => $event->reason,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to queue order cancelled email', [
                'order_id' => $event->order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
