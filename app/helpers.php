<?php

if (!function_exists('setting')) {
    /**
     * Get a setting value by key.
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function setting(string $key, $default = null)
    {
        return \App\Models\Setting::getValue($key, $default);
    }
}

if (!function_exists('set_setting')) {
    /**
     * Set a setting value by key.
     *
     * @param string $key
     * @param mixed $value
     * @param string $category
     * @param string $type
     * @param array $options
     * @param bool $isPublic
     * @return \App\Models\Setting
     */
    function set_setting(string $key, $value, string $category = 'general', string $type = 'text', array $options = [], bool $isPublic = true)
    {
        return \App\Models\Setting::setValue($key, $value, $category, $type, $options, $isPublic);
    }
}
