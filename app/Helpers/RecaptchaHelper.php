<?php

namespace App\Helpers;

use Anhskohbo\NoCaptcha\Facades\NoCaptcha;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\HtmlString;

class RecaptchaHelper
{
    /**
     * Render the reCAPTCHA widget.
     *
     * @param string $formId The ID of the form this reCAP<PERSON><PERSON> belongs to
     * @param array $options Additional options for the reCAPTCHA
     * @return HtmlString
     */
    public static function render(string $formId, array $options = []): HtmlString
    {
        $siteKey = Config::get('captcha.sitekey');
        
        if (empty($siteKey)) {
            return new HtmlString('<!-- reCAP<PERSON><PERSON> not configured. Please set NOCAPTCHA_SITEKEY in your .env file -->');
        }
        
        $defaultOptions = [
            'sitekey' => $siteKey,
            'version' => Config::get('captcha.version', 'invisible'),
            'showBadge' => true,
            'badgePosition' => Config::get('captcha.attributes.data-badge', 'bottomright'),
        ];
        
        $options = array_merge($defaultOptions, $options);
        
        // Create a unique ID for the reCAPTCHA container
        $recaptchaId = 'recaptcha-' . $formId . '-' . uniqid();
        
        $html = '<div id="' . $recaptchaId . '" class="g-recaptcha" ';
        
        // Add data attributes
        $html .= 'data-sitekey="' . e($options['sitekey']) . '" ';
        $html .= 'data-size="' . e($options['version'] === 'invisible' ? 'invisible' : 'normal') . '" ';
        $html .= 'data-badge="' . e($options['badgePosition']) . '" ';
        $html .= 'data-callback="onRecaptchaSuccess" ';
        $html .= 'data-expired-callback="onRecaptchaExpired" ';
        $html .= 'data-error-callback="onRecaptchaError"></div>';
        
        // Add the hidden input for the response
        $html .= '<input type="hidden" name="g-recaptcha-response" id="g-recaptcha-response-' . e($formId) . '">';
        
        // Add error container
        $html .= '<div id="recaptcha-error-' . e($formId) . '" class="recaptcha-error"></div>';
        
        return new HtmlString($html);
    }
    
    /**
     * Verify the reCAPTCHA response.
     *
     * @param string $response The reCAPTCHA response token
     * @param string $clientIp The client IP address
     * @return bool
     */
    public static function verify(string $response, ?string $clientIp = null): bool
    {
        if (empty($response)) {
            return false;
        }
        
        try {
            $verifyResponse = NoCaptcha::verifyResponse($response, $clientIp);
            return $verifyResponse->isSuccess();
        } catch (\Exception $e) {
            \Log::error('reCAPTCHA verification error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get the reCAPTCHA JavaScript code.
     *
     * @return HtmlString
     */
    public static function script(): HtmlString
    {
        $siteKey = Config::get('captcha.sitekey');
        
        if (empty($siteKey)) {
            return new HtmlString('');
        }
        
        $script = "<script>";
        $script .= "window.recaptchaSiteKey = '" . e($siteKey) . "';";
        $script .= "</script>";
        $script .= "<script src='https://www.google.com/recaptcha/api.js?onload=onRecaptchaLoad&render=explicit' async defer></script>";
        $script .= "<script src='" . asset('js/recaptcha.js') . "' defer></script>";
        
        return new HtmlString($script);
    }
    
    /**
     * Get the reCAPTCHA styles.
     *
     * @return HtmlString
     */
    public static function style(): HtmlString
    {
        $style = "<style>";
        $style .= ".grecaptcha-badge { visibility: hidden !important; opacity: 0 !important; pointer-events: none !important; }";
        $style .= ".g-recaptcha { margin: 1rem 0; }";
        $style .= ".recaptcha-error { color: #dc3545; font-size: 0.875em; margin-top: 0.25rem; display: none; }";
        $style .= "</style>";
        
        return new HtmlString($style);
    }
}
