# Plan to Address Cookie Implementation Gaps

This document outlines a detailed plan to address the remaining gaps in the cookie implementation, focusing on architectural and structural changes required for conditional script loading and developing a "Cookie Consent Manager" for granular user preferences.

## Goal 1: Implement Conditional Script Loading (Enforcement of Consent)

This goal aims to ensure that non-essential scripts (Performance, Functionality, and Targeting/Advertising) are only loaded after the user has explicitly consented to them.

### Steps:

1.  **Define Cookie Categories and Corresponding Consent Flags:**
    *   Establish clear categories for cookies based on the policy: `performance`, `functionality`, `targeting_advertising`.
    *   Each category will have a corresponding consent flag (e.g., `cookie_performance_consent`, `cookie_functionality_consent`, `cookie_targeting_advertising_consent`).
    *   Strictly necessary cookies will not require explicit consent and will always load.

2.  **Modify Cookie Consent Mechanism:**
    *   **Update `resources/views/components/cookie-consent.blade.php`:**
        *   Change the JavaScript logic to store granular consent for each category (e.g., `cookie_performance_consent=true`, `cookie_functionality_consent=false`).
        *   The "Accept" button will set all non-essential categories to `true`.
        *   The "Decline" button will set all non-essential categories to `false`.
        *   Introduce a "Manage Preferences" button that opens the new "Cookie Consent Manager" modal/page.
    *   **Backend Handling (if necessary):** If cookie preferences need to persist beyond client-side cookies (e.g., for logged-in users), consider a backend mechanism (e.g., a `UserCookiePreferences` model and associated controller/migration) to store these preferences in the database. For now, we'll assume client-side cookies are sufficient as per the current implementation.

3.  **Implement Conditional Script Loading in Layouts:**
    *   **Identify Non-Essential Scripts:** Review `resources/js/app.js` and any other scripts loaded in `resources/views/layouts/app.blade.php`, `resources/views/layouts/guest.blade.php`, and `resources/views/pages/layout.blade.php` to identify which ones fall into "Performance," "Functionality," or "Targeting/Advertising" categories. This might require further investigation into the purpose of each script.
    *   **Wrap Script Tags with Conditional Logic:**
        *   For each non-essential script, wrap its `<script>` tag with Blade `@if` directives that check the corresponding cookie consent flag.
        *   Example:
            ```blade
            @if(request()->cookie('cookie_performance_consent') === 'true')
                <script src="{{ asset('js/analytics.js') }}"></script>
            @endif
            ```
        *   Consider using a helper function or a custom Blade directive to simplify these checks.
    *   **Consolidate `x-cookie-consent` inclusion:** Ensure `x-cookie-consent` is only included once, preferably in `layouts/app.blade.php` and `layouts/guest.blade.php` at the end of the `<body>`. The `pages/layout.blade.php` extends `layouts.app`, so it should inherit it.

## Goal 2: Develop a "Cookie Consent Manager" for Granular Preferences

This goal aims to provide users with a dedicated interface to manage their cookie preferences.

### Steps:

1.  **Create a New Route and Controller for Cookie Settings:**
    *   Define a new route (e.g., `/cookie-settings`) that will display the "Cookie Consent Manager" interface.
    *   Create a new controller (e.g., `CookieConsentController`) to handle requests to this route and potentially update cookie preferences.

2.  **Design and Implement the "Cookie Consent Manager" Interface:**
    *   **Create a new Blade view (e.g., `resources/views/pages/cookie-settings.blade.php`):** This view will contain the UI for the consent manager.
    *   **Modal or Dedicated Page:** Decide whether the manager will be a modal (opened from the footer link) or a dedicated page. A modal is generally more user-friendly for quick adjustments. If a modal, it would be a component loaded on all pages, but hidden by default.
    *   **Categorized Toggles:** Provide toggle switches or checkboxes for each non-essential cookie category: "Performance," "Functionality," and "Targeting/Advertising."
    *   **Save Preferences Button:** Include a button to save the user's updated preferences.
    *   **"Strictly Necessary" Information:** Clearly state that "Strictly Necessary" cookies are always active and cannot be disabled.
    *   **Detailed Cookie List (Optional but Recommended):** As mentioned in the policy, a detailed list of cookies used within each category would enhance transparency. This could be dynamically loaded or hardcoded.

3.  **Update Cookie Preference Storage:**
    *   When the user saves preferences in the "Cookie Consent Manager," update the client-side cookies (and potentially backend storage if implemented) with the granular choices.
    *   The JavaScript in the manager will interact with the cookie setting logic.

4.  **Update Footer Link:**
    *   Modify the "Cookie Policy" link in `resources/views/components/footer.blade.php` to point to the new "Cookie Settings" route or trigger the "Cookie Consent Manager" modal. Change the text to "Cookie Settings" or "Manage Cookie Preferences".

5.  **Integrate with Existing Cookie Banner:**
    *   The "Manage Preferences" button on the initial cookie banner should open the "Cookie Consent Manager."
    *   If the user has already made granular choices, the initial banner should reflect those choices (e.g., "Accept All," "Decline All," "Manage Preferences").

### Architectural Diagram

```mermaid
graph TD
    A[User Visits Website] --> B{Check Cookie Consent};
    B -- No Consent --> C[Display Cookie Banner];
    C -- Accept All --> D[Set All Non-Essential Cookies to True];
    C -- Decline All --> E[Set All Non-Essential Cookies to False];
    C -- Manage Preferences --> F[Open Cookie Consent Manager Modal/Page];
    F -- Save Preferences --> G[Update Granular Cookie Consent];
    G --> H[Reload Page / Apply Script Changes];
    D --> H;
    E --> H;
    H --> I[Conditional Script Loading];
    I -- Performance Consent True --> J[Load Performance Scripts];
    I -- Functionality Consent True --> K[Load Functionality Scripts];
    I -- Targeting Consent True --> L[Load Targeting/Advertising Scripts];
    I -- Strictly Necessary --> M[Load Strictly Necessary Scripts (Always)];

    subgraph Layout Files
        N[resources/views/layouts/app.blade.php]
        O[resources/views/layouts/guest.blade.php]
        P[resources/views/pages/layout.blade.php]
    end

    subgraph Components
        Q[resources/views/components/cookie-consent.blade.php]
        R[resources/views/components/footer.blade.php]
    end

    subgraph New Files
        S[resources/views/pages/cookie-settings.blade.php (or Modal)]
        T[app/Http/Controllers/CookieConsentController.php]
        U[routes/web.php (new route)]
    end

    Q --> C;
    R --> F;
    S --> G;
    T --> G;
    U --> S;
    N --> I;
    O --> I;
    P --> I;