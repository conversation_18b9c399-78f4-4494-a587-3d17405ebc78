<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // General settings
        Setting::setValue('company_name', 'WisdomTechno', 'general', 'text');
        Setting::setValue('site_name', 'WisdomTechno', 'general', 'text');
        Setting::setValue('site_description', 'Empowering businesses with cutting-edge technology solutions', 'general', 'textarea');
        Setting::setValue('site_logo', '/images/logo.png', 'general', 'file');
        Setting::setValue('favicon', '/images/favicon.ico', 'general', 'file');
        Setting::setValue('primary_color', '#4f46e5', 'general', 'color');
        Setting::setValue('secondary_color', '#7c3aed', 'general', 'color');
        Setting::setValue('default_language', 'en', 'general', 'select', ['en' => 'English', 'fr' => 'French', 'es' => 'Spanish']);
        Setting::setValue('default_currency', 'USD', 'general', 'select', ['USD' => 'US Dollar', 'EUR' => 'Euro', 'GBP' => 'British Pound']);

        // Contact settings
        Setting::setValue('contact_email', '<EMAIL>', 'contact', 'email');
        Setting::setValue('contact_phone', '+852 4453 7120', 'contact', 'text');
        Setting::setValue('contact_phone_secondary', '+44 7361 884937', 'contact', 'text');
        Setting::setValue('contact_address', '123 Tech Street, 70000 Paris', 'contact', 'textarea');
        Setting::setValue('google_maps_embed', '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2624.9916256937595!2d2.292292615509614!3d48.85837007928746!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x47e66e2964e34e2d%3A0x8ddca9ee380ef7e0!2sEiffel%20Tower!5e0!3m2!1sen!2sus!4v1619712826145!5m2!1sen!2sus" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy"></iframe>', 'contact', 'textarea');

        // Email settings
        Setting::setValue('mail_from_address', '<EMAIL>', 'email', 'email');
        Setting::setValue('mail_from_name', 'WisdomTechno', 'email', 'text');
        Setting::setValue('mail_driver', 'smtp', 'email', 'select', ['smtp' => 'SMTP', 'sendmail' => 'Sendmail', 'mailgun' => 'Mailgun']);
        Setting::setValue('mail_host', 'smtp.mailtrap.io', 'email', 'text', [], false);
        Setting::setValue('mail_port', '2525', 'email', 'text', [], false);
        Setting::setValue('mail_username', 'your-username', 'email', 'text', [], false);
        Setting::setValue('mail_password', 'your-password', 'email', 'password', [], false);
        Setting::setValue('mail_encryption', 'tls', 'email', 'select', ['tls' => 'TLS', 'ssl' => 'SSL', 'none' => 'None'], false);

        // Social media settings
        Setting::setValue('facebook_url', 'https://facebook.com/wisdomtechno', 'social', 'url');
        Setting::setValue('twitter_url', 'https://twitter.com/wisdomtechno', 'social', 'url');
        Setting::setValue('instagram_url', 'https://instagram.com/wisdomtechno', 'social', 'url');
        Setting::setValue('linkedin_url', 'https://linkedin.com/company/wisdomtechno', 'social', 'url');
        Setting::setValue('youtube_url', 'https://youtube.com/c/wisdomtechno', 'social', 'url');
    }
}
