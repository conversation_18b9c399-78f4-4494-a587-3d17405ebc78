<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\Payment;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Payment>
 */
class PaymentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Payment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'order_id' => Order::factory(),
            'payment_method' => $this->faker->randomElement(['stripe', 'paypal', 'bank_transfer']),
            'transaction_id' => $this->faker->uuid(),
            'status' => $this->faker->randomElement(['pending', 'processing', 'completed', 'failed', 'refunded', 'partial']),
            'amount' => $this->faker->randomFloat(2, 10, 1000),
            'currency' => $this->faker->currencyCode(),
            'payment_details' => [
                'payment_date' => now()->toIso8601String(),
            ],
        ];
    }

    /**
     * Indicate that the payment is completed.
     */
    public function completed(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'completed',
            ];
        });
    }

    /**
     * Indicate that the payment is pending.
     */
    public function pending(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'pending',
            ];
        });
    }

    /**
     * Indicate that the payment is failed.
     */
    public function failed(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'failed',
                'payment_details' => [
                    'error' => 'Payment failed',
                    'error_code' => 'payment_failed',
                ],
            ];
        });
    }

    /**
     * Indicate that the payment is refunded.
     */
    public function refunded(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'refunded',
            ];
        });
    }

    /**
     * Indicate that the payment is partial.
     */
    public function partial(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'partial',
                'payment_details' => [
                    'payment_type' => 'partial',
                    'payment_percentage' => $this->faker->numberBetween(50, 90),
                ],
            ];
        });
    }

    /**
     * Indicate that the payment is for Stripe.
     */
    public function stripe(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'payment_method' => 'stripe',
                'transaction_id' => 'pi_' . $this->faker->regexify('[A-Za-z0-9]{24}'),
                'payment_details' => [
                    'payment_method_id' => 'pm_' . $this->faker->regexify('[A-Za-z0-9]{24}'),
                    'payment_date' => now()->toIso8601String(),
                ],
            ];
        });
    }

    /**
     * Indicate that the payment is for PayPal.
     */
    public function paypal(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'payment_method' => 'paypal',
                'transaction_id' => 'paypal_' . $this->faker->regexify('[A-Za-z0-9]{17}'),
                'payment_details' => [
                    'paypal_order_id' => 'paypal_order_' . $this->faker->regexify('[A-Za-z0-9]{17}'),
                    'payment_date' => now()->toIso8601String(),
                ],
            ];
        });
    }

    /**
     * Indicate that the payment is for bank transfer.
     */
    public function bankTransfer(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'payment_method' => 'bank_transfer',
                'transaction_id' => 'bank_' . $this->faker->regexify('[A-Za-z0-9]{10}'),
                'status' => 'pending',
                'payment_details' => [
                    'reference' => $this->faker->regexify('[A-Z0-9]{8}'),
                    'status' => 'pending_verification',
                    'instructions' => 'Please transfer the amount to our bank account and include your order number as reference.',
                ],
            ];
        });
    }
}
