<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TaxClass>
 */
class TaxClassFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->words(2, true) . ' Tax',
            'country' => $this->faker->optional(0.7)->countryCode(),
            'state' => $this->faker->optional(0.5)->stateAbbr(),
            'city' => $this->faker->optional(0.3)->city(),
            'zip' => $this->faker->optional(0.3)->postcode(),
            'rate' => $this->faker->randomFloat(2, 0, 25),
            'is_global' => $this->faker->boolean(20), // 20% chance of being global
            'priority' => $this->faker->numberBetween(0, 10),
            'on_shipping' => $this->faker->boolean(80), // 80% chance of applying to shipping
        ];
    }

    /**
     * Indicate that the tax class is global.
     */
    public function global(): static
    {
        return $this->state(fn () => [
            'is_global' => true,
            'country' => null,
            'state' => null,
            'city' => null,
            'zip' => null,
        ]);
    }

    /**
     * Indicate that the tax class applies to shipping.
     */
    public function withShipping(): static
    {
        return $this->state(fn () => [
            'on_shipping' => true,
        ]);
    }

    /**
     * Indicate that the tax class does not apply to shipping.
     */
    public function withoutShipping(): static
    {
        return $this->state(fn () => [
            'on_shipping' => false,
        ]);
    }
}
