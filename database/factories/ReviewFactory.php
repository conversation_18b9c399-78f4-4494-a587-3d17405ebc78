<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Review>
 */
class ReviewFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_id' => Product::factory(),
            'user_id' => User::factory(),
            'rating' => $this->faker->numberBetween(1, 5),
            'title' => $this->faker->sentence(4),
            'content' => $this->faker->paragraph(3),
            'verified_purchase' => $this->faker->boolean(30), // 30% chance of verified purchase
            'is_approved' => $this->faker->boolean(80), // 80% chance of being approved
            'is_featured' => $this->faker->boolean(10), // 10% chance of being featured
            'helpful_count' => $this->faker->numberBetween(0, 50),
            'images' => null, // Can be overridden in tests
        ];
    }

    /**
     * Indicate that the review is approved.
     */
    public function approved(): static
    {
        return $this->state(fn () => [
            'is_approved' => true,
            'approved_at' => now(),
        ]);
    }

    /**
     * Indicate that the review is featured.
     */
    public function featured(): static
    {
        return $this->state(fn () => [
            'is_featured' => true,
            'is_approved' => true,
            'approved_at' => now(),
        ]);
    }

    /**
     * Indicate that the review is from a verified purchase.
     */
    public function verifiedPurchase(): static
    {
        return $this->state(fn () => [
            'verified_purchase' => true,
        ]);
    }

    /**
     * Create a review with a specific rating.
     */
    public function rating(int $rating): static
    {
        return $this->state(fn () => [
            'rating' => $rating,
        ]);
    }

    /**
     * Create a review with images.
     */
    public function withImages(?array $images = null): static
    {
        return $this->state(fn () => [
            'images' => $images ?? [
                '/storage/reviews/sample1.jpg',
                '/storage/reviews/sample2.jpg',
            ],
        ]);
    }
}
