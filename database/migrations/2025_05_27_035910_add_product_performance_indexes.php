<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add index for JSON slug field (PostgreSQL)
        // This significantly improves product lookup by slug performance
        DB::statement("CREATE INDEX IF NOT EXISTS idx_products_slug_en ON products ((slug->>'en'))");

        // Add composite indexes for common product queries
        Schema::table('products', function (Blueprint $table) {
            // Index for active products by category (used in product listing and related products)
            if (!Schema::hasIndex('products', 'idx_products_active_category')) {
                $table->index(['is_active', 'category_id'], 'idx_products_active_category');
            }

            // Index for featured products (used in homepage and promotions)
            if (!Schema::hasIndex('products', 'idx_products_active_featured')) {
                $table->index(['is_active', 'is_featured'], 'idx_products_active_featured');
            }

            // Index for product search and filtering
            if (!Schema::hasIndex('products', 'idx_products_category_active')) {
                $table->index(['category_id', 'is_active'], 'idx_products_category_active');
            }
        });

        // Add indexes for product variants
        Schema::table('product_variants', function (Blueprint $table) {
            // Index for active variants by product (used in product detail page)
            if (!Schema::hasIndex('product_variants', 'idx_variants_active_product')) {
                $table->index(['is_active', 'product_id'], 'idx_variants_active_product');
            }

            // Index for price-based queries and sorting
            if (!Schema::hasIndex('product_variants', 'idx_variants_product_price')) {
                $table->index(['product_id', 'price'], 'idx_variants_product_price');
            }

            // Index for SKU lookups
            if (!Schema::hasIndex('product_variants', 'idx_variants_sku')) {
                $table->index(['sku'], 'idx_variants_sku');
            }
        });

        // Add indexes for inventory items
        Schema::table('inventory_items', function (Blueprint $table) {
            // Index for stock tracking queries
            if (!Schema::hasIndex('inventory_items', 'idx_inventory_track_quantity')) {
                $table->index(['track_inventory', 'quantity_on_hand'], 'idx_inventory_track_quantity');
            }

            // Index for low stock alerts
            if (!Schema::hasIndex('inventory_items', 'idx_inventory_low_stock')) {
                $table->index(['low_stock_threshold', 'quantity_on_hand'], 'idx_inventory_low_stock');
            }
        });

        // Add indexes for categories
        Schema::table('categories', function (Blueprint $table) {
            // Index for category hierarchy queries
            if (!Schema::hasIndex('categories', 'idx_categories_parent')) {
                $table->index(['parent_id'], 'idx_categories_parent');
            }
        });

        // Add indexes for cart operations
        Schema::table('cart_items', function (Blueprint $table) {
            // Index for cart item lookups by variant
            if (!Schema::hasIndex('cart_items', 'idx_cart_items_variant')) {
                $table->index(['product_variant_id'], 'idx_cart_items_variant');
            }
        });

        // Add indexes for stock reservations
        Schema::table('stock_reservations', function (Blueprint $table) {
            // Index for reservation cleanup and expiry
            if (!Schema::hasIndex('stock_reservations', 'idx_stock_reservations_expires')) {
                $table->index(['expires_at'], 'idx_stock_reservations_expires');
            }

            // Index for variant-based reservation queries
            if (!Schema::hasIndex('stock_reservations', 'idx_stock_reservations_variant')) {
                $table->index(['product_variant_id'], 'idx_stock_reservations_variant');
            }
        });

        // Note: Skipping GIN index for JSON attributes due to operator class complexity
        // Standard B-tree indexes above will provide significant performance improvements
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop JSON slug index
        DB::statement("DROP INDEX IF EXISTS idx_products_slug_en");

        // Drop product indexes
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex('idx_products_active_category');
            $table->dropIndex('idx_products_active_featured');
            $table->dropIndex('idx_products_category_active');
        });

        // Drop product variant indexes
        Schema::table('product_variants', function (Blueprint $table) {
            $table->dropIndex('idx_variants_active_product');
            $table->dropIndex('idx_variants_product_price');
            $table->dropIndex('idx_variants_sku');
        });

        // Drop inventory indexes
        Schema::table('inventory_items', function (Blueprint $table) {
            $table->dropIndex('idx_inventory_track_quantity');
            $table->dropIndex('idx_inventory_low_stock');
        });

        // Drop category indexes
        Schema::table('categories', function (Blueprint $table) {
            $table->dropIndex('idx_categories_parent');
        });

        // Drop cart indexes
        Schema::table('cart_items', function (Blueprint $table) {
            $table->dropIndex('idx_cart_items_variant');
        });

        // Drop stock reservation indexes
        Schema::table('stock_reservations', function (Blueprint $table) {
            $table->dropIndex('idx_stock_reservations_expires');
            $table->dropIndex('idx_stock_reservations_variant');
        });
    }
};
