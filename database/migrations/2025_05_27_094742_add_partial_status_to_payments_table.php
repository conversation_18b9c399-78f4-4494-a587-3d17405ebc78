<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For PostgreSQL, we need to drop and recreate the check constraint
        if (DB::getDriverName() === 'pgsql') {
            // Drop the existing check constraint
            DB::statement('ALTER TABLE payments DROP CONSTRAINT IF EXISTS payments_status_check');

            // Add the new check constraint with 'partial' included
            DB::statement("ALTER TABLE payments ADD CONSTRAINT payments_status_check CHECK (status::text = ANY (ARRAY['pending'::character varying, 'processing'::character varying, 'completed'::character varying, 'failed'::character varying, 'refunded'::character varying, 'partial'::character varying]::text[]))");
        } else {
            // For MySQL and other databases
            Schema::table('payments', function (Blueprint $table) {
                $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'refunded', 'partial'])
                      ->default('pending')
                      ->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // For PostgreSQL, restore the original check constraint
        if (DB::getDriverName() === 'pgsql') {
            // Drop the current check constraint
            DB::statement('ALTER TABLE payments DROP CONSTRAINT IF EXISTS payments_status_check');

            // Restore the original check constraint without 'partial'
            DB::statement("ALTER TABLE payments ADD CONSTRAINT payments_status_check CHECK (status::text = ANY (ARRAY['pending'::character varying, 'processing'::character varying, 'completed'::character varying, 'failed'::character varying, 'refunded'::character varying]::text[]))");
        } else {
            // For MySQL and other databases, revert the change
            Schema::table('payments', function (Blueprint $table) {
                $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'refunded'])
                      ->default('pending')
                      ->change();
            });
        }
    }
};
