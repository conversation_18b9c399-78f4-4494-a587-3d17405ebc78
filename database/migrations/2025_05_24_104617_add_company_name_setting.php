<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\Setting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add company_name setting if it doesn't exist
        $existingSetting = Setting::where('key', 'company_name')->first();

        if (!$existingSetting) {
            Setting::setValue('company_name', 'WisdomTechno', 'general', 'text');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove company_name setting
        Setting::where('key', 'company_name')->delete();
    }
};
