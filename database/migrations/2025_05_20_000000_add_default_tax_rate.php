<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class AddDefaultTaxRate extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        try {
            // First check if the is_default column exists
            if (!Schema::hasColumn('tax_rates', 'is_default')) {
                Log::info('is_default column does not exist yet, skipping default tax rate creation');
                return;
            }

            // Check if we already have a default tax rate
            $exists = DB::table('tax_rates')
                ->where('is_default', true)
                ->exists();

            if (!$exists) {
                // Check if we need to add the id field for UUID
                $taxRateData = [
                    'name' => 'Global Default',
                    'country' => 'ZZ', // Special country code for global default
                    'rate' => 0, // 0% tax by default - should be configured in admin
                    'is_default' => true,
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                // Check if the table uses UUIDs
                $columns = Schema::getColumnListing('tax_rates');
                if (in_array('id', $columns)) {
                    $idColumn = Schema::getColumnType('tax_rates', 'id');
                    if (strpos($idColumn, 'uuid') !== false || strpos($idColumn, 'char') !== false) {
                        $taxRateData['id'] = \Illuminate\Support\Str::uuid();
                    }
                }

                DB::table('tax_rates')->insert($taxRateData);

                Log::info('Default global tax rate created');
            }
        } catch (\Exception $e) {
            Log::error('Failed to create default tax rate: ' . $e->getMessage());
            // Don't fail the migration if we can't create the default rate
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // Don't remove the default tax rate as it might be in use
    }
}
