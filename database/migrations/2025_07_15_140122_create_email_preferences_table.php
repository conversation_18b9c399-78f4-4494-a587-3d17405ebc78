<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_preferences', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('email')->index();
            $table->boolean('order_confirmation')->default(true);
            $table->boolean('payment_confirmed')->default(true);
            $table->boolean('payment_failed')->default(true);
            $table->boolean('order_processing')->default(true);
            $table->boolean('order_shipped')->default(true);
            $table->boolean('order_delivered')->default(true);
            $table->boolean('order_cancelled')->default(true);
            $table->boolean('marketing_emails')->default(false);
            $table->string('unsubscribe_token')->unique();
            $table->timestamp('unsubscribed_at')->nullable();
            $table->timestamps();

            $table->unique('email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_preferences');
    }
};
