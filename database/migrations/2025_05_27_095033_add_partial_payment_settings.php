<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add partial payment settings to the settings table
        DB::table('settings')->insert([
            [
                'id' => (string) \Illuminate\Support\Str::uuid(),
                'key' => 'payments.partial_payments_enabled',
                'value' => 'false',
                'category' => 'payments',
                'type' => 'boolean',
                'options' => json_encode(['label' => 'Allow customers to make partial payments']),
                'is_public' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => (string) \Illuminate\Support\Str::uuid(),
                'key' => 'payments.partial_payment_minimum_percentage',
                'value' => '50',
                'category' => 'payments',
                'type' => 'integer',
                'options' => json_encode(['label' => 'Minimum percentage of total amount required for partial payments', 'min' => 1, 'max' => 99]),
                'is_public' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => (string) \Illuminate\Support\Str::uuid(),
                'key' => 'payments.partial_payment_grace_period_days',
                'value' => '7',
                'category' => 'payments',
                'type' => 'integer',
                'options' => json_encode(['label' => 'Number of days to complete remaining payment after partial payment', 'min' => 1, 'max' => 365]),
                'is_public' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove partial payment settings
        DB::table('settings')->whereIn('key', [
            'payments.partial_payments_enabled',
            'payments.partial_payment_minimum_percentage',
            'payments.partial_payment_grace_period_days',
        ])->delete();
    }
};
