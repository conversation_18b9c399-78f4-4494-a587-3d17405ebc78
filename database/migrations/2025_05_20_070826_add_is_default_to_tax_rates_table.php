<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $columnAdded = false;

        Schema::table('tax_rates', function (Blueprint $table) use (&$columnAdded) {
            if (!Schema::hasColumn('tax_rates', 'is_default')) {
                $table->boolean('is_default')->default(false)->after('is_active');
                $table->index('is_default');
                $columnAdded = true;
            }
        });

        // If we just added the column, create the default tax rate
        if ($columnAdded) {
            $this->createDefaultTaxRate();
        }
    }

    /**
     * Create a default tax rate if none exists.
     */
    private function createDefaultTaxRate(): void
    {
        try {
            // Check if we already have a default tax rate
            $exists = DB::table('tax_rates')
                ->where('is_default', true)
                ->exists();

            if (!$exists) {
                // Check if we need to add the id field for UUID
                $taxRateData = [
                    'name' => 'Global Default',
                    'country' => 'ZZ', // Special country code for global default
                    'rate' => 0, // 0% tax by default - should be configured in admin
                    'is_default' => true,
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                // Check if the table uses UUIDs
                $columns = Schema::getColumnListing('tax_rates');
                if (in_array('id', $columns)) {
                    $idColumn = Schema::getColumnType('tax_rates', 'id');
                    if (strpos($idColumn, 'uuid') !== false || strpos($idColumn, 'char') !== false) {
                        $taxRateData['id'] = \Illuminate\Support\Str::uuid();
                    }
                }

                DB::table('tax_rates')->insert($taxRateData);

                Log::info('Default global tax rate created after adding is_default column');
            }
        } catch (\Exception $e) {
            Log::error('Failed to create default tax rate after adding column: ' . $e->getMessage());
            // Don't fail the migration if we can't create the default rate
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Don't remove in production to prevent data loss
        if (!app()->environment('production')) {
            Schema::table('tax_rates', function (Blueprint $table) {
                if (Schema::hasColumn('tax_rates', 'is_default')) {
                    $table->dropColumn('is_default');
                }
                $table->dropIndexIfExists('tax_rates_is_default_index');
            });
        }
    }
};
