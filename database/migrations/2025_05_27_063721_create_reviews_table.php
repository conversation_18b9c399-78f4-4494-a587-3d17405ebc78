<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reviews', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('product_id')->constrained()->onDelete('cascade');
            $table->foreignUuid('user_id')->constrained()->onDelete('cascade');
            $table->integer('rating')->unsigned()->comment('Rating from 1 to 5');
            $table->string('title', 255);
            $table->text('content');
            $table->boolean('verified_purchase')->default(false)->comment('Whether this review is from a verified purchase');
            $table->boolean('is_approved')->default(false)->comment('Whether this review has been approved by admin');
            $table->boolean('is_featured')->default(false)->comment('Whether this review is featured');
            $table->integer('helpful_count')->default(0)->comment('Number of users who found this review helpful');
            $table->json('images')->nullable()->comment('Array of image URLs attached to the review');
            $table->timestamp('approved_at')->nullable();
            $table->foreignUuid('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['product_id', 'is_approved', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['rating', 'is_approved']);
            $table->index(['is_featured', 'is_approved']);

            // Ensure one review per user per product
            $table->unique(['product_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reviews');
    }
};
