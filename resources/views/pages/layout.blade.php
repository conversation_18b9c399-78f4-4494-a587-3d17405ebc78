@extends('layouts.app')

@section('content')
<!-- Ultra-Modern Page Header -->
<div class="relative overflow-hidden bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-500">
    <!-- Animated background elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-0 -left-10 w-72 h-72 bg-white rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob"></div>
            <div class="absolute top-0 -right-10 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob animation-delay-2000"></div>
            <div class="absolute -bottom-10 left-20 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob animation-delay-4000"></div>
        </div>
    </div>

    <!-- Header content -->
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-28">
        <div class="flex flex-col items-center text-center">
            <h1 class="text-4xl md:text-5xl lg:text-6xl font-extrabold text-white mb-6 tracking-tight">
                @yield('page-title')
            </h1>
            <div class="w-24 h-1 bg-gradient-to-r from-pink-500 to-indigo-500 rounded-full mb-8"></div>
            @hasSection('page-subtitle')
                <p class="text-xl text-white/90 max-w-3xl">@yield('page-subtitle')</p>
            @endif
        </div>
    </div>

    <!-- Wave divider -->
    <div class="absolute bottom-0 left-0 right-0">
        <x-svg.wave-divider />
    </div>
</div>

<!-- Modern Content Section -->
<div class="bg-gray-50 dark:bg-gray-900 pt-12 pb-24">
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        @if (session('success'))
            <div class="mb-8 transform transition-all duration-300 hover:scale-[1.01]">
                <div class="bg-emerald-50 dark:bg-emerald-900/30 border border-emerald-200 dark:border-emerald-800 rounded-xl shadow-sm overflow-hidden">
                    <div class="px-6 py-5 flex items-center">
                        <div class="flex-shrink-0">
                            <x-svg.checkmark-circle-filled class="h-8 w-8 text-emerald-500" />
                        </div>
                        <div class="ml-4">
                            <p class="text-lg font-medium text-emerald-800 dark:text-emerald-200">Success</p>
                            <p class="text-emerald-700 dark:text-emerald-300">{{ session('success') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        @if (session('error'))
            <div class="mb-8 transform transition-all duration-300 hover:scale-[1.01]">
                <div class="bg-rose-50 dark:bg-rose-900/30 border border-rose-200 dark:border-rose-800 rounded-xl shadow-sm overflow-hidden">
                    <div class="px-6 py-5 flex items-center">
                        <div class="flex-shrink-0">
                            <x-svg.exclamation-circle-filled class="h-8 w-8 text-rose-500" />
                        </div>
                        <div class="ml-4">
                            <p class="text-lg font-medium text-rose-800 dark:text-rose-200">Error</p>
                            <p class="text-rose-700 dark:text-rose-300">{{ session('error') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden backdrop-blur-sm border border-gray-100 dark:border-gray-700">
            <div class="p-8 md:p-12">
                @yield('page-content')
            </div>
        </div>
    </div>
</div>
@endsection

