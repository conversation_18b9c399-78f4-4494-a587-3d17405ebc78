@extends('pages.layout')

@section('title', ContentBlock::get('about.meta.title', 'About ' . \App\Models\Setting::getValue('company_name', 'WisdomTechno')))

@section('page-title', ContentBlock::get('about.page.title', 'About ' . \App\Models\Setting::getValue('company_name', 'WisdomTechno')))

@section('page-subtitle', ContentBlock::get('about.page.subtitle', 'Innovative technology solutions for forward-thinking businesses'))

@section('page-content')
    <!-- Hero section with animated gradient background -->
    <div class="relative overflow-hidden rounded-2xl mb-16">
        <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 opacity-90"></div>
        <div class="absolute inset-0 bg-[url('/img/grid-pattern.svg')] opacity-20"></div>
        <div class="relative px-8 py-16 md:py-20 text-white">
            <div class="max-w-3xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl font-bold mb-6">{{ ContentBlock::get('about.hero.title', 'Transforming Businesses Through Technology') }}</h2>
                <p class="text-lg md:text-xl opacity-90 mb-8">{{ ContentBlock::get('about.hero.description', \App\Models\Setting::getValue('company_name', 'WisdomTechno') . ' is a leading provider of innovative technology solutions for businesses of all sizes. Founded in 2010, we have been at the forefront of technological advancement, helping our clients navigate the ever-changing digital landscape.') }}</p>
                <div class="flex flex-wrap justify-center gap-4">
                    <div class="flex items-center bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                        <x-svg.why-secure class="h-5 w-5 mr-2" />
                        <span>{{ ContentBlock::get('about.hero.stats.clients', 'Trusted by 500+ clients') }}</span>
                    </div>
                    <div class="flex items-center bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                        <x-svg.about-presence class="h-5 w-5 mr-2" />
                        <span>{{ ContentBlock::get('about.hero.stats.presence', 'Global presence') }}</span>
                    </div>
                    <div class="flex items-center bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                        <x-svg.why-fast class="h-5 w-5 mr-2" />
                        <span>{{ ContentBlock::get('about.hero.stats.solutions', 'Industry-leading solutions') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mission and Vision section with cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transform transition duration-500 hover:shadow-xl">
            <div class="h-2 bg-gradient-to-r from-blue-500 to-indigo-600"></div>
            <div class="p-8">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0 h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-4">
                        <x-svg.why-fast class="h-6 w-6" />
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white">{{ ContentBlock::get('about.mission.title', 'Our Mission') }}</h2>
                </div>
                <p class="text-gray-700 dark:text-gray-300">{{ ContentBlock::get('about.mission.text', "Our mission is to empower businesses through technology. We believe that the right technology solutions can transform organizations, making them more efficient, competitive, and successful in today's digital world.") }}</p>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transform transition duration-500 hover:shadow-xl">
            <div class="h-2 bg-gradient-to-r from-purple-500 to-pink-600"></div>
            <div class="p-8">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0 h-12 w-12 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center text-purple-600 dark:text-purple-400 mr-4">
                        <x-svg.about-vision class="h-6 w-6" />
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white">{{ ContentBlock::get('about.vision.title', 'Our Vision') }}</h2>
                </div>
                <p class="text-gray-700 dark:text-gray-300">{{ ContentBlock::get('about.vision.text', "Our vision is to be the leading provider of innovative technology solutions that drive business success and growth. We believe that technology can transform organizations, making them more efficient, competitive, and successful in today's digital world.") }}</p>
            </div>
        </div>
    </div>

    <!-- Our Values section with modern cards -->
    <div class="mb-16">
        <div class="flex items-center mb-8">
            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white font-bold mr-4">
                <x-svg.why-secure class="h-5 w-5" />
            </div>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">{{ ContentBlock::get('about.values.section_title', 'Our Values') }}</h2>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 border border-gray-100 dark:border-gray-700 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0 w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3">
                        <x-svg.why-fast class="h-5 w-5" />
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ ContentBlock::get('about.values.innovation.title', 'Innovation') }}</h3>
                </div>
                <p class="text-gray-700 dark:text-gray-300">{{ ContentBlock::get('about.values.innovation.description', 'We constantly seek new and better ways to solve problems, pushing the boundaries of what\'s possible.') }}</p>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 border border-gray-100 dark:border-gray-700 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0 w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center text-purple-600 dark:text-purple-400 mr-3">
                        <x-svg.values-excellence class="h-5 w-5" />
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ ContentBlock::get('about.values.excellence.title', 'Excellence') }}</h3>
                </div>
                <p class="text-gray-700 dark:text-gray-300">{{ ContentBlock::get('about.values.excellence.description', 'We are committed to delivering the highest quality in everything we do, exceeding expectations at every turn.') }}</p>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 border border-gray-100 dark:border-gray-700 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0 w-10 h-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center text-green-600 dark:text-green-400 mr-3">
                        <x-svg.why-secure class="h-5 w-5" />
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ ContentBlock::get('about.values.integrity.title', 'Integrity') }}</h3>
                </div>
                <p class="text-gray-700 dark:text-gray-300">{{ ContentBlock::get('about.values.integrity.description', 'We operate with honesty, transparency, and ethical standards in all our business dealings.') }}</p>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 border border-gray-100 dark:border-gray-700 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0 w-10 h-10 rounded-full bg-amber-100 dark:bg-amber-900 flex items-center justify-center text-amber-600 dark:text-amber-400 mr-3">
                        <x-svg.values-customer-focus class="h-5 w-5" />
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ ContentBlock::get('about.values.customer_focus.title', 'Customer Focus') }}</h3>
                </div>
                <p class="text-gray-700 dark:text-gray-300">{{ ContentBlock::get('about.values.customer_focus.description', "We put our clients' needs at the center of our decisions, ensuring their success is our priority.") }}</p>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 border border-gray-100 dark:border-gray-700 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0 w-10 h-10 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center text-red-600 dark:text-red-400 mr-3">
                        <x-svg.values-collaboration class="h-5 w-5" />
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ ContentBlock::get('about.values.collaboration.title', 'Collaboration') }}</h3>
                </div>
                <p class="text-gray-700 dark:text-gray-300">{{ ContentBlock::get('about.values.collaboration.description', 'We believe in the power of teamwork and partnership, working together to achieve common goals.') }}</p>
            </div>
        </div>
    </div>

    <!-- Our Approach section with timeline -->
    <div class="mb-16">
        <div class="flex items-center mb-8">
            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white font-bold mr-4">
                <x-svg.approach-chart class="h-5 w-5" />
            </div>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">{{ ContentBlock::get('about.approach.section_title', 'Our Approach') }}</h2>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-8">
            <p class="text-gray-700 dark:text-gray-300 mb-8">{{ ContentBlock::get('about.approach.intro', 'At ' . \App\Models\Setting::getValue('company_name', 'WisdomTechno') . ', we take a consultative approach to understanding your business challenges and objectives. We then leverage our expertise to design and implement solutions that address your specific needs. Our process includes:') }}</p>

            <div class="relative">
                <!-- Timeline line -->
                <div class="absolute left-8 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-500 to-indigo-600 rounded-full"></div>

                <!-- Timeline items -->
                <div class="space-y-12">
                    <div class="relative pl-20">
                        <div class="absolute left-0 flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 shadow-lg">
                            <span class="text-white text-2xl font-bold">1</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">{{ ContentBlock::get('about.approach.discovery.title', 'Discovery') }}</h3>
                            <p class="text-gray-700 dark:text-gray-300">{{ ContentBlock::get('about.approach.discovery.description', 'We take the time to understand your business, goals, and challenges. This involves in-depth discussions with key stakeholders, analysis of current systems, and identification of pain points and opportunities.') }}</p>
                        </div>
                    </div>

                    <div class="relative pl-20">
                        <div class="absolute left-0 flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 shadow-lg">
                            <span class="text-white text-2xl font-bold">2</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">{{ ContentBlock::get('about.approach.strategy.title', 'Strategy') }}</h3>
                            <p class="text-gray-700 dark:text-gray-300">{{ ContentBlock::get('about.approach.strategy.description', 'We develop a tailored strategy that aligns technology with your business objectives. Our team creates a comprehensive roadmap that outlines the solutions, timeline, and expected outcomes.') }}</p>
                        </div>
                    </div>

                    <div class="relative pl-20">
                        <div class="absolute left-0 flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-pink-600 shadow-lg">
                            <span class="text-white text-2xl font-bold">3</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">{{ ContentBlock::get('about.approach.implementation.title', 'Implementation') }}</h3>
                            <p class="text-gray-700 dark:text-gray-300">{{ ContentBlock::get('about.approach.implementation.description', 'We execute the strategy with precision and attention to detail. Our experienced team of developers, engineers, and project managers work collaboratively to deliver high-quality solutions on time and within budget.') }}</p>
                        </div>
                    </div>

                    <div class="relative pl-20">
                        <div class="absolute left-0 flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-pink-500 to-red-600 shadow-lg">
                            <span class="text-white text-2xl font-bold">4</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">{{ ContentBlock::get('about.approach.support.title', 'Support') }}</h3>
                            <p class="text-gray-700 dark:text-gray-300">{{ ContentBlock::get('about.approach.support.description', 'We provide ongoing support to ensure your technology continues to serve your needs. Our dedicated support team is available to address any issues, provide training, and help you maximize the value of your investment.') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Our Team section with modern cards -->
    <div class="mb-16">
        <div class="text-center mb-12">
            <div class="inline-block h-1 w-24 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full mb-4"></div>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">{{ ContentBlock::get('about.team.section_title', 'Meet Our Leadership Team') }}</h2>
            <p class="mt-4 text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">{{ ContentBlock::get('about.team.subtitle', 'Our talented team of experts is dedicated to delivering innovative solutions and exceptional service to our clients.') }}</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Team Member 1 -->
            <div class="group">
                <div class="relative overflow-hidden rounded-xl shadow-xl bg-white dark:bg-gray-800 transform transition duration-500 hover:translate-y-[-10px]">
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-500 to-indigo-600 opacity-90 z-10 transition-opacity duration-500 group-hover:opacity-100"></div>
                    <div class="absolute inset-0 bg-[url('/img/grid-pattern.svg')] opacity-20 z-20"></div>

                    <div class="relative z-30 p-8 text-center">
                        <div class="relative mx-auto w-32 h-32 mb-6 rounded-full overflow-hidden border-4 border-white dark:border-gray-700 shadow-lg">
                            <div class="absolute inset-0 bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center text-white">
                                <x-svg.team-member class="h-20 w-20" />
                            </div>
                        </div>

                        <h3 class="text-xl font-bold mb-2 text-white group-hover:text-white">{{ ContentBlock::get('about.team.member1.name', 'John Smith') }}</h3>
                        <p class="text-blue-200 font-medium mb-4 group-hover:text-blue-100">{{ ContentBlock::get('about.team.member1.role', 'CEO & Founder') }}</p>

                        <div class="bg-white/20 backdrop-blur-sm rounded-lg p-4 mb-6">
                            <p class="text-white/90 text-sm">{{ ContentBlock::get('about.team.member1.description', 'John has over 20 years of experience in the technology industry and is passionate about helping businesses leverage technology to achieve their goals.') }}</p>
                        </div>

                        <div class="flex justify-center space-x-4">
                            <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" class="text-white/70 hover:text-white transition-colors">
                                <span class="sr-only">{{ ContentBlock::get('social.linkedin.sr_only', 'LinkedIn') }}</span>
                                <x-svg.social-linkedin class="h-6 w-6" />
                            </a>
                            <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" class="text-white/70 hover:text-white transition-colors">
                                <span class="sr-only">{{ ContentBlock::get('social.twitter.sr_only', 'Twitter') }}</span>
                                <x-svg.social-twitter class="h-6 w-6" />
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Team Member 2 -->
            <div class="group">
                <div class="relative overflow-hidden rounded-xl shadow-xl bg-white dark:bg-gray-800 transform transition duration-500 hover:translate-y-[-10px]">
                    <div class="absolute inset-0 bg-gradient-to-br from-purple-500 to-pink-600 opacity-90 z-10 transition-opacity duration-500 group-hover:opacity-100"></div>
                    <div class="absolute inset-0 bg-[url('/img/grid-pattern.svg')] opacity-20 z-20"></div>

                    <div class="relative z-30 p-8 text-center">
                        <div class="relative mx-auto w-32 h-32 mb-6 rounded-full overflow-hidden border-4 border-white dark:border-gray-700 shadow-lg">
                            <div class="absolute inset-0 bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center text-white">
                                <x-svg.team-member class="h-20 w-20" />
                            </div>
                        </div>

                        <h3 class="text-xl font-bold mb-2 text-white group-hover:text-white">{{ ContentBlock::get('about.team.member2.name', 'Sarah Johnson') }}</h3>
                        <p class="text-pink-200 font-medium mb-4 group-hover:text-pink-100">{{ ContentBlock::get('about.team.member2.role', 'CTO') }}</p>

                        <div class="bg-white/20 backdrop-blur-sm rounded-lg p-4 mb-6">
                            <p class="text-white/90 text-sm">{{ ContentBlock::get('about.team.member2.description', 'Sarah leads our technical team with her extensive knowledge of software development and system architecture. She ensures our solutions are robust, scalable, and secure.') }}</p>
                        </div>

                        <div class="flex justify-center space-x-4">
                            <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" class="text-white/70 hover:text-white transition-colors">
                                <span class="sr-only">{{ ContentBlock::get('social.linkedin.sr_only', 'LinkedIn') }}</span>
                                <x-svg.social-linkedin class="h-6 w-6" />
                            </a>
                            <a href="https://github.com" target="_blank" rel="noopener noreferrer" class="text-white/70 hover:text-white transition-colors">
                                <span class="sr-only">{{ ContentBlock::get('social.github.sr_only', 'GitHub') }}</span>
                                <x-svg.social-github class="h-6 w-6" />
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Team Member 3 -->
            <div class="group">
                <div class="relative overflow-hidden rounded-xl shadow-xl bg-white dark:bg-gray-800 transform transition duration-500 hover:translate-y-[-10px]">
                    <div class="absolute inset-0 bg-gradient-to-br from-green-500 to-teal-600 opacity-90 z-10 transition-opacity duration-500 group-hover:opacity-100"></div>
                    <div class="absolute inset-0 bg-[url('/img/grid-pattern.svg')] opacity-20 z-20"></div>

                    <div class="relative z-30 p-8 text-center">
                        <div class="relative mx-auto w-32 h-32 mb-6 rounded-full overflow-hidden border-4 border-white dark:border-gray-700 shadow-lg">
                            <div class="absolute inset-0 bg-gradient-to-br from-green-500 to-teal-600 flex items-center justify-center text-white">
                                <x-svg.team-member class="h-20 w-20" />
                            </div>
                        </div>

                        <h3 class="text-xl font-bold mb-2 text-white group-hover:text-white">{{ ContentBlock::get('about.team.member3.name', 'Michael Chen') }}</h3>
                        <p class="text-teal-200 font-medium mb-4 group-hover:text-teal-100">{{ ContentBlock::get('about.team.member3.role', 'Head of Customer Success') }}</p>

                        <div class="bg-white/20 backdrop-blur-sm rounded-lg p-4 mb-6">
                            <p class="text-white/90 text-sm">{{ ContentBlock::get('about.team.member3.description', 'Michael ensures our clients receive exceptional support and achieve their desired outcomes. His team works closely with clients to maximize the value of our solutions.') }}</p>
                        </div>

                        <div class="flex justify-center space-x-4">
                            <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" class="text-white/70 hover:text-white transition-colors">
                                <span class="sr-only">{{ ContentBlock::get('social.linkedin.sr_only', 'LinkedIn') }}</span>
                                <x-svg.social-linkedin class="h-6 w-6" />
                            </a>
                            <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" class="text-white/70 hover:text-white transition-colors">
                                <span class="sr-only">{{ ContentBlock::get('social.twitter.sr_only', 'Twitter') }}</span>
                                <x-svg.social-twitter class="h-6 w-6" />
                            </a>
                            <a href="https://github.com" target="_blank" rel="noopener noreferrer" class="text-white/70 hover:text-white transition-colors">
                                <span class="sr-only">{{ ContentBlock::get('social.github.sr_only', 'GitHub') }}</span>
                                <x-svg.social-github class="h-6 w-6" />
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Join Our Team CTA -->
        <div class="mt-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl shadow-xl p-8 text-white text-center">
            <h3 class="text-2xl font-bold mb-4">{{ ContentBlock::get('about.cta.join_team.title', 'Join Our Team') }}</h3>
            <p class="mb-6 max-w-2xl mx-auto">{{ ContentBlock::get('about.cta.join_team.description', "We're always looking for talented individuals who are passionate about technology and innovation. Check out our current openings and become part of our growing team.") }}</p>
            <a href="#" class="inline-block px-6 py-3 bg-white text-indigo-600 font-medium rounded-lg hover:bg-gray-100 transition duration-300 transform hover:scale-105 shadow-md">{{ ContentBlock::get('about.cta.join_team.button', 'View Career Opportunities') }}</a>
        </div>
    </div>
@endsection