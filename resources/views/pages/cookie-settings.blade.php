@extends('pages.layout')

@section('title', '<PERSON><PERSON>s')

@section('page-title', 'Manage Your Cookie Preferences')

@section('page-subtitle', 'Control which types of cookies you allow on our website.')

@section('page-content')
    <div class="max-w-none">
        <p class="mb-8 text-gray-700 dark:text-gray-300">
            You can enable or disable different categories of cookies below. Please note that "Strictly Necessary Cookies" are essential for the website to function and cannot be disabled.
        </p>

        <form action="{{ route('cookie-settings.update') }}" method="POST" class="space-y-8">
            @csrf
            @method('POST')

            <!-- Strictly Necessary Cookies -->
            <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Strictly Necessary Cookies</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">These cookies are essential for the website to function and cannot be switched off.</p>
                    </div>
                    <span class="text-sm font-semibold text-green-600 dark:text-green-400">Always Active</span>
                </div>
            </div>

            <!-- Performance Cookies -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Performance Cookies</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">These cookies allow us to count visits and traffic sources to measure and improve our site's performance.</p>
                    </div>
                    <label for="performance_cookies" class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="performance_cookies" name="performance_cookies" value="true" class="sr-only peer" {{ request()->cookie('cookie_performance_consent') === 'true' ? 'checked' : '' }}>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-600 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-500 peer-checked:bg-blue-600"></div>
                    </label>
                </div>
            </div>

            <!-- Functionality Cookies -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Functionality Cookies</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">These cookies enable enhanced functionality and personalization, like remembering your preferences.</p>
                    </div>
                    <label for="functionality_cookies" class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="functionality_cookies" name="functionality_cookies" value="true" class="sr-only peer" {{ request()->cookie('cookie_functionality_consent') === 'true' ? 'checked' : '' }}>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-600 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-500 peer-checked:bg-blue-600"></div>
                    </label>
                </div>
            </div>

            <!-- Targeting/Advertising Cookies -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Targeting/Advertising Cookies</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">These cookies are used to build a profile of your interests and show you relevant adverts on other sites.</p>
                    </div>
                    <label for="targeting_advertising_cookies" class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="targeting_advertising_cookies" name="targeting_advertising_cookies" value="true" class="sr-only peer" {{ request()->cookie('cookie_targeting_advertising_consent') === 'true' ? 'checked' : '' }}>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-600 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-500 peer-checked:bg-blue-600"></div>
                    </label>
                </div>
            </div>

            <div class="flex justify-end">
                <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-indigo-500 dark:hover:bg-indigo-600">
                    Save Preferences
                </button>
            </div>
        </form>
    </div>
@endsection