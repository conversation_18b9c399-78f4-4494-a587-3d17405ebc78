@once
    @push('head')
        <meta name="recaptcha-sitekey" content="{{ $siteKey }}">
        <script src="{{ asset('js/recaptcha.js') }}" defer></script>
        <style>
            /* Hide the reCAPTCHA badge */
            .grecaptcha-badge { 
                visibility: hidden !important;
                opacity: 0 !important;
                pointer-events: none !important;
            }
            
            /* Style for the reCAPTCHA container */
            .g-recaptcha {
                margin: 1rem 0;
            }
            
            /* Error message styling */
            .recaptcha-error {
                color: #dc3545;
                font-size: 0.875em;
                margin-top: 0.25rem;
                display: none;
            }
        </style>
    @endpush
@endonce

<div class="recaptcha-wrapper">
    <!-- reCAPTCHA widget will be inserted here -->
    <div id="recaptcha-{{ $formId }}" 
         class="g-recaptcha" 
         data-sitekey="{{ $siteKey }}" 
         data-size="invisible"
         data-badge="{{ $badgePosition }}"
         data-callback="onRecaptchaSuccess"
         data-expired-callback="onRecaptchaExpired"
         data-error-callback="onRecaptchaError">
    </div>
    
    <!-- Error message container -->
    <div id="recaptcha-error-{{ $formId }}" class="recaptcha-error"></div>
    
    <!-- Hidden input to store the reCAPTCHA response -->
    <input type="hidden" name="g-recaptcha-response" id="g-recaptcha-response-{{ $formId }}">
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to handle reCAPTCHA success
        window.onRecaptchaSuccess = function(token) {
            // Set the token in the hidden input
            document.getElementById('g-recaptcha-response-{{ $formId }}').value = token;
            
            // Submit the form
            const form = document.getElementById('{{ $formId }}');
            if (form) {
                form.dispatchEvent(new Event('captcha:verified'));
            }
        };
        
        // Function to handle reCAPTCHA expiration
        window.onRecaptchaExpired = function() {
            // Clear the token
            document.getElementById('g-recaptcha-response-{{ $formId }}').value = '';
            
            // Show error message
            const errorElement = document.getElementById('recaptcha-error-{{ $formId }}');
            if (errorElement) {
                errorElement.textContent = 'reCAPTCHA verification expired. Please try again.';
                errorElement.style.display = 'block';
            }
        };
        
        // Function to handle reCAPTCHA errors
        window.onRecaptchaError = function() {
            // Show error message
            const errorElement = document.getElementById('recaptcha-error-{{ $formId }}');
            if (errorElement) {
                errorElement.textContent = 'Error verifying reCAPTCHA. Please try again.';
                errorElement.style.display = 'block';
            }
        };
    });
    
    // Function to execute reCAPTCHA (can be called from form's submit handler)
    function executeRecaptcha(formId) {
        const widgetId = grecaptcha.render('recaptcha-' + formId, {
            'sitekey': '{{ $siteKey }}',
            'size': 'invisible',
            'callback': window.onRecaptchaSuccess,
            'expired-callback': window.onRecaptchaExpired,
            'error-callback': window.onRecaptchaError
        });
        
        grecaptcha.execute(widgetId);
        return false;
    }
</script>
@endpush