@props(['sessionDomain' => null]) {{-- This prop is no longer strictly needed here if set<PERSON><PERSON><PERSON> is removed, but get<PERSON><PERSON><PERSON> is fine without it --}}

<div id="cookie-consent-banner" class="fixed bottom-0 left-0 right-0 bg-gray-800 text-white p-4 flex items-center justify-between flex-wrap z-50">
    <p class="text-sm mb-2 sm:mb-0">
        This website uses cookies to ensure you get the best experience on our website.
        <a href="{{ route('cookie-policy') }}" class="text-blue-300 hover:underline ml-1">Learn More</a>
    </p>
    <div class="flex space-x-2">
        <form action="{{ route('cookie-consent.accept-all') }}" method="POST" class="inline">
            @csrf
            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white text-sm py-2 px-4 rounded">Accept All</button>
        </form>
        <form action="{{ route('cookie-consent.decline-all') }}" method="POST" class="inline">
            @csrf
            <button type="submit" class="bg-gray-600 hover:bg-gray-700 text-white text-sm py-2 px-4 rounded">Decline All</button>
        </form>
        <a href="{{ route('cookie-settings') }}" id="manage-cookies" class="bg-gray-400 hover:bg-gray-500 text-white text-sm py-2 px-4 rounded">
            Manage Preferences
        </a>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const banner = document.getElementById('cookie-consent-banner');

        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }

        function hasConsent() {
            // Check if any of the consent cookies exist, regardless of their value.
            // This indicates that a user has made a decision (either accepted or declined).
            return getCookie('cookie_performance_consent') !== null ||
                   getCookie('cookie_functionality_consent') !== null ||
                   getCookie('cookie_targeting_advertising_consent') !== null;
        }

        if (hasConsent()) {
            banner.style.display = 'none';
        } else {
            banner.style.display = 'flex';
        }
    });
</script>