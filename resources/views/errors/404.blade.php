<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Page Not Found') }}
        </h2>
    </x-slot>

    @section('content')
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="text-center">
                        <div class="mb-8">
                            <h1 class="text-6xl font-bold text-gray-300 dark:text-gray-600 mb-4">404</h1>
                            <h2 class="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
                                {{ __('Page Not Found') }}
                            </h2>
                            <p class="text-gray-600 dark:text-gray-400 mb-8">
                                {{ $message ?? __('The page you are looking for could not be found.') }}
                            </p>
                        </div>

                        @if(isset($similarProducts) && $similarProducts->count() > 0)
                            <div class="mb-8">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
                                    {{ __('You might be interested in these products:') }}
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                    @foreach($similarProducts as $product)
                                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">
                                                {{ $product->getTranslation('name', app()->getLocale()) }}
                                            </h4>
                                            @if($product->category)
                                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                                    {{ $product->category->getTranslation('name', app()->getLocale()) }}
                                                </p>
                                            @endif
                                            <a href="{{ route('products.show', $product->getTranslation('slug', app()->getLocale())) }}"
                                               class="inline-block bg-indigo-600 text-white px-4 py-2 rounded-md text-sm hover:bg-indigo-700 transition-colors duration-200">
                                                {{ __('View Product') }}
                                            </a>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <div class="space-x-4">
                            <a href="{{ route('store.index') }}"
                               class="inline-block bg-indigo-600 text-white px-6 py-3 rounded-md font-medium hover:bg-indigo-700 transition-colors duration-200">
                                {{ __('Back to Store') }}
                            </a>
                            <button onclick="history.back()"
                                    class="inline-block bg-gray-600 text-white px-6 py-3 rounded-md font-medium hover:bg-gray-700 transition-colors duration-200">
                                {{ __('Go Back') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endsection
</x-app-layout>
