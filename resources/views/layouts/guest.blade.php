<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ $title ?? 'Authentication' }} - {{ \App\Models\Setting::getValue('company_name', 'WisdomTechno') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        {{-- Helper function to check cookie consent --}}
        @php
            function hasCookieConsent($category) {
                return request()->cookie('cookie_' . $category . '_consent') === 'true';
            }
        @endphp


                
                // Function to load reCAPTCHA script
                function loadRecaptcha() {
                    if (document.querySelector('.g-recaptcha') && typeof grecaptcha === 'undefined') {
                        const script = document.createElement('script');
                        script.src = 'https://www.google.com/recaptcha/api.js?onload=onRecaptchaLoad&render=explicit';
                        script.async = true;
                        script.defer = true;
                        document.head.appendChild(script);
                    }
                }
                
                // Load reCAPTCHA when DOM is ready
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', loadRecaptcha);
                } else {
                    loadRecaptcha();
                }
            </script>
        @endif
        
        <!-- Custom CSS -->
        <link rel="stylesheet" href="{{ asset('css/custom.css') }}">
        
        <!-- reCAPTCHA Styles -->
        <style>
            .grecaptcha-badge {
                visibility: hidden !important;
                opacity: 0 !important;
                pointer-events: none !important;
            }
            .g-recaptcha {
                margin: 1rem 0;
            }
            .recaptcha-error {
                color: #dc3545;
                font-size: 0.875em;
                margin-top: 0.25rem;
                display: none;
            }
        </style>
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
            <!-- Background Pattern -->
            <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>

            <!-- Animated Background Elements -->
            <div class="absolute inset-0 overflow-hidden pointer-events-none">
                <div class="absolute top-0 -left-10 w-72 h-72 bg-indigo-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-blob"></div>
                <div class="absolute top-0 -right-10 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-blob animation-delay-2000"></div>
                <div class="absolute -bottom-10 left-20 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-blob animation-delay-4000"></div>
            </div>

            <div class="relative sm:mx-auto sm:w-full sm:max-w-md">
                <!-- Logo and Company Name -->
                <div class="text-center">
                    <a href="{{ route('home') }}" class="inline-flex flex-col items-center group">
                        <div class="w-20 h-20 bg-white dark:bg-gray-800 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-gray-200 dark:border-gray-700">
                            <img src="{{ asset('img/logo.png') }}"
                                 alt="{{ \App\Models\Setting::getValue('company_name', 'WisdomTechno') }} Logo"
                                 class="h-16 w-auto object-contain">
                        </div>
                        <h1 class="mt-4 text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                            {{ \App\Models\Setting::getValue('company_name', 'WisdomTechno') }}
                        </h1>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                            {{ \App\Models\Setting::getValue('site_description', 'Empowering businesses with cutting-edge technology solutions') }}
                        </p>
                    </a>
                </div>

                <!-- Auth Card -->
                <div class="mt-8 bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg py-8 px-6 shadow-2xl sm:rounded-2xl sm:px-10 border border-gray-200/50 dark:border-gray-700/50">
                    {{ $slot }}
                </div>

                <!-- Footer Links -->
                <div class="mt-8 text-center">
                    <div class="flex justify-center space-x-6 text-sm">
                        <a href="{{ route('home') }}" class="text-gray-600 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400 transition-colors duration-200">
                            {{ __('Home') }}
                        </a>
                        <a href="{{ route('about') }}" class="text-gray-600 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400 transition-colors duration-200">
                            {{ __('About') }}
                        </a>
                        <a href="{{ route('contact') }}" class="text-gray-600 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400 transition-colors duration-200">
                            {{ __('Contact') }}
                        </a>
                    </div>
                    <p class="mt-4 text-xs text-gray-500 dark:text-gray-400">
                        &copy; {{ date('Y') }} {{ \App\Models\Setting::getValue('company_name', 'WisdomTechno') }}. {{ __('All rights reserved.') }}
                    </p>
                </div>
            </div>
        </div>
        <x-cookie-consent />
        @if(hasCookieConsent('performance'))
            @stack('performance_scripts')
        @endif
        @if(hasCookieConsent('functionality'))
            @stack('functionality_scripts')
        @endif
        @if(hasCookieConsent('targeting_advertising'))
            @stack('targeting_advertising_scripts')
        @endif
        @stack('essential_scripts') {{-- For scripts that are always needed --}}
    </body>
</html>
