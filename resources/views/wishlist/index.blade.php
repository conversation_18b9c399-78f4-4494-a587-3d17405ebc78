<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('My Wishlist') }}
            </h2>
            <div class="mt-2 sm:mt-0 flex items-center space-x-4">
                <span class="text-sm text-gray-600 dark:text-gray-400">
                    {{ $totalItems }} {{ Str::plural('item', $totalItems) }}
                </span>
                @if($totalItems > 0)
                    <button type="button"
                            class="clear-wishlist-btn inline-flex items-center px-3 py-1 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Clear All
                    </button>
                @endif
            </div>
        </div>
    </x-slot>

    @section('content')
    <!-- JavaScript Notification Container -->
    <div id="notification-container" class="fixed top-20 right-5 z-50 space-y-4 w-full max-w-sm pointer-events-none">
        <!-- Notifications will be dynamically inserted here -->
    </div>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if($wishlistItems->count() > 0)
                <!-- Wishlist Statistics -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-indigo-600 dark:text-indigo-400">{{ $totalItems }}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">{{ __('Items in Wishlist') }}</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 dark:text-green-400">${{ number_format($totalValue, 2) }}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">{{ __('Total Value') }}</div>
                            </div>
                            <div class="text-center">
                                <a href="{{ route('store.index') }}"
                                   class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-sm text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    {{ __('Continue Shopping') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Wishlist Items -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            @foreach($wishlistItems as $product)
                                <div class="wishlist-item bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300"
                                     data-product-id="{{ $product->id }}">
                                    <!-- Product Image -->
                                    <div class="relative">
                                        <a href="{{ route('products.show', $product->getTranslation('slug', app()->getLocale())) }}">
                                            @php
                                                $thumbnailUrl = '';
                                                try {
                                                    $thumbnailUrl = $product->getFirstMediaUrl('thumbnail');
                                                } catch (\Exception $e) {
                                                    // Silently handle the error
                                                }
                                            @endphp

                                            @if(!empty($thumbnailUrl))
                                                <img src="{{ $thumbnailUrl }}"
                                                     alt="{{ $product->getTranslation('name', app()->getLocale()) }}"
                                                     class="w-full h-48 object-cover">
                                            @else
                                                <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-600 dark:to-gray-800 flex items-center justify-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                    </svg>
                                                </div>
                                            @endif
                                        </a>

                                        <!-- Remove from Wishlist Button -->
                                        <button type="button"
                                                class="remove-from-wishlist-btn absolute top-2 right-2 p-2 bg-white dark:bg-gray-800 rounded-full shadow-md hover:bg-red-50 dark:hover:bg-red-900 transition-colors duration-200 group"
                                                data-product-id="{{ $product->id }}"
                                                title="Remove from wishlist">
                                            <svg class="w-4 h-4 text-gray-600 dark:text-gray-400 group-hover:text-red-600 dark:group-hover:text-red-400"
                                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </div>

                                    <!-- Product Details -->
                                    <div class="p-4">
                                        <a href="{{ route('products.show', $product->getTranslation('slug', app()->getLocale())) }}" class="block">
                                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors duration-200 line-clamp-2">
                                                {{ $product->getTranslation('name', app()->getLocale()) }}
                                            </h3>
                                        </a>

                                        @if($product->category)
                                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                                {{ $product->category->getTranslation('name', app()->getLocale()) }}
                                            </p>
                                        @endif

                                        @if($product->variants->count() > 0)
                                            @php
                                                $minPrice = $product->variants->min('price');
                                                $maxPrice = $product->variants->max('price');
                                                $firstVariant = $product->variants->first();
                                            @endphp

                                            <div class="flex items-center justify-between mb-3">
                                                <div>
                                                    @if($minPrice === $maxPrice)
                                                        <span class="text-lg font-bold text-gray-900 dark:text-white">${{ number_format($minPrice, 2) }}</span>
                                                    @else
                                                        <span class="text-lg font-bold text-gray-900 dark:text-white">${{ number_format($minPrice, 2) }} - ${{ number_format($maxPrice, 2) }}</span>
                                                    @endif

                                                    @if($firstVariant->compare_at_price && $firstVariant->compare_at_price > $firstVariant->price)
                                                        <span class="ml-2 text-sm text-gray-500 line-through">${{ number_format($firstVariant->compare_at_price, 2) }}</span>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="flex space-x-2">
                                                <a href="{{ route('products.show', $product->getTranslation('slug', app()->getLocale())) }}"
                                                   class="flex-1 inline-flex items-center justify-center px-3 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                                    {{ __('View Product') }}
                                                </a>
                                            </div>
                                        @else
                                            <div class="text-sm text-gray-500 dark:text-gray-400 italic">
                                                {{ __('Currently unavailable') }}
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        @if($wishlistItems->hasPages())
                            <div class="mt-8">
                                {{ $wishlistItems->links() }}
                            </div>
                        @endif
                    </div>
                </div>
            @else
                <!-- Empty Wishlist -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-12 text-center">
                        <svg class="mx-auto h-24 w-24 text-gray-400 dark:text-gray-600 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">{{ __('Your wishlist is empty') }}</h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-6">{{ __('Start adding products you love to your wishlist!') }}</p>
                        <a href="{{ route('store.index') }}"
                           class="inline-flex items-center px-6 py-3 bg-indigo-600 border border-transparent rounded-md font-semibold text-sm text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                            {{ __('Start Shopping') }}
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
    @endsection

    @push('scripts')
    <script src="{{ asset('js/wishlist.js') }}"></script>
    <script>
        // Wishlist page event handlers
        document.addEventListener('DOMContentLoaded', function() {
            // Wait for wishlist.js to load
            setTimeout(function() {
                // Handle remove buttons
                const removeButtons = document.querySelectorAll('.remove-from-wishlist-btn');

                removeButtons.forEach(function(button) {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        const productId = this.getAttribute('data-product-id');
                        const buttonElement = this;

                        if (productId && window.removeFromWishlist) {
                            // Disable button during request
                            buttonElement.disabled = true;
                            buttonElement.style.opacity = '0.5';

                            window.removeFromWishlist(productId).then(() => {
                                // Success handled by wishlist.js
                            }).catch((error) => {
                                console.error('Error removing product:', error);
                                // Re-enable button on error
                                buttonElement.disabled = false;
                                buttonElement.style.opacity = '1';
                            });
                        }
                    });
                });

                // Handle clear button
                const clearButton = document.querySelector('.clear-wishlist-btn');
                if (clearButton) {
                    clearButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        if (window.clearWishlist && typeof Wishlist !== 'undefined') {
                            // Disable button during request
                            Wishlist.disableClearButton();

                            window.clearWishlist().then(() => {
                                // Success handled by wishlist.js
                            }).catch((error) => {
                                console.error('Error clearing wishlist:', error);
                                // Re-enable button on error
                                Wishlist.enableClearButton();
                            });
                        }
                    });
                }
            }, 100); // Small delay to ensure wishlist.js loads
        });
    </script>
    @endpush
</x-app-layout>
