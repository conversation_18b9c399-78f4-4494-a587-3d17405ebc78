<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Checkout') }}
        </h2>
    </x-slot>

    @section('content')
    <script>
        /**
         * Show Notification
         * - Dynamically creates and displays a notification element.
         */
        function showNotification(type, message) {
            // Find or create the notification container
            let notificationContainer = document.getElementById('dynamic-notification-container');
            if (!notificationContainer) {
                notificationContainer = document.createElement('div');
                notificationContainer.id = 'dynamic-notification-container';
                notificationContainer.className = 'fixed top-5 right-5 z-[100] space-y-4 w-full max-w-sm';
                document.body.appendChild(notificationContainer);
            }

            // Create the notification element
            const notificationId = `notification-${Date.now()}`;
            const notificationElement = document.createElement('div');
            notificationElement.id = notificationId;
            notificationElement.className = `rounded-md p-4 shadow-lg transition-all duration-300 ease-out transform opacity-0 translate-x-4`;
            notificationElement.setAttribute('role', 'alert');

            // Apply type-specific classes
            const typeClasses = {
                success: 'bg-green-100 border-l-4 border-green-500 text-green-700 dark:bg-green-900 dark:border-green-600 dark:text-green-300',
                error: 'bg-red-100 border-l-4 border-red-500 text-red-700 dark:bg-red-900 dark:border-red-600 dark:text-red-300',
                warning: 'bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 dark:bg-yellow-900 dark:border-yellow-600 dark:text-yellow-300',
                info: 'bg-blue-100 border-l-4 border-blue-500 text-blue-700 dark:bg-blue-900 dark:border-blue-600 dark:text-blue-300',
            };
            notificationElement.classList.add(...(typeClasses[type] || typeClasses.info).split(' '));

            // Add content and close button
            notificationElement.innerHTML = `
                <div class="flex">
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium">${message}</p>
                    </div>
                    <div class="ml-auto pl-3">
                        <div class="-mx-1.5 -my-1.5">
                            <button type="button" class="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2">
                                <span class="sr-only">Dismiss</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // Add to container
            notificationContainer.appendChild(notificationElement);

            // Trigger enter animation
            requestAnimationFrame(() => {
                notificationElement.classList.remove('opacity-0', 'translate-x-4');
                notificationElement.classList.add('opacity-100', 'translate-x-0');
            });

            // Auto-dismiss after 5 seconds
            const dismissTimeout = setTimeout(() => {
                notificationElement.classList.remove('opacity-100', 'translate-x-0');
                notificationElement.classList.add('opacity-0', 'translate-x-4');
                setTimeout(() => notificationElement.remove(), 300); // Remove from DOM after transition
            }, 5000);

            // Dismiss on click
            notificationElement.querySelector('button').addEventListener('click', () => {
                clearTimeout(dismissTimeout);
                notificationElement.classList.remove('opacity-100', 'translate-x-0');
                notificationElement.classList.add('opacity-0', 'translate-x-4');
                setTimeout(() => notificationElement.remove(), 300);
            });
        }

        @php
            $hasOldInput = session()->hasOldInput();
            $loggedIn = Auth::check();
            $userHasAddresses = $loggedIn && count($addresses) > 0;

            // Initial state for useBillingForShipping:
            // Default to true (checked). If old input exists, it's true if 'use_billing_for_shipping' was submitted (i.e., checked).
            $initialUseBillingForShipping = true;
            if ($hasOldInput) {
                $initialUseBillingForShipping = !is_null(old('use_billing_for_shipping'));
            }

            // Initial state for showNewBillingAddressForm:
            // True if guest, or logged in with no addresses, or old input for new billing address name exists.
            // False if a saved billing address was selected from old input.
            $initialShowNewBillingAddressForm = (!$loggedIn || !$userHasAddresses || old('billing_address.first_name'));
            if (old('billing_address_id')) {
                $initialShowNewBillingAddressForm = false;
            }

            // Initial state for showNewShippingAddressForm:
            // True if useBillingForShipping is false AND (guest, or logged in with no addresses, or old input for new shipping name).
            // False if useBillingForShipping is false AND a saved shipping address was selected from old input.
            $initialShowNewShippingAddressForm = false;
            if (!$initialUseBillingForShipping) {
                $initialShowNewShippingAddressForm = (!$loggedIn || !$userHasAddresses || old('shipping_address.first_name'));
                if (old('shipping_address_id')) {
                    $initialShowNewShippingAddressForm = false;
                }
            }
        @endphp

        function checkoutForm() {
            return {
                selectedBillingAddressId: '{{ old('billing_address_id', $addresses->firstWhere('is_default_billing', true)?->id ?? '') }}',
                showNewBillingAddressForm: {{ Js::from($initialShowNewBillingAddressForm) }},
                useBillingForShipping: {{ Js::from($initialUseBillingForShipping) }},
                selectedShippingAddressId: '{{ old('shipping_address_id', $addresses->firstWhere('is_default_shipping', true)?->id ?? '') }}',
                showNewShippingAddressForm: {{ Js::from($initialShowNewShippingAddressForm) }},
                selectedShippingMethod: '{{ old('shipping_method', '') }}',
                selectedPaymentMethod: '{{ old('payment_method', '') }}',
                shippingMethods: @json($shippingMethods),
                cartSubtotal: {{ (float)$cart->subtotal }},
                currency: '{{ $cart->currency }}',
                loggedIn: {{ Js::from($loggedIn) }},
                userHasAddresses: {{ Js::from($userHasAddresses) }},
                isLoadingShippingMethods: false,
                shippingMethodsError: null,
                updateTimeout: null,

                init() {
                    this.$watch('selectedBillingAddressId', value => {
                        if (value) this.showNewBillingAddressForm = false;
                        else if (!this.loggedIn || !this.userHasAddresses) this.showNewBillingAddressForm = true;
                    });
                    this.$watch('selectedShippingAddressId', value => {
                        if (value && !this.useBillingForShipping) this.showNewShippingAddressForm = false;
                        else if (!this.useBillingForShipping && (!this.loggedIn || !this.userHasAddresses)) this.showNewShippingAddressForm = true;
                    });
                    this.$watch('useBillingForShipping', () => this.toggleShippingAddressForm());
                    this.toggleShippingAddressForm(); // Initial call to set correct visibility

                    // Watch for address changes to update shipping methods
                    this.$watch('selectedShippingAddressId', () => {
                        if (!this.showNewShippingAddressForm) {
                            // Using saved address - trigger update
                            setTimeout(() => this.updateShippingMethods(), 100);
                        }
                    });

                    this.$watch('selectedBillingAddressId', () => {
                        if (this.useBillingForShipping && !this.showNewBillingAddressForm) {
                            // Using saved billing address for shipping - trigger update
                            setTimeout(() => this.updateShippingMethods(), 100);
                        }
                    });

                    // Add event listeners for address form fields
                    this.$nextTick(() => {
                        // Listen for changes in new address forms
                        const addressFields = [
                            'select[name="shipping_address[country]"]',
                            'input[name="shipping_address[region]"]',
                            'input[name="shipping_address[city]"]',
                            'input[name="shipping_address[postal_code]"]',
                            'select[name="billing_address[country]"]',
                            'input[name="billing_address[region]"]',
                            'input[name="billing_address[city]"]',
                            'input[name="billing_address[postal_code]"]'
                        ];

                        addressFields.forEach(selector => {
                            const field = document.querySelector(selector);
                            if (field) {
                                field.addEventListener('change', () => {
                                    // Debounce the update
                                    clearTimeout(this.updateTimeout);
                                    this.updateTimeout = setTimeout(() => this.updateShippingMethods(), 500);
                                });
                            }
                        });

                        // Initial shipping method update on page load (for form re-renders)
                        setTimeout(() => this.updateShippingMethodsOnLoad(), 500);
                    });
                },

                selectBillingAddress(id) {
                    this.selectedBillingAddressId = id;
                    this.showNewBillingAddressForm = false;
                    // Ensure the hidden input is updated
                    document.querySelector('input[name="billing_address_id"][type="hidden"]').value = id;
                    // Update radio buttons
                    document.querySelectorAll('input[type="radio"][x-model="selectedBillingAddressId"]').forEach(radio => {
                        radio.checked = (radio.value === id);
                    });
                },

                selectShippingAddress(id) {
                    this.selectedShippingAddressId = id;
                    this.showNewShippingAddressForm = false;
                    // Ensure the hidden input is updated
                    document.querySelector('input[name="shipping_address_id"][type="hidden"]').value = id;
                    // Update radio buttons
                    document.querySelectorAll('input[type="radio"][x-model="selectedShippingAddressId"]').forEach(radio => {
                        radio.checked = (radio.value === id);
                    });
                },

                toggleShippingAddressForm() {
                    const shippingSection = document.getElementById('shipping-address-section'); // The whole section
                    const shippingAddressFormDiv = document.getElementById('shipping-address-form'); // The form itself
                    const shippingAddressSelectDiv = document.querySelector('#shipping-address-section select[name="shipping_address_id"]')?.closest('div.mb-6'); // The select dropdown div

                    if (this.useBillingForShipping) {
                        if(shippingSection) shippingSection.classList.add('hidden');
                        this.showNewShippingAddressForm = false; // Don't show new shipping form
                        this.selectedShippingAddressId = ''; // Clear selected shipping address
                        if(shippingAddressSelectDiv) shippingAddressSelectDiv.classList.add('hidden');
                        if(shippingAddressFormDiv) shippingAddressFormDiv.classList.add('hidden');

                    } else {
                        if(shippingSection) shippingSection.classList.remove('hidden');
                        if (this.userHasAddresses && this.selectedShippingAddressId) {
                            this.showNewShippingAddressForm = false;
                            if(shippingAddressSelectDiv) shippingAddressSelectDiv.classList.remove('hidden');
                            if(shippingAddressFormDiv) shippingAddressFormDiv.classList.add('hidden');
                        } else {
                            this.showNewShippingAddressForm = true;
                            if(shippingAddressSelectDiv && this.userHasAddresses) shippingAddressSelectDiv.classList.add('hidden'); // Hide select if adding new and user has addresses
                            if(shippingAddressFormDiv) shippingAddressFormDiv.classList.remove('hidden');
                        }
                    }
                },

                get shippingCost() {
                    if (!this.selectedShippingMethod) {
                        return 0; // No shipping method selected
                    }

                    const method = this.shippingMethods[this.selectedShippingMethod];
                    if (method) {
                        if (method.minimum_order && this.cartSubtotal < method.minimum_order && method.price === 0) {
                            const defaultPaidMethodKey = Object.keys(this.shippingMethods).find(key => {
                                const m = this.shippingMethods[key];
                                return m.price > 0 && (!m.minimum_order || this.cartSubtotal >= m.minimum_order);
                            });
                            return defaultPaidMethodKey ? this.shippingMethods[defaultPaidMethodKey].price : 0;
                        }
                        return method.price;
                    }
                    return 0;
                },

                get shippingCostText() {
                    if (!this.selectedShippingMethod) {
                        return '{{ __("Select a shipping method") }}';
                    }

                    const cost = this.shippingCost;
                    return cost > 0 ? `${this.currency} ${cost.toFixed(2)}` : '{{ __("Free") }}';
                },

                get totalCost() {
                    return this.cartSubtotal + this.shippingCost; // Taxes are server-side
                },

                get totalCostText() {
                    return `${this.currency} ${this.totalCost.toFixed(2)}`;
                },

                validateAndSubmit() {
                    let isValid = true;

                    if (!this.selectedShippingMethod) {
                        showNotification('warning', '{{ __('Please select a shipping method') }}');
                        isValid = false;
                    }

                    if (!this.selectedPaymentMethod) {
                        showNotification('warning', '{{ __('Please select a payment method') }}');
                        isValid = false;
                    }

                    if (isValid) {
                        document.getElementById('checkout-form').submit();
                    }
                },

                // Initial shipping method update on page load (for form re-renders)
                async updateShippingMethodsOnLoad() {
                    // Check if we have address data from form re-render
                    const hasAddressData = this.hasAddressDataFromForm();
                    if (hasAddressData) {
                        await this.updateShippingMethods();
                    }
                },

                // Check if form has address data (from validation errors/re-render)
                hasAddressDataFromForm() {
                    // Check if we have a selected address or filled form fields
                    if (this.useBillingForShipping) {
                        return this.selectedBillingAddressId || this.hasFilledBillingForm();
                    } else {
                        return this.selectedShippingAddressId || this.hasFilledShippingForm();
                    }
                },

                hasFilledBillingForm() {
                    const countryField = document.querySelector('select[name="billing_address[country]"]');
                    return countryField && countryField.value;
                },

                hasFilledShippingForm() {
                    const countryField = document.querySelector('select[name="shipping_address[country]"]');
                    return countryField && countryField.value;
                },

                // Update shipping methods when address changes
                async updateShippingMethods() {
                    if (this.isLoadingShippingMethods) return;

                    this.isLoadingShippingMethods = true;
                    this.shippingMethodsError = null;

                    try {
                        let requestData = {};

                        // Determine what address data to send
                        if (this.useBillingForShipping) {
                            // Using billing address for shipping
                            if (this.selectedBillingAddressId && !this.showNewBillingAddressForm) {
                                // Using saved billing address
                                requestData.address_id = this.selectedBillingAddressId;
                            } else if (this.showNewBillingAddressForm) {
                                // Using new billing address
                                const addressData = this.getBillingAddressData();
                                if (addressData && addressData.country) {
                                    requestData.address = addressData;
                                } else {
                                    // No valid address data
                                    this.isLoadingShippingMethods = false;
                                    return;
                                }
                            }
                        } else {
                            // Using separate shipping address
                            if (this.selectedShippingAddressId && !this.showNewShippingAddressForm) {
                                // Using saved shipping address
                                requestData.address_id = this.selectedShippingAddressId;
                            } else if (this.showNewShippingAddressForm) {
                                // Using new shipping address
                                const addressData = this.getShippingAddressData();
                                if (addressData && addressData.country) {
                                    requestData.address = addressData;
                                } else {
                                    // No valid address data
                                    this.isLoadingShippingMethods = false;
                                    return;
                                }
                            }
                        }

                        // If no address data, don't make request
                        if (!requestData.address_id && !requestData.address) {
                            this.isLoadingShippingMethods = false;
                            return;
                        }

                        const response = await fetch('{{ route('ajax.shipping-methods') }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify(requestData)
                        });

                        const data = await response.json();

                        if (data.success) {
                            this.shippingMethods = data.shipping_methods;

                            // Clear selected method if it's no longer available
                            if (this.selectedShippingMethod && !this.shippingMethods[this.selectedShippingMethod]) {
                                this.selectedShippingMethod = '';
                            }

                            // Auto-select first available method if none selected
                            if (!this.selectedShippingMethod && Object.keys(this.shippingMethods).length > 0) {
                                this.selectedShippingMethod = Object.keys(this.shippingMethods)[0];
                            }
                        } else {
                            this.shippingMethodsError = data.message || 'Failed to load shipping methods';
                        }
                    } catch (error) {
                        console.error('Error fetching shipping methods:', error);
                        this.shippingMethodsError = 'Failed to load shipping methods. Please try again.';
                    } finally {
                        this.isLoadingShippingMethods = false;
                    }
                },

                // Get current shipping address data
                getShippingAddressData() {
                    if (this.useBillingForShipping) {
                        return this.getBillingAddressData();
                    }

                    if (this.selectedShippingAddressId && !this.showNewShippingAddressForm) {
                        // Using saved address - we can't easily get the data here
                        // This would require additional AJAX call or storing address data in frontend
                        return null;
                    }

                    if (this.showNewShippingAddressForm) {
                        return {
                            country: document.querySelector('select[name="shipping_address[country]"]')?.value || '',
                            region: document.querySelector('input[name="shipping_address[region]"]')?.value || '',
                            city: document.querySelector('input[name="shipping_address[city]"]')?.value || '',
                            postal_code: document.querySelector('input[name="shipping_address[postal_code]"]')?.value || '',
                            address_line1: document.querySelector('input[name="shipping_address[address_line1]"]')?.value || ''
                        };
                    }

                    return null;
                },

                // Get billing address data
                getBillingAddressData() {
                    if (this.selectedBillingAddressId && !this.showNewBillingAddressForm) {
                        // Using saved address - we can't easily get the data here
                        return null;
                    }

                    if (this.showNewBillingAddressForm) {
                        return {
                            country: document.querySelector('select[name="billing_address[country]"]')?.value || '',
                            region: document.querySelector('input[name="billing_address[region]"]')?.value || '',
                            city: document.querySelector('input[name="billing_address[city]"]')?.value || '',
                            postal_code: document.querySelector('input[name="billing_address[postal_code]"]')?.value || '',
                            address_line1: document.querySelector('input[name="billing_address[address_line1]"]')?.value || ''
                        };
                    }

                    return null;
                }
            }
        }
    </script>
    <div class="py-12" x-data="checkoutForm()">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('error'))
                <div class="mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-md" role="alert">
                    <div class="flex">
                        <div class="py-1"><x-svg.warning-circle-filled class="h-6 w-6 text-red-500 mr-3" /></div>
                        <div>
                            <p class="font-bold">{{ __('Error') }}</p>
                            <p class="text-sm">{{ session('error') }}</p>
                        </div>
                    </div>
                </div>
            @endif
            @if ($errors->any())
                <div class="mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-md" role="alert">
                    <div class="flex">
                        <div class="py-1"><x-svg.warning-circle-filled class="h-6 w-6 text-red-500 mr-3" /></div>
                        <div>
                            <p class="font-bold">{{ __('Please correct the errors below:') }}</p>
                            <ul class="mt-1 list-disc list-inside text-sm">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            @endif


            <form method="POST" action="{{ route('checkout.process') }}" id="checkout-form">
                @csrf
                <input type="hidden" name="checkout_id" value="{{ $checkoutId }}">
                <!-- Hidden inputs to ensure address IDs are always sent when selected -->
                <input type="hidden" name="billing_address_id" :value="selectedBillingAddressId" x-model="selectedBillingAddressId">
                <input type="hidden" name="shipping_address_id" :value="selectedShippingAddressId" x-model="selectedShippingAddressId">

                <div class="flex flex-col lg:flex-row gap-8">
                    <!-- Main Checkout Form Area -->
                    <div class="lg:w-2/3 space-y-8">
                        <!-- Customer Information & Billing Address -->
                        <section class="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 border-b dark:border-gray-700 pb-3">
                                <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-indigo-500 text-white mr-3">1</span>
                                {{ __('Billing Information') }}
                            </h3>

                            @if(Auth::check() && count($addresses) > 0)
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        {{ __('Select a saved billing address') }}
                                    </label>
                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        @foreach($addresses as $address) {{-- $address is an Address model instance --}}
                                        <label
                                            @click="selectBillingAddress('{{ $address->id }}')"
                                            :class="{ 'ring-2 ring-indigo-500 border-indigo-500 dark:border-indigo-400': selectedBillingAddressId === '{{ $address->id }}', 'border-gray-300 dark:border-gray-600': selectedBillingAddressId !== '{{ $address->id }}' }"
                                            class="block p-4 border rounded-lg cursor-pointer hover:border-indigo-500 dark:hover:border-indigo-400 transition-all">
                                            <input type="radio" value="{{ $address->id }}" class="hidden" x-model="selectedBillingAddressId">
                                            {{-- For saved addresses, use the authenticated user's name --}}
                                            <p class="font-medium text-gray-900 dark:text-white">{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}</p>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $address->address_line1 }}</p>
                                            @if($address->address_line2)<p class="text-sm text-gray-600 dark:text-gray-400">{{ $address->address_line2 }}</p>@endif
                                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $address->city }}, {{ $address->region }} {{ $address->postal_code }}</p>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ \Symfony\Component\Intl\Countries::getName($address->country, app()->getLocale()) }}</p>
                                        </label>
                                        @endforeach
                                    </div>
                                    <button type="button" @click="showNewBillingAddressForm = !showNewBillingAddressForm; selectedBillingAddressId = ''" class="mt-4 text-sm text-indigo-600 dark:text-indigo-400 hover:underline">
                                        <span x-text="showNewBillingAddressForm ? '{{ __('Cancel adding new address') }}' : '{{ __('+ Add a new billing address') }}'"></span>
                                    </button>
                                </div>
                            @else
                                {{-- For guests or users with no saved addresses, ensure the new address form is shown by default --}}
                                {{-- <input type="hidden" x-init="showNewBillingAddressForm = true; selectedBillingAddressId = '';"> --}} {{-- This logic is now handled by initial data properties --}}
                            @endif

                            <div x-show="showNewBillingAddressForm" x-transition class="space-y-4" id="billing-address-form"> {{-- Added ID for targeting --}}
                                @include('checkout.partials.address-form', [
                                    'type' => 'billing',
                                    'countries' => $countries,
                                    'address' => null
                                ])
                                @if(Auth::check())
                                <div class="flex items-center">
                                    <input type="checkbox" name="save_billing_address" id="save_billing_address" value="1" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                                    <label for="save_billing_address" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">{{ __('Save this address for future use') }}</label>
                                    </div>
                                    @endif
                                </div>
                            </section>

                        <!-- Shipping Address -->
                        <section class="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6" id="shipping-address-section">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 border-b dark:border-gray-700 pb-3">
                                <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-indigo-500 text-white mr-3">2</span>
                                {{ __('Shipping Information') }}
                            </h3>
                            <div class="mb-4">
                                <div class="flex items-center">
                                    <input id="use_billing_for_shipping" name="use_billing_for_shipping" type="checkbox" value="1" x-model="useBillingForShipping" @change="toggleShippingAddressForm()" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                                    <label for="use_billing_for_shipping" class="ml-2 block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Shipping address is the same as billing address') }}</label>
                                </div>
                            </div>

                            <div x-show="!useBillingForShipping" x-transition class="space-y-6" >
                                @if(Auth::check() && count($addresses) > 0)
                                    <div class="mb-6">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            {{ __('Select a saved shipping address') }}
                                        </label>
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            @foreach($addresses as $address)
                                            <label
                                                @click="selectShippingAddress('{{ $address->id }}')"
                                                :class="{ 'ring-2 ring-indigo-500 border-indigo-500 dark:border-indigo-400': selectedShippingAddressId === '{{ $address->id }}', 'border-gray-300 dark:border-gray-600': selectedShippingAddressId !== '{{ $address->id }}' }"
                                                class="block p-4 border rounded-lg cursor-pointer hover:border-indigo-500 dark:hover:border-indigo-400 transition-all">
                                                <input type="radio" value="{{ $address->id }}" class="hidden" x-model="selectedShippingAddressId">
                                                <p class="font-medium text-gray-900 dark:text-white">{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}</p>
                                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $address->address_line1 }}</p>
                                                @if($address->address_line2)<p class="text-sm text-gray-600 dark:text-gray-400">{{ $address->address_line2 }}</p>@endif
                                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $address->city }}, {{ $address->region }} {{ $address->postal_code }}</p>
                                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ \Symfony\Component\Intl\Countries::getName($address->country, app()->getLocale()) }}</p>
                                            </label>
                                            @endforeach
                                        </div>
                                        <button type="button" @click="showNewShippingAddressForm = !showNewShippingAddressForm; selectedShippingAddressId = ''" class="mt-4 text-sm text-indigo-600 dark:text-indigo-400 hover:underline">
                                            <span x-text="showNewShippingAddressForm ? '{{ __('Cancel adding new address') }}' : '{{ __('+ Add a new shipping address') }}'"></span>
                                        </button>
                                    </div>
                                @else
                                    {{-- Removed problematic x-init. Logic moved to Alpine component data properties. --}}
                                @endif

                                <div x-show="showNewShippingAddressForm && !useBillingForShipping" x-transition class="space-y-4" id="shipping-address-form"> {{-- Added ID for targeting --}}
                                    @include('checkout.partials.address-form', [
                                        'type' => 'shipping',
                                        'countries' => $countries,
                                        'address' => null
                                    ])
                                    @if(Auth::check())
                                    <div class="flex items-center">
                                        <input type="checkbox" name="save_shipping_address" id="save_shipping_address" value="1" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                                        <label for="save_shipping_address" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">{{ __('Save this address for future use') }}</label>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </section>

                        <!-- Shipping Method -->
                        <section class="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 border-b dark:border-gray-700 pb-3">
                                <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-indigo-500 text-white mr-3">3</span>
                                {{ __('Shipping Method') }}
                            </h3>
                            <div class="space-y-4">
                                <p class="text-sm text-gray-700 dark:text-gray-300 mb-4">{{ __('Please select a shipping method:') }}</p>

                                <!-- Loading State -->
                                <div x-show="isLoadingShippingMethods" class="flex items-center justify-center py-8">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
                                    <span class="ml-3 text-gray-600 dark:text-gray-400">{{ __('Loading shipping methods...') }}</span>
                                </div>

                                <!-- Error State -->
                                <div x-show="shippingMethodsError" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                                    <p x-text="shippingMethodsError"></p>
                                    <button @click="updateShippingMethods()" class="mt-2 text-sm underline hover:no-underline">
                                        {{ __('Try again') }}
                                    </button>
                                </div>

                                <!-- Shipping Methods -->
                                <div x-show="!isLoadingShippingMethods && !shippingMethodsError" class="space-y-4">
                                    <template x-for="(method, key) in shippingMethods" :key="key">
                                        <label
                                            @click="selectedShippingMethod = key"
                                            :class="{ 'ring-2 ring-indigo-500 border-indigo-500 dark:border-indigo-400': selectedShippingMethod === key, 'border-gray-300 dark:border-gray-600': selectedShippingMethod !== key }"
                                            class="flex items-start p-4 border rounded-lg cursor-pointer hover:border-indigo-500 dark:hover:border-indigo-400 transition-all">
                                            <input type="radio" name="shipping_method" :value="key" class="mt-1 h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500" x-model="selectedShippingMethod">
                                            <div class="ml-3 flex-1">
                                                <div class="flex justify-between">
                                                    <span class="font-medium text-gray-900 dark:text-white" x-text="method.name"></span>
                                                    <span class="font-medium text-gray-900 dark:text-white">
                                                        <span x-show="method.price > 0" x-text="`{{ $cart->currency }} ${parseFloat(method.price).toFixed(2)}`"></span>
                                                        <span x-show="method.price <= 0">{{ __('Free') }}</span>
                                                    </span>
                                                </div>
                                                <p class="text-sm text-gray-600 dark:text-gray-400" x-text="method.description"></p>
                                                <p x-show="method.minimum_order" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                    {{ __('Minimum order value') }}: <span x-text="`{{ $cart->currency }} ${parseFloat(method.minimum_order || 0).toFixed(2)}`"></span>
                                                </p>
                                            </div>
                                        </label>
                                    </template>

                                    <!-- Fallback for when no shipping methods are available -->
                                    <div x-show="Object.keys(shippingMethods).length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
                                        <p>{{ __('No shipping methods available for your address.') }}</p>
                                        <p class="text-sm mt-2">{{ __('Please check your address or contact support.') }}</p>
                                    </div>
                                </div>
                            @error('shipping_method')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @else
                                <p class="text-red-500 text-xs mt-1" x-show="!selectedShippingMethod">{{ __('Please select a shipping method') }}</p>
                            @enderror
                        </section>

                        <!-- Payment Method -->
                        <section class="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 border-b dark:border-gray-700 pb-3">
                                <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-indigo-500 text-white mr-3">4</span>
                                {{ __('Payment Method') }}
                            </h3>
                            <div class="space-y-4">
                                <p class="text-sm text-gray-700 dark:text-gray-300 mb-4">{{ __('Please select a payment method:') }}</p>
                                @foreach($paymentMethods as $key => $method)
                                    <label
                                        @click="selectedPaymentMethod = '{{ $key }}'"
                                        :class="{ 'ring-2 ring-indigo-500 border-indigo-500 dark:border-indigo-400': selectedPaymentMethod === '{{ $key }}', 'border-gray-300 dark:border-gray-600': selectedPaymentMethod !== '{{ $key }}' }"
                                        class="flex items-start p-4 border rounded-lg cursor-pointer hover:border-indigo-500 dark:hover:border-indigo-400 transition-all">
                                        <input type="radio" name="payment_method" value="{{ $key }}" class="mt-1 h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500" x-model="selectedPaymentMethod" @keydown.arrow-up.prevent @keydown.arrow-down.prevent>
                                        <div class="ml-3 flex-1">
                                            <div class="flex items-center">
                                                @if($method['icon'] === 'fa-credit-card')
                                                    <i class="fas fa-credit-card mr-2 text-gray-600 dark:text-gray-300"></i>
                                                @elseif($method['icon'] === 'fa-paypal')
                                                    <i class="fab fa-paypal mr-2 text-blue-600 dark:text-blue-400"></i>
                                                @elseif($method['icon'] === 'fa-university')
                                                    <i class="fas fa-university mr-2 text-gray-600 dark:text-gray-300"></i>
                                                @else
                                                    {{-- Fallback for other Font Awesome icons, assuming $method['icon'] is like 'fa-some-icon' --}}
                                                    <i class="fas {{ $method['icon'] }} mr-2 text-gray-600 dark:text-gray-300"></i>
                                                @endif
                                                <span class="font-medium text-gray-900 dark:text-white">{{ $method['name'] }}</span>
                                            </div>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $method['description'] }}</p>
                                        </div>
                                    </label>
                                @endforeach
                            </div>
                            @error('payment_method')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @else
                                <p class="text-red-500 text-xs mt-1" x-show="!selectedPaymentMethod">{{ __('Please select a payment method') }}</p>
                            @enderror
                        </section>

                        <!-- Terms and Conditions -->
                        <div class="mt-8 bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6">
                            <div class="relative flex items-start">
                                <div class="flex items-center h-5">
                                    <input id="terms_accepted" name="terms_accepted" type="checkbox" value="1" class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                </div>
                                <div class="ml-3 text-sm">
                                    <label for="terms_accepted" class="font-medium text-gray-700 dark:text-gray-300">
                                        {{ __('I have read and agree to the') }} <a href="{{ route('terms') }}" class="text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300 underline" target="_blank">{{ __('Terms and Conditions') }}</a> {{ __('and') }} <a href="{{ route('privacy') }}" class="text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300 underline" target="_blank">{{ __('Privacy Policy') }}</a>.
                                    </label>
                                </div>
                            </div>
                            @error('terms_accepted') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror
                        </div>
                    </div>

                    <!-- Order Summary (Sticky Sidebar) -->
                    <div class="lg:w-1/3">
                        <div class="sticky top-24">
                            <section class="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6">
                                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 border-b dark:border-gray-700 pb-3">
                                    {{ __('Order Summary') }}
                                </h3>
                                <div class="space-y-4">
                                    @foreach($cart->items as $item)
                                        <div class="flex items-start py-3 {{ !$loop->last ? 'border-b border-gray-200 dark:border-gray-700' : '' }}">
                                            <div class="w-16 h-16 flex-shrink-0 bg-gray-200 dark:bg-gray-700 rounded-md overflow-hidden">
                                                @php
                                                    $thumbnailUrl = '';
                                                    if ($item->productVariant && $item->productVariant->product) {
                                                        try { $thumbnailUrl = $item->productVariant->product->getFirstMediaUrl('thumbnail'); } catch (\Exception $e) {}
                                                    }
                                                @endphp
                                                @if($thumbnailUrl)
                                                    <img src="{{ $thumbnailUrl }}" alt="{{ $item->productVariant->product->name }}" class="w-full h-full object-cover">
                                                @else
                                                    <div class="w-full h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
                                                        <x-svg.product-placeholder class="h-8 w-8" />
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="ml-4 flex-1">
                                                <h4 class="font-medium text-gray-900 dark:text-white text-sm">{{ $item->productVariant->product->name }}</h4>
                                                <p class="text-xs text-gray-600 dark:text-gray-400">{{ $item->productVariant->name }}</p>
                                                <div class="flex justify-between mt-1">
                                                    <span class="text-xs text-gray-600 dark:text-gray-400">{{ __('Qty') }}: {{ $item->quantity }}</span>
                                                    <span class="font-medium text-sm text-gray-900 dark:text-white">{{ $cart->currency }} {{ number_format($item->unit_price * $item->quantity, 2) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 space-y-2">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600 dark:text-gray-400">{{ __('Subtotal') }}:</span>
                                        <span class="text-gray-900 dark:text-white">{{ $cart->currency }} {{ number_format($cart->subtotal, 2) }}</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600 dark:text-gray-400">{{ __('Shipping') }}:</span>
                                        <span class="text-gray-900 dark:text-white" x-text="shippingCostText">Calculated at next step</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600 dark:text-gray-400">{{ __('Taxes') }}:</span>
                                        <span class="text-gray-900 dark:text-white">Calculated at next step</span>
                                    </div>
                                    <div class="flex justify-between font-bold text-lg pt-2 border-t border-gray-200 dark:border-gray-700 mt-2">
                                        <span class="text-gray-900 dark:text-white">{{ __('Estimated Total') }}:</span>
                                        <span class="text-gray-900 dark:text-white" x-text="totalCostText">{{ $cart->currency }} {{ number_format($cart->subtotal, 2) }}</span>
                                    </div>
                                </div>

                                <div class="mt-8">
                                    <button type="submit"
                                        class="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-semibold py-3 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                                        @click.prevent="validateAndSubmit()">
                                        {{ __('Place Order') }}
                                    </button>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    @endsection

    {{-- The script is now defined above, so we remove it from the @push directive --}}
</x-app-layout>
