@extends('layouts.app')

@section('header')
    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
        {{ __('Payment') }}
    </h2>
@endsection

@section('content')
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Order Summary -->
                        <div class="md:col-span-1">
                            <h3 class="text-lg font-semibold mb-4">{{ __('Order Summary') }}</h3>
                            <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                                <p class="mb-2"><span class="font-medium">{{ __('Order Number') }}:</span> {{ $order->order_number }}</p>
                                <p class="mb-2"><span class="font-medium">{{ __('Date') }}:</span> {{ $order->created_at->format('M d, Y') }}</p>
                                <p class="mb-4"><span class="font-medium">{{ __('Status') }}:</span>
                                    <span class="px-2 py-1 text-xs rounded-full
                                        @if($order->status === 'pending') bg-yellow-200 text-yellow-800
                                        @elseif($order->status === 'processing') bg-blue-200 text-blue-800
                                        @elseif($order->status === 'shipped') bg-indigo-200 text-indigo-800
                                        @elseif($order->status === 'delivered') bg-green-200 text-green-800
                                        @elseif($order->status === 'cancelled') bg-red-200 text-red-800
                                        @elseif($order->status === 'refunded') bg-gray-200 text-gray-800
                                        @endif">
                                        {{ ucfirst($order->status) }}
                                    </span>
                                </p>

                                <div class="border-t border-gray-300 dark:border-gray-600 pt-4 mb-4">
                                    <p class="flex justify-between mb-2">
                                        <span>{{ __('Subtotal') }}:</span>
                                        <span>{{ $order->currency }} {{ number_format($order->subtotal, 2) }}</span>
                                    </p>
                                    <p class="flex justify-between mb-2">
                                        <span>{{ __('Shipping') }}:</span>
                                        <span>{{ $order->currency }} {{ number_format($order->shipping_cost, 2) }}</span>
                                    </p>
                                    <p class="flex justify-between mb-2">
                                        <span>{{ __('Tax') }}:</span>
                                        <span>{{ $order->currency }} {{ number_format($order->tax_amount, 2) }}</span>
                                    </p>
                                    @if($order->discount_amount > 0)
                                    <p class="flex justify-between mb-2">
                                        <span>{{ __('Discount') }}:</span>
                                        <span>-{{ $order->currency }} {{ number_format($order->discount_amount, 2) }}</span>
                                    </p>
                                    @endif
                                    <p class="flex justify-between font-bold mt-2 pt-2 border-t border-gray-300 dark:border-gray-600">
                                        <span>{{ __('Total') }}:</span>
                                        <span>{{ $order->currency }} {{ number_format($order->total, 2) }}</span>
                                    </p>
                                </div>

                                <div class="text-sm">
                                    <p class="mb-1"><span class="font-medium">{{ __('Shipping Address') }}:</span></p>
                                    <p class="mb-4">
                                        {{ $order->shippingAddress->address_line1 }}<br>
                                        @if($order->shippingAddress->address_line2)
                                            {{ $order->shippingAddress->address_line2 }}<br>
                                        @endif
                                        {{ $order->shippingAddress->city }},
                                        @if($order->shippingAddress->state)
                                            {{ $order->shippingAddress->state }},
                                        @endif
                                        {{ $order->shippingAddress->postal_code }}<br>
                                        {{ $order->shippingAddress->country }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Form -->
                        <div class="md:col-span-2">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('Payment Details') }}</h3>
                                <div class="flex items-center text-sm text-green-600 dark:text-green-400">
                                    <i class="fas fa-shield-alt mr-2"></i>
                                    <span>{{ __('Secured by Stripe') }}</span>
                                </div>
                            </div>

                            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 shadow-sm">
                                <form id="payment-form" class="space-y-6">
                                    <!-- Card Information Section -->
                                    <div class="space-y-4">
                                        <div class="flex items-center justify-between">
                                            <label for="card-element" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                <i class="fas fa-credit-card mr-2 text-indigo-500"></i>
                                                {{ __('Card Information') }}
                                            </label>
                                            <!-- Accepted Payment Methods (Inline) -->
                                            <div class="flex space-x-2">
                                                <i class="fab fa-cc-visa text-lg text-blue-600" title="Visa"></i>
                                                <i class="fab fa-cc-mastercard text-lg text-red-500" title="Mastercard"></i>
                                                <i class="fab fa-cc-amex text-lg text-blue-500" title="American Express"></i>
                                                <i class="fab fa-cc-discover text-lg text-orange-500" title="Discover"></i>
                                            </div>
                                        </div>

                                        <div id="card-element" class="border border-gray-300 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-700 transition-colors focus-within:border-indigo-500 focus-within:ring-1 focus-within:ring-indigo-500">
                                            <!-- Stripe Elements will be inserted here -->
                                        </div>
                                        <div id="card-errors" class="text-red-500 text-sm mt-2" role="alert"></div>
                                    </div>

                                    <!-- Security Notice -->
                                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                                        <div class="flex items-start">
                                            <i class="fas fa-lock text-green-500 mr-3 mt-0.5"></i>
                                            <div class="text-sm">
                                                <p class="font-medium text-gray-900 dark:text-white mb-1">{{ __('Your payment is secure') }}</p>
                                                <p class="text-gray-600 dark:text-gray-400">{{ __('Your payment information is encrypted and processed securely by Stripe. We never store your card details.') }}</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Payment Button -->
                                    <button id="submit-button" type="submit" class="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold py-4 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] disabled:hover:scale-100 shadow-lg hover:shadow-xl disabled:shadow-md flex items-center justify-center space-x-2">
                                        <span id="button-text" class="flex items-center space-x-2">
                                            <i class="fas fa-lock text-sm"></i>
                                            <span>{{ __('Pay') }} {{ $order->currency }} {{ number_format($order->total, 2) }}</span>
                                        </span>
                                        <div id="spinner" class="hidden flex items-center space-x-2">
                                            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            <span>{{ __('Processing Payment...') }}</span>
                                        </div>
                                    </button>

                                    <!-- Trust Indicators -->
                                    <div class="flex items-center justify-center space-x-6 mt-4 text-xs text-gray-500 dark:text-gray-400">
                                        <div class="flex items-center space-x-1">
                                            <i class="fas fa-shield-alt text-green-500"></i>
                                            <span>{{ __('SSL Secured') }}</span>
                                        </div>
                                        <div class="flex items-center space-x-1">
                                            <i class="fab fa-stripe text-indigo-500"></i>
                                            <span>{{ __('Powered by Stripe') }}</span>
                                        </div>
                                        <div class="flex items-center space-x-1">
                                            <i class="fas fa-user-shield text-blue-500"></i>
                                            <span>{{ __('PCI Compliant') }}</span>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <div class="mt-6 text-center">
                                <a href="{{ route('orders.show', $order) }}" class="text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300 font-medium">
                                    {{ __('Cancel and return to order details') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script src="https://js.stripe.com/v3/"></script>
<script>
    // Pass the order number and URLs to JavaScript
    const orderNumber = @json($order->order_number ?? '');
    const cancelUrl = @json(route('checkout.stripe.cancel', ['order_id' => $order->order_number]));
    const confirmationUrl = @json(route('checkout.confirmation'));

    console.log('Order number:', orderNumber);
    console.log('Cancel URL:', cancelUrl);
    console.log('Confirmation URL:', confirmationUrl);

    document.addEventListener('DOMContentLoaded', function() {
        // Check if Stripe is loaded
        if (typeof Stripe === 'undefined') {
            console.error('Stripe SDK failed to load');
            document.getElementById('card-errors').textContent = 'Payment system is temporarily unavailable. Please refresh the page and try again.';
            return;
        }

        // Check if public key is available
        const publicKey = @json($publicKey ?? '');
        console.log('Stripe public key:', publicKey ? 'Available' : 'Missing');
        if (!publicKey) {
            console.error('Stripe public key is not configured');
            document.getElementById('card-errors').textContent = 'Payment configuration error. Please contact support.';
            return;
        }

        // Check if client secret is available
        const clientSecret = @json($clientSecret ?? '');
        console.log('Stripe client secret:', clientSecret ? 'Available' : 'Missing');
        if (!clientSecret) {
            console.error('Stripe client secret is not available');
            document.getElementById('card-errors').textContent = 'Payment session expired. Please refresh the page and try again.';
            return;
        }

        try {
            // Initialize Stripe
            const stripe = Stripe(publicKey);
            const elements = stripe.elements();

        // Create card element with modern styling
        const style = {
            base: {
                color: document.documentElement.classList.contains('dark') ? '#ffffff' : '#1f2937',
                fontFamily: '"Figtree", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                fontSmoothing: 'antialiased',
                fontSize: '16px',
                lineHeight: '24px',
                '::placeholder': {
                    color: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                },
                backgroundColor: 'transparent',
                iconColor: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
            },
            invalid: {
                color: '#ef4444',
                iconColor: '#ef4444'
            },
            complete: {
                color: document.documentElement.classList.contains('dark') ? '#10b981' : '#059669',
                iconColor: document.documentElement.classList.contains('dark') ? '#10b981' : '#059669'
            }
        };

        const cardElement = elements.create('card', { style: style });
        cardElement.mount('#card-element');

        // Handle form submission
        const form = document.getElementById('payment-form');
        const submitButton = document.getElementById('submit-button');
        const buttonText = document.getElementById('button-text');
        const spinner = document.getElementById('spinner');
        const cardErrors = document.getElementById('card-errors');

        form.addEventListener('submit', async (event) => {
            event.preventDefault();

            // Validate form before processing
            if (!validateForm()) {
                return;
            }

            // Set loading state
            setLoadingState(true);
            clearErrors();

            try {
                // Create payment method first (modern approach)
                const { paymentMethod, error: pmError } = await stripe.createPaymentMethod({
                    type: 'card',
                    card: cardElement,
                    billing_details: {
                        name: @json($order->customer_name ?? $order->user->name ?? ''),
                        email: @json($order->customer_email ?? $order->user->email ?? ''),
                        address: {
                            line1: @json($order->billingAddress->address_line1 ?? $order->shippingAddress->address_line1 ?? ''),
                            city: @json($order->billingAddress->city ?? $order->shippingAddress->city ?? ''),
                            state: @json($order->billingAddress->region ?? $order->shippingAddress->region ?? ''),
                            postal_code: @json($order->billingAddress->postal_code ?? $order->shippingAddress->postal_code ?? ''),
                            country: @json($order->billingAddress->country ?? $order->shippingAddress->country ?? ''),
                        }
                    }
                });

                if (pmError) {
                    throw new Error(pmError.message);
                }

                // Confirm payment with the created payment method
                const { paymentIntent, error } = await stripe.confirmCardPayment(clientSecret, {
                    payment_method: paymentMethod.id,
                    return_url: window.location.origin + '/payments/' + orderNumber + '?payment_method=stripe',
                });

                if (error) {
                    // Handle different types of errors
                    handlePaymentError(error);
                } else if (paymentIntent.status === 'succeeded') {
                    // Payment successful
                    showSuccess('Payment successful! Redirecting...');
                    setTimeout(() => {
                        window.location.href = @json(route('checkout.confirmStripePayment', $order->order_number)) +
                            '?payment_intent=' + paymentIntent.id +
                            '&payment_intent_status=' + paymentIntent.status;
                    }, 1000);
                } else if (paymentIntent.status === 'requires_action') {
                    // Handle 3D Secure authentication
                    const { error: confirmError } = await stripe.confirmCardPayment(clientSecret);
                    if (confirmError) {
                        handlePaymentError(confirmError);
                    }
                } else {
                    // Handle other statuses
                    handlePaymentError({ message: 'Payment could not be processed. Please try again.' });
                }
            } catch (error) {
                console.error('Payment processing error:', error);
                handlePaymentError(error);
            }

            function handlePaymentError(error) {
                setLoadingState(false);

                // Categorize errors for better user experience
                let userMessage = error.message || 'An unexpected error occurred. Please try again.';

                if (error.type === 'card_error') {
                    // Card-specific errors (declined, insufficient funds, etc.)
                    userMessage = error.message;
                } else if (error.type === 'validation_error') {
                    // Validation errors
                    userMessage = 'Please check your card information and try again.';
                } else if (error.payment_intent &&
                          (error.payment_intent.status === 'requires_payment_method' ||
                           error.payment_intent.status === 'canceled')) {
                    // Redirect to payment page for serious errors
                    window.location.href = cancelUrl + '?error=' + encodeURIComponent(error.message);
                    return;
                }

                showError(userMessage);
            }
        });

            // Handle real-time validation errors from the card Element
            cardElement.addEventListener('change', (event) => {
                const cardElement = document.getElementById('card-element');

                if (event.error) {
                    showError(event.error.message);
                    cardElement.classList.add('border-red-500');
                    cardElement.classList.remove('border-green-500');
                } else if (event.complete) {
                    clearErrors();
                    cardElement.classList.add('border-green-500');
                    cardElement.classList.remove('border-red-500');
                } else {
                    clearErrors();
                    cardElement.classList.remove('border-red-500', 'border-green-500');
                }
            });

            // Helper functions
            function validateForm() {
                // Add any additional validation here
                return true;
            }

            function setLoadingState(loading) {
                submitButton.disabled = loading;
                if (loading) {
                    buttonText.classList.add('hidden');
                    spinner.classList.remove('hidden');
                    submitButton.classList.add('cursor-not-allowed');
                } else {
                    buttonText.classList.remove('hidden');
                    spinner.classList.add('hidden');
                    submitButton.classList.remove('cursor-not-allowed');
                }
            }

            function showError(message) {
                cardErrors.textContent = message;
                cardErrors.classList.remove('hidden');
            }

            function clearErrors() {
                cardErrors.textContent = '';
                cardErrors.classList.add('hidden');
            }

            function showSuccess(message) {
                // You can implement a success message display here
                console.log('Success:', message);
            }

        } catch (error) {
            console.error('Stripe initialization error:', error);
            showError('Failed to initialize payment system. Please refresh the page and try again.');
        }
    });
</script>
@endpush
