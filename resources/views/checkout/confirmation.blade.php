@extends('layouts.app')

@section('header')
    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
        {{ __('Order Confirmation') }}
    </h2>
@endsection

@section('content')
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="mb-8 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-l-4 border-green-500 text-green-700 dark:text-green-300 p-6 rounded-lg shadow-md" role="alert">
                    <div class="flex items-center">
                        <x-svg.checkmark-circle-filled class="h-6 w-6 text-green-500 mr-3" />
                        <span class="font-medium">{{ session('success') }}</span>
                    </div>
                </div>
            @endif

            <!-- Success Header Section -->
            <div class="text-center mb-12">
                <div class="relative">
                    <!-- Animated Success Icon -->
                    <div class="inline-flex items-center justify-center w-24 h-24 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 text-white mb-6 shadow-lg animate-pulse">
                        <x-svg.checkmark-circle-filled class="h-12 w-12" />
                    </div>

                    <!-- Celebration Elements -->
                    <div class="absolute -top-2 -left-2 w-4 h-4 bg-yellow-400 rounded-full animate-bounce"></div>
                    <div class="absolute -top-1 -right-3 w-3 h-3 bg-pink-400 rounded-full animate-bounce" style="animation-delay: 0.1s;"></div>
                    <div class="absolute -bottom-1 -left-3 w-2 h-2 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.2s;"></div>
                    <div class="absolute -bottom-2 -right-2 w-3 h-3 bg-purple-400 rounded-full animate-bounce" style="animation-delay: 0.3s;"></div>
                </div>

                <h1 class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-4">
                    {{ __('Order Confirmed!') }}
                </h1>
                <p class="text-xl text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto">
                    {{ __('Thank you for your purchase! Your order has been successfully placed and is now being processed.') }}
                </p>

                <!-- Order Number Highlight -->
                <div class="inline-flex items-center bg-white dark:bg-gray-800 rounded-full px-6 py-3 shadow-lg border border-gray-200 dark:border-gray-700">
                    <x-svg.document-text class="h-5 w-5 text-indigo-500 mr-3" />
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400 mr-2">{{ __('Order Number') }}:</span>
                    <span class="text-lg font-bold text-indigo-600 dark:text-indigo-400">{{ $order->order_number }}</span>
                </div>
            </div>

            <!-- Main Content Layout -->
            <div class="flex flex-col lg:flex-row gap-8">
                <!-- Left Column: Order Details -->
                <div class="lg:w-2/3 space-y-8">
                    <!-- Order Summary Section -->
                    <section class="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 border-b dark:border-gray-700 pb-3">
                            <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-indigo-500 text-white mr-3">1</span>
                            {{ __('Order Summary') }}
                        </h3>

                        <!-- Product Items -->
                        <div class="space-y-4">
                            @foreach($order->items as $item)
                                <div class="flex items-start py-4 {{ !$loop->last ? 'border-b border-gray-200 dark:border-gray-700' : '' }}">
                                    <div class="w-16 h-16 flex-shrink-0 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
                                        @if($item->product_image)
                                            <img src="{{ $item->product_image }}" alt="{{ $item->product_name }}" class="w-full h-full object-cover">
                                        @else
                                            <div class="w-full h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
                                                <x-svg.product-placeholder class="h-8 w-8" />
                                            </div>
                                        @endif
                                    </div>
                                    <div class="ml-4 flex-1">
                                        <h4 class="font-medium text-gray-900 dark:text-white text-base">{{ $item->product_name }}</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $item->variant_name }}</p>
                                        <div class="flex justify-between items-center mt-2">
                                            <span class="text-sm text-gray-600 dark:text-gray-400">
                                                {{ __('Qty') }}: <span class="font-medium">{{ $item->quantity }}</span>
                                            </span>
                                            <div class="text-right">
                                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                                    {{ $order->currency }} {{ number_format($item->unit_price, 2) }} {{ __('each') }}
                                                </p>
                                                <p class="font-semibold text-gray-900 dark:text-white">
                                                    {{ $order->currency }} {{ number_format($item->unit_price * $item->quantity, 2) }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </section>

                    <!-- Shipping Information Section -->
                    <section class="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 border-b dark:border-gray-700 pb-3">
                            <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-indigo-500 text-white mr-3">2</span>
                            {{ __('Shipping & Billing Information') }}
                        </h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Shipping Address -->
                            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-5 border border-blue-200 dark:border-blue-800">
                                <div class="flex items-center mb-4">
                                    <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                        </svg>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('Shipping Address') }}</h4>
                                </div>
                                <address class="not-italic text-gray-700 dark:text-gray-300 leading-relaxed">
                                    <div class="font-medium text-gray-900 dark:text-white">
                                        {{ $order->shippingAddress->first_name }} {{ $order->shippingAddress->last_name }}
                                    </div>
                                    <div class="mt-1">
                                        {{ $order->shippingAddress->address_line1 }}
                                        @if($order->shippingAddress->address_line2)
                                            <br>{{ $order->shippingAddress->address_line2 }}
                                        @endif
                                    </div>
                                    <div class="mt-1">
                                        {{ $order->shippingAddress->city }}@if($order->shippingAddress->region), {{ $order->shippingAddress->region }}@endif {{ $order->shippingAddress->postal_code }}
                                    </div>
                                    <div class="mt-1 font-medium">
                                        {{ \Symfony\Component\Intl\Countries::getName($order->shippingAddress->country, app()->getLocale()) }}
                                    </div>
                                </address>
                            </div>

                            <!-- Billing Address -->
                            <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-5 border border-purple-200 dark:border-purple-800">
                                <div class="flex items-center mb-4">
                                    <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                                        <x-svg.credit-card-filled class="h-5 w-5 text-white" />
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('Billing Address') }}</h4>
                                </div>
                                <address class="not-italic text-gray-700 dark:text-gray-300 leading-relaxed">
                                    <div class="font-medium text-gray-900 dark:text-white">
                                        {{ $order->billingAddress->first_name }} {{ $order->billingAddress->last_name }}
                                    </div>
                                    <div class="mt-1">
                                        {{ $order->billingAddress->address_line1 }}
                                        @if($order->billingAddress->address_line2)
                                            <br>{{ $order->billingAddress->address_line2 }}
                                        @endif
                                    </div>
                                    <div class="mt-1">
                                        {{ $order->billingAddress->city }}@if($order->billingAddress->region), {{ $order->billingAddress->region }}@endif {{ $order->billingAddress->postal_code }}
                                    </div>
                                    <div class="mt-1 font-medium">
                                        {{ \Symfony\Component\Intl\Countries::getName($order->billingAddress->country, app()->getLocale()) }}
                                    </div>
                                </address>
                            </div>
                        </div>
                    </section>

                <!-- Right Column: Order Summary & Information -->
                <div class="lg:w-1/3">
                    <div class="sticky top-24 space-y-8">
                        <!-- Order Total Section -->
                        <section class="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6 mb-8">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 border-b dark:border-gray-700 pb-3">
                                {{ __('Order Total') }}
                            </h3>

                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600 dark:text-gray-400">{{ __('Subtotal') }}:</span>
                                    <span class="text-gray-900 dark:text-white font-medium">{{ $order->currency }} {{ number_format($order->subtotal, 2) }}</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600 dark:text-gray-400">{{ __('Shipping') }}:</span>
                                    <span class="text-gray-900 dark:text-white font-medium">
                                        @if($order->shipping_cost > 0)
                                            {{ $order->currency }} {{ number_format($order->shipping_cost, 2) }}
                                        @else
                                            {{ __('Free') }}
                                        @endif
                                    </span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600 dark:text-gray-400">{{ __('Tax') }}:</span>
                                    <span class="text-gray-900 dark:text-white font-medium">{{ $order->currency }} {{ number_format($order->tax_amount, 2) }}</span>
                                </div>
                                @if($order->discount_amount > 0)
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600 dark:text-gray-400">{{ __('Discount') }}:</span>
                                        <span class="text-green-600 dark:text-green-400 font-medium">-{{ $order->currency }} {{ number_format($order->discount_amount, 2) }}</span>
                                    </div>
                                @endif
                                <div class="border-t border-gray-200 dark:border-gray-700 pt-3 mt-3">
                                    <div class="flex justify-between">
                                        <span class="text-lg font-bold text-gray-900 dark:text-white">{{ __('Total') }}:</span>
                                        <span class="text-lg font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                                            {{ $order->currency }} {{ number_format($order->total, 2) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <!-- Order Information Section -->
                        <section class="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6 mb-8">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 border-b dark:border-gray-700 pb-3">
                                {{ __('Order Information') }}
                            </h3>

                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Date') }}</span>
                                    <span class="text-sm text-gray-900 dark:text-white">{{ $order->created_at->format('M d, Y') }}</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Time') }}</span>
                                    <span class="text-sm text-gray-900 dark:text-white">{{ $order->created_at->format('h:i A') }}</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Status') }}</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($order->status === 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400
                                        @elseif($order->status === 'processing') bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400
                                        @elseif($order->status === 'shipped') bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400
                                        @elseif($order->status === 'delivered') bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400
                                        @elseif($order->status === 'cancelled') bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400
                                        @elseif($order->status === 'refunded') bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400
                                        @endif">
                                        {{ ucfirst($order->status) }}
                                    </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Payment') }}</span>
                                    <span class="text-sm text-gray-900 dark:text-white">
                                        @if($order->payment_method === 'stripe')
                                            {{ __('Credit Card') }}
                                        @elseif($order->payment_method === 'paypal')
                                            {{ __('PayPal') }}
                                        @elseif($order->payment_method === 'bank_transfer')
                                            {{ __('Bank Transfer') }}
                                        @else
                                            {{ ucfirst($order->payment_method) }}
                                        @endif
                                    </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Shipping') }}</span>
                                    <span class="text-sm text-gray-900 dark:text-white">{{ $order->shipping_method }}</span>
                                </div>
                            </div>
                        </section>

                        <!-- Next Steps Section -->
                        <section class="bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-xl p-6 border border-indigo-200 dark:border-indigo-800 mb-8">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                {{ __('What\'s Next?') }}
                            </h3>

                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                        <x-svg.checkmark class="h-3 w-3 text-white" />
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">{{ __('Confirmation Email Sent') }}</p>
                                        <p class="text-xs text-gray-600 dark:text-gray-400">{{ __('Check your inbox for order details') }}</p>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                        <x-svg.clock class="h-3 w-3 text-white" />
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">{{ __('Processing Your Order') }}</p>
                                        <p class="text-xs text-gray-600 dark:text-gray-400">{{ __('We\'ll prepare your items for shipping') }}</p>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <div class="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">{{ __('Shipping Updates') }}</p>
                                        <p class="text-xs text-gray-600 dark:text-gray-400">{{ __('Track your package once it ships') }}</p>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="mt-12 text-center">
                <div class="flex flex-col sm:flex-row justify-center gap-4 max-w-md mx-auto">
                    <a href="{{ route('orders.show', $order) }}" class="flex-1 inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-semibold rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900">
                        <x-svg.document-text class="h-5 w-5 mr-2" />
                        {{ __('View Order Details') }}
                    </a>
                    <a href="{{ route('home') }}" class="flex-1 inline-flex items-center justify-center px-6 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg font-semibold text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 shadow-md hover:shadow-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                        </svg>
                        {{ __('Continue Shopping') }}
                    </a>
                </div>

                <!-- Additional Information -->
                <div class="mt-8 max-w-2xl mx-auto">
                    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                        <div class="flex items-center justify-center mb-4">
                            <x-svg.form-info class="h-6 w-6 mr-2" />
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white">{{ __('Need Help?') }}</h4>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400 text-center mb-4">
                            {{ __('If you have any questions about your order, please don\'t hesitate to contact our customer support team.') }}
                        </p>
                        <div class="flex flex-col sm:flex-row gap-3 justify-center">
                            <a href="mailto:{{ \App\Models\Setting::getValue('contact_email', '<EMAIL>') }}" class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300">
                                <x-svg.form-email-icon class="h-4 w-4 mr-2" />
                                {{ __('Email Support') }}
                            </a>
                            <span class="hidden sm:block text-gray-300 dark:text-gray-600">|</span>
                            <a href="tel:{{ \App\Models\Setting::getValue('contact_phone', '+1234567890') }}" class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300">
                                <x-svg.phone-support class="h-4 w-4 mr-2" />
                                {{ __('Call Support') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
