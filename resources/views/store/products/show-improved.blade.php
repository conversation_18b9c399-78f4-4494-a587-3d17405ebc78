<x-app-layout>
    <x-slot name="header">
        <!-- Enhanced CSS and JS for product page -->
        <link rel="stylesheet" href="{{ asset('css/product-page.css') }}">
        <script src="{{ asset('js/product-page.js') }}" defer></script>
        
        <!-- SEO Meta Tags -->
        <meta name="description" content="{{ $product->getTranslation('description', app()->getLocale()) }}">
        <meta property="og:title" content="{{ $product->getTranslation('name', app()->getLocale()) }}">
        <meta property="og:description" content="{{ $product->getTranslation('description', app()->getLocale()) }}">
        <meta property="og:type" content="product">
        <meta property="og:url" content="{{ url()->current() }}">
        
        @php
            $thumbnailUrl = '';
            try {
                $thumbnailUrl = $product->getFirstMediaUrl('thumbnail');
            } catch (\Exception $e) {
                // Silently handle the error
            }
        @endphp
        
        @if($thumbnailUrl)
            <meta property="og:image" content="{{ $thumbnailUrl }}">
        @endif

        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <h1 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ $product->getTranslation('name', app()->getLocale()) }}
            </h1>
            <div class="mt-2 sm:mt-0">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="{{ route('store.index') }}" 
                               class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-md px-2 py-1"
                               aria-label="Go to store homepage">
                                <svg class="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                                </svg>
                                Store
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                                </svg>
                                <a href="{{ route('categories.show', $product->category->getTranslation('slug', app()->getLocale())) }}" 
                                   class="ml-1 text-sm font-medium text-gray-700 hover:text-indigo-600 md:ml-2 dark:text-gray-400 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-md px-2 py-1">
                                    {{ $product->category->getTranslation('name', app()->getLocale()) }}
                                </a>
                            </div>
                        </li>
                        <li aria-current="page">
                            <div class="flex items-center">
                                <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                                </svg>
                                <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">
                                    {{ $product->getTranslation('name', app()->getLocale()) }}
                                </span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </x-slot>

    @section('content')
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Enhanced Product Images with Accessibility -->
                    <div class="product-gallery" role="region" aria-label="Product images">
                        @php
                            $images = [];
                            try {
                                $images = $product->getMedia('images');
                                if (!$thumbnailUrl && count($images) > 0) {
                                    $thumbnailUrl = $images->first()->getUrl();
                                }
                            } catch (\Exception $e) {
                                // Silently handle the error
                            }
                        @endphp

                        <div class="mb-4 relative overflow-hidden rounded-lg product-image-container">
                            @if(!empty($thumbnailUrl))
                                <img src="{{ $thumbnailUrl }}" 
                                     alt="{{ $product->getTranslation('name', app()->getLocale()) }}"
                                     class="w-full h-96 object-cover rounded-lg main-product-image transition-transform duration-300 cursor-pointer"
                                     data-zoom-enabled="true"
                                     tabindex="0"
                                     role="button"
                                     aria-label="Click to view fullscreen image"
                                     onkeydown="if(event.key === 'Enter' || event.key === ' ') { event.preventDefault(); this.click(); }">
                                <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-opacity duration-300 cursor-zoom-in zoom-overlay" aria-hidden="true"></div>
                            @else
                                <div class="w-full h-96 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-600 dark:to-gray-800 flex items-center justify-center rounded-lg"
                                     role="img" 
                                     aria-label="No product image available">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                            @endif
                        </div>

                        @if(count($images) > 0 || !empty($thumbnailUrl))
                            <div class="grid grid-cols-4 gap-2 product-thumbnails" role="tablist" aria-label="Product image thumbnails">
                                @if(!empty($thumbnailUrl))
                                    <button type="button"
                                            class="w-full h-24 object-cover rounded-lg cursor-pointer hover:opacity-75 hover:shadow-lg transition-all duration-200 product-thumbnail ring-2 ring-indigo-500 focus:outline-none focus:ring-4 focus:ring-indigo-300"
                                            data-full-image="{{ $thumbnailUrl }}"
                                            role="tab"
                                            aria-selected="true"
                                            aria-label="View main product image"
                                            tabindex="0">
                                        <img src="{{ $thumbnailUrl }}" 
                                             alt="{{ $product->getTranslation('name', app()->getLocale()) }}"
                                             class="w-full h-full object-cover rounded-lg">
                                    </button>
                                @endif

                                @foreach($images as $index => $image)
                                    <button type="button"
                                            class="w-full h-24 object-cover rounded-lg cursor-pointer hover:opacity-75 hover:shadow-lg transition-all duration-200 product-thumbnail focus:outline-none focus:ring-4 focus:ring-indigo-300"
                                            data-full-image="{{ $image->getUrl() }}"
                                            role="tab"
                                            aria-selected="false"
                                            aria-label="View product image {{ $index + 2 }}"
                                            tabindex="-1">
                                        <img src="{{ $image->getUrl('thumb') }}" 
                                             alt="{{ $product->getTranslation('name', app()->getLocale()) }} - Image {{ $index + 2 }}"
                                             class="w-full h-full object-cover rounded-lg">
                                    </button>
                                @endforeach
                            </div>
                        @endif
                    </div>

                    <!-- Enhanced Product Details -->
                    <div class="product-details">
                        <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                            {{ $product->getTranslation('name', app()->getLocale()) }}
                        </h1>

                        <div class="mb-4 flex items-center justify-between">
                            <a href="{{ route('categories.show', $product->category->getTranslation('slug', app()->getLocale())) }}" 
                               class="text-sm text-indigo-600 dark:text-indigo-400 hover:underline focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-md px-2 py-1">
                                {{ $product->category->getTranslation('name', app()->getLocale()) }}
                            </a>

                            <!-- Enhanced Social Sharing with Accessibility -->
                            <div class="flex space-x-2" role="group" aria-label="Share this product">
                                <button type="button" 
                                        class="social-share-btn p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200" 
                                        data-platform="facebook" 
                                        aria-label="Share on Facebook">
                                    <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                                <button type="button" 
                                        class="social-share-btn p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200" 
                                        data-platform="twitter" 
                                        aria-label="Share on Twitter">
                                    <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                                    </svg>
                                </button>
                                <button type="button" 
                                        class="social-share-btn p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors duration-200" 
                                        data-platform="pinterest" 
                                        aria-label="Share on Pinterest">
                                    <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path d="M12 0a12 12 0 00-4.373 23.182c-.067-.593-.132-1.505.028-2.153.145-.587.933-3.739.933-3.739s-.238-.477-.238-1.182c0-1.107.642-1.933 1.438-1.933.678 0 1.006.509 1.006 1.119 0 .681-.434 1.7-.659 2.644-.188.79.398 1.435 1.18 1.435 1.416 0 2.502-1.493 2.502-3.646 0-1.907-1.37-3.24-3.329-3.24-2.266 0-3.591 1.693-3.591 3.44 0 .68.26 1.409.587 1.805.065.08.075.149.055.228-.06.252-.196.796-.222.907-.035.146-.116.177-.268.107-1-.465-1.624-1.926-1.624-3.1 0-2.523 1.834-4.84 5.287-4.84 2.775 0 4.932 1.977 4.932 4.62 0 2.757-1.739 4.976-4.151 4.976-.811 0-1.573-.421-1.834-.919l-.498 1.902c-.181.695-.669 1.566-.997 2.097A12 12 0 1012 0z"></path>
                                    </svg>
                                </button>
                                <button type="button" 
                                        class="social-share-btn p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200" 
                                        data-platform="email" 
                                        aria-label="Share via Email">
                                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <div class="mb-6">
                            <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
                                {{ $product->getTranslation('description', app()->getLocale()) }}
                            </p>
                        </div>

                        @if($product->variants->count() > 0)
                            @php
                                $firstVariant = $product->variants->first();
                                $minPrice = $product->variants->min('price');
                                $maxPrice = $product->variants->max('price');
                            @endphp

                            <div class="mb-6">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-2xl font-bold text-gray-900 dark:text-white product-price" aria-label="Product price">
                                            @if($minPrice === $maxPrice)
                                                ${{ number_format($minPrice, 2) }}
                                            @else
                                                ${{ number_format($minPrice, 2) }} - ${{ number_format($maxPrice, 2) }}
                                            @endif
                                        </span>

                                        @if($firstVariant->compare_at_price && $firstVariant->compare_at_price > $firstVariant->price)
                                            <span class="ml-2 text-lg text-gray-500 line-through product-compare-price" aria-label="Original price">
                                                ${{ number_format($firstVariant->compare_at_price, 2) }}
                                            </span>
                                            <span class="ml-2 text-sm font-medium text-green-600 product-discount" aria-label="Discount percentage">
                                                {{ round((1 - $firstVariant->price / $firstVariant->compare_at_price) * 100) }}% off
                                            </span>
                                        @endif
                                    </div>

                                    <button type="button" 
                                            class="wishlist-button p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500" 
                                            aria-label="Add to wishlist">
                                        <svg class="w-6 h-6 text-gray-400 hover:text-red-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                        </svg>
                                    </button>
                                </div>

                                <div class="mt-2">
                                    <span class="stock-status text-green-600" aria-live="polite" aria-atomic="true">In stock</span>
                                </div>
                            </div>
                        @endif
                        
                        <!-- Continue with the rest of the form and content... -->
                        <!-- This is where the variant selection form would continue -->
                        <!-- Due to length constraints, the rest would be in the next section -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endsection
</x-app-layout>
