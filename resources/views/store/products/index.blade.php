<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ __('Products') }}
                </h2>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    {{ $totalProducts }} {{ Str::plural('product', $totalProducts) }} found
                </p>
            </div>

            <!-- Breadcrumb -->
            <nav class="flex mt-4 sm:mt-0" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{{ route('home') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                            <svg class="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                            </svg>
                            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">Products</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </x-slot>

    @section('content')
    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col lg:flex-row gap-8">
                <!-- Sidebar Filters -->
                <div class="lg:w-1/4">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 sticky top-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Filters</h3>

                        <form action="{{ route('products.index') }}" method="GET" id="filter-form" class="space-y-6">
                            <!-- Search -->
                            <div>
                                <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                                <div class="relative">
                                    <input type="text" name="search" id="search" value="{{ $search }}"
                                           placeholder="Search products..."
                                           class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <!-- Categories -->
                            <div>
                                <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category</label>
                                <select name="category" id="category"
                                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                    <option value="">All Categories</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ $currentCategory == $category->id ? 'selected' : '' }}>
                                            {{ $category->getTranslation('name', app()->getLocale()) }}
                                        </option>
                                        @foreach($category->children as $child)
                                            <option value="{{ $child->id }}" {{ $currentCategory == $child->id ? 'selected' : '' }}>
                                                &nbsp;&nbsp;{{ $child->getTranslation('name', app()->getLocale()) }}
                                            </option>
                                        @endforeach
                                    @endforeach
                                </select>
                            </div>

                            <!-- Price Range -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Price Range</label>
                                <div class="grid grid-cols-2 gap-3">
                                    <div>
                                        <input type="number" name="min_price" id="min_price" value="{{ $minPrice }}"
                                               placeholder="Min" min="0" step="0.01"
                                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                    </div>
                                    <div>
                                        <input type="number" name="max_price" id="max_price" value="{{ $maxPrice }}"
                                               placeholder="Max" min="0" step="0.01"
                                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                    </div>
                                </div>
                                <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                                    Range: ${{ number_format($priceRange['min'], 2) }} - ${{ number_format($priceRange['max'], 2) }}
                                </div>
                            </div>

                            <!-- Availability -->
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" name="in_stock" value="1" {{ $inStock ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700">
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">In Stock Only</span>
                                </label>
                            </div>

                            <!-- Filter Actions -->
                            <div class="flex space-x-3">
                                <button type="submit"
                                        class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                                    Apply
                                </button>
                                <a href="{{ route('products.index') }}"
                                   class="flex-1 bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-800 dark:text-white font-medium py-2 px-4 rounded-lg transition duration-200 text-center">
                                    Clear
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="lg:w-3/4">
                    <!-- Toolbar -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                            <!-- Results Info -->
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                Showing {{ $products->firstItem() ?? 0 }}-{{ $products->lastItem() ?? 0 }} of {{ $products->total() }} products
                            </div>

                            <!-- Sort and View Options -->
                            <div class="flex items-center space-x-4">
                                <!-- Sort -->
                                <div class="flex items-center space-x-2">
                                    <label for="sort-select" class="text-sm font-medium text-gray-700 dark:text-gray-300">Sort:</label>
                                    <select id="sort-select" onchange="updateSort(this.value)"
                                            class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                        <option value="newest" {{ $sortBy == 'newest' ? 'selected' : '' }}>Newest</option>
                                        <option value="name" {{ $sortBy == 'name' ? 'selected' : '' }}>Name A-Z</option>
                                        <option value="price_low" {{ $sortBy == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                                        <option value="price_high" {{ $sortBy == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                                    </select>
                                </div>

                                <!-- Per Page -->
                                <div class="flex items-center space-x-2">
                                    <label for="per-page-select" class="text-sm font-medium text-gray-700 dark:text-gray-300">Show:</label>
                                    <select id="per-page-select" onchange="updatePerPage(this.value)"
                                            class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                        <option value="12" {{ $perPage == 12 ? 'selected' : '' }}>12</option>
                                        <option value="24" {{ $perPage == 24 ? 'selected' : '' }}>24</option>
                                        <option value="48" {{ $perPage == 48 ? 'selected' : '' }}>48</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products Grid -->
                    @if($products->count() > 0)
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($products as $product)
                                <div class="group bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                                    <!-- Product Image -->
                                    <div class="relative aspect-square overflow-hidden">
                                        <a href="{{ route('products.show', $product->getTranslation('slug', app()->getLocale())) }}">
                                            @php
                                                $thumbnailUrl = '';
                                                try {
                                                    $thumbnailUrl = $product->getFirstMediaUrl('thumbnail');
                                                } catch (\Exception $e) {
                                                    // Silently handle the error
                                                }
                                            @endphp

                                            @if(!empty($thumbnailUrl))
                                                <img src="{{ $thumbnailUrl }}"
                                                     alt="{{ $product->getTranslation('name', app()->getLocale()) }}"
                                                     class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                                            @else
                                                <div class="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-600 dark:to-gray-800 flex items-center justify-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                    </svg>
                                                </div>
                                            @endif
                                        </a>

                                        <!-- Wishlist Button -->
                                        @auth
                                            <button type="button"
                                                    class="wishlist-button absolute top-3 right-3 p-2 bg-white dark:bg-gray-800 rounded-full shadow-md hover:shadow-lg transition-all duration-200 opacity-0 group-hover:opacity-100"
                                                    data-product-id="{{ $product->id }}"
                                                    onclick="toggleWishlist('{{ $product->id }}')"
                                                    title="{{ in_array($product->id, $wishlistItems) ? 'Remove from wishlist' : 'Add to wishlist' }}">
                                                <svg class="w-5 h-5 {{ in_array($product->id, $wishlistItems) ? 'text-red-500' : 'text-gray-400' }}"
                                                     fill="{{ in_array($product->id, $wishlistItems) ? 'currentColor' : 'none' }}"
                                                     stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                                </svg>
                                            </button>
                                        @endauth

                                        <!-- Sale Badge -->
                                        @if($product->variants->count() > 0)
                                            @php
                                                $firstVariant = $product->variants->first();
                                                $hasDiscount = $firstVariant->compare_at_price && $firstVariant->compare_at_price > $firstVariant->price;
                                            @endphp
                                            @if($hasDiscount)
                                                @php
                                                    $discountPercent = round((($firstVariant->compare_at_price - $firstVariant->price) / $firstVariant->compare_at_price) * 100);
                                                @endphp
                                                <div class="absolute top-3 left-3 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                                                    -{{ $discountPercent }}%
                                                </div>
                                            @endif
                                        @endif

                                        <!-- Stock Status -->
                                        @php
                                            $inStock = false;
                                            foreach($product->variants as $variant) {
                                                if($variant->inventoryItem && ($variant->inventoryItem->quantity_on_hand > 0 || $variant->inventoryItem->allow_backorder)) {
                                                    $inStock = true;
                                                    break;
                                                }
                                            }
                                        @endphp
                                        @if(!$inStock)
                                            <div class="absolute bottom-3 left-3 bg-gray-800 bg-opacity-75 text-white text-xs font-medium px-2 py-1 rounded">
                                                Out of Stock
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Product Info -->
                                    <div class="p-4">
                                        <!-- Category -->
                                        @if($product->category)
                                            <div class="text-xs text-blue-600 dark:text-blue-400 font-medium mb-1">
                                                {{ $product->category->getTranslation('name', app()->getLocale()) }}
                                            </div>
                                        @endif

                                        <!-- Product Name -->
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                                            <a href="{{ route('products.show', $product->getTranslation('slug', app()->getLocale())) }}"
                                               class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                                {{ $product->getTranslation('name', app()->getLocale()) }}
                                            </a>
                                        </h3>

                                        <!-- Product Description -->
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                                            {{ Str::limit($product->getTranslation('description', app()->getLocale()), 80) }}
                                        </p>

                                        <!-- Price and Actions -->
                                        @if($product->variants->count() > 0)
                                            @php
                                                $minPrice = $product->variants->min('price');
                                                $maxPrice = $product->variants->max('price');
                                                $firstVariant = $product->variants->first();
                                            @endphp

                                            <div class="flex items-center justify-between">
                                                <div class="flex flex-col">
                                                    @if($minPrice === $maxPrice)
                                                        <div class="flex items-center space-x-2">
                                                            <span class="text-lg font-bold text-gray-900 dark:text-white">
                                                                ${{ number_format($minPrice, 2) }}
                                                            </span>
                                                            @if($firstVariant->compare_at_price && $firstVariant->compare_at_price > $firstVariant->price)
                                                                <span class="text-sm text-gray-500 line-through">
                                                                    ${{ number_format($firstVariant->compare_at_price, 2) }}
                                                                </span>
                                                            @endif
                                                        </div>
                                                    @else
                                                        <span class="text-lg font-bold text-gray-900 dark:text-white">
                                                            ${{ number_format($minPrice, 2) }} - ${{ number_format($maxPrice, 2) }}
                                                        </span>
                                                    @endif
                                                </div>

                                                <!-- Add to Cart Button -->
                                                <a href="{{ route('products.show', $product->getTranslation('slug', app()->getLocale())) }}"
                                                   class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 text-sm">
                                                    {{ $inStock ? 'View Details' : 'View Product' }}
                                                </a>
                                            </div>
                                        @else
                                            <div class="flex items-center justify-between">
                                                <span class="text-sm text-gray-500 dark:text-gray-400 italic">
                                                    Currently unavailable
                                                </span>
                                                <a href="{{ route('products.show', $product->getTranslation('slug', app()->getLocale())) }}"
                                                   class="bg-gray-400 text-white font-medium py-2 px-4 rounded-lg text-sm cursor-not-allowed">
                                                    View Details
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        @if($products->hasPages())
                            <div class="mt-8">
                                {{ $products->appends(request()->query())->links() }}
                            </div>
                        @endif
                    @else
                        <!-- Empty State -->
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12 text-center">
                            <svg class="mx-auto h-16 w-16 text-gray-400 dark:text-gray-500 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No products found</h3>
                            <p class="text-gray-600 dark:text-gray-400 mb-6">
                                We couldn't find any products matching your criteria. Try adjusting your filters or search terms.
                            </p>
                            <div class="flex flex-col sm:flex-row gap-3 justify-center">
                                <a href="{{ route('products.index') }}"
                                   class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition duration-200">
                                    View All Products
                                </a>
                                <button onclick="clearFilters()"
                                        class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-800 dark:text-white font-medium py-2 px-6 rounded-lg transition duration-200">
                                    Clear Filters
                                </button>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Enhanced Functionality -->
    <script>
        // Update sort parameter
        function updateSort(sortValue) {
            const url = new URL(window.location);
            url.searchParams.set('sort', sortValue);
            window.location.href = url.toString();
        }

        // Update per page parameter
        function updatePerPage(perPageValue) {
            const url = new URL(window.location);
            url.searchParams.set('per_page', perPageValue);
            url.searchParams.delete('page'); // Reset to first page
            window.location.href = url.toString();
        }

        // Clear all filters
        function clearFilters() {
            window.location.href = '{{ route("products.index") }}';
        }

        // Auto-submit form on filter change
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('filter-form');
            const inputs = form.querySelectorAll('input, select');

            inputs.forEach(input => {
                if (input.type === 'checkbox') {
                    input.addEventListener('change', function() {
                        setTimeout(() => form.submit(), 100);
                    });
                } else {
                    let timeout;
                    input.addEventListener('input', function() {
                        clearTimeout(timeout);
                        timeout = setTimeout(() => form.submit(), 500);
                    });
                }
            });
        });
    </script>

    <!-- Styles -->
    <style>
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .aspect-square {
            aspect-ratio: 1 / 1;
        }
    </style>

    @endsection

    @push('scripts')
    <script src="{{ asset('js/wishlist.js') }}"></script>
    @endpush
</x-app-layout>