<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unsubscribe - {{ config('app.name') }}</title>
    <style>
        body {
            font-family: 'Figtree', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f7fafc;
            color: #2d3748;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
            text-align: center;
            padding: 30px 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .content {
            padding: 30px;
            text-align: center;
        }
        .button {
            display: inline-block;
            padding: 14px 28px;
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: #ffffff;
            text-decoration: none;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        .button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(245, 101, 101, 0.4);
        }
        .button-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }
        .button-secondary:hover {
            background: #cbd5e0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .footer {
            background-color: #2d3748;
            color: #a0aec0;
            text-align: center;
            padding: 20px;
            font-size: 14px;
        }
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
        .email-info {
            background-color: #f7fafc;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Unsubscribe</h1>
            <p>We're sorry to see you go</p>
        </div>
        
        <div class="content">
            <div class="email-info">
                <p><strong>Email:</strong> {{ $preference->email }}</p>
            </div>

            <h2>Are you sure you want to unsubscribe?</h2>
            <p>If you unsubscribe, you will no longer receive any email notifications from {{ config('app.name') }}, including important order updates.</p>
            
            <p>Instead of unsubscribing completely, you might want to:</p>
            <ul style="text-align: left; display: inline-block;">
                <li>Customize which emails you receive</li>
                <li>Keep order notifications but disable marketing emails</li>
                <li>Temporarily pause emails and resubscribe later</li>
            </ul>

            <div style="margin-top: 30px;">
                <form method="POST" action="{{ route('email.unsubscribe') }}" style="display: inline;">
                    @csrf
                    <input type="hidden" name="token" value="{{ $preference->unsubscribe_token }}">
                    <button type="submit" class="button">Yes, Unsubscribe Me</button>
                </form>
                
                <a href="{{ route('email.preferences', $preference->unsubscribe_token) }}" class="button button-secondary">Customize Email Preferences</a>
            </div>

            <p style="margin-top: 30px; font-size: 14px; color: #718096;">
                You can always resubscribe later by visiting your email preferences.
            </p>
        </div>
        
        <div class="footer">
            <p>&copy; {{ date('Y') }} {{ config('app.name') }}. All rights reserved.</p>
            <p><a href="{{ url('/') }}">Visit Our Store</a></p>
        </div>
    </div>
</body>
</html>
