<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Preferences - {{ config('app.name') }}</title>
    <style>
        body {
            font-family: 'Figtree', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f7fafc;
            color: #2d3748;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .content {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: flex;
            align-items: center;
            font-weight: 500;
            cursor: pointer;
            padding: 12px;
            border-radius: 6px;
            transition: background-color 0.2s;
        }
        .form-group label:hover {
            background-color: #f7fafc;
        }
        .form-group input[type="checkbox"] {
            margin-right: 12px;
            width: 18px;
            height: 18px;
        }
        .description {
            font-size: 14px;
            color: #718096;
            margin-left: 30px;
            margin-top: 4px;
        }
        .button {
            display: inline-block;
            padding: 14px 28px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            text-decoration: none;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .button-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }
        .button-secondary:hover {
            background: #cbd5e0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .alert {
            padding: 16px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .alert-success {
            background-color: #f0fff4;
            border-left: 4px solid #48bb78;
            color: #2f855a;
        }
        .footer {
            background-color: #2d3748;
            color: #a0aec0;
            text-align: center;
            padding: 20px;
            font-size: 14px;
        }
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h3 {
            color: #2c5282;
            font-size: 18px;
            margin-bottom: 15px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Email Preferences</h1>
            <p>Manage your email notification settings</p>
        </div>
        
        <div class="content">
            @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif

            <p>Hello! You can customize which email notifications you'd like to receive from <strong>{{ config('app.name') }}</strong>.</p>
            <p><strong>Email:</strong> {{ $preference->email }}</p>

            <form method="POST" action="{{ route('email.preferences.update', $preference->unsubscribe_token) }}">
                @csrf
                @method('PUT')

                <div class="section">
                    <h3>Order Notifications</h3>
                    
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="order_confirmation" value="1" {{ $preference->order_confirmation ? 'checked' : '' }}>
                            Order Confirmation
                        </label>
                        <div class="description">Receive confirmation when your order is placed</div>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="payment_confirmed" value="1" {{ $preference->payment_confirmed ? 'checked' : '' }}>
                            Payment Confirmed
                        </label>
                        <div class="description">Get notified when your payment is successfully processed</div>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="payment_failed" value="1" {{ $preference->payment_failed ? 'checked' : '' }}>
                            Payment Failed
                        </label>
                        <div class="description">Important alerts if payment processing fails</div>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="order_processing" value="1" {{ $preference->order_processing ? 'checked' : '' }}>
                            Order Processing
                        </label>
                        <div class="description">Updates when your order is being prepared</div>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="order_shipped" value="1" {{ $preference->order_shipped ? 'checked' : '' }}>
                            Order Shipped
                        </label>
                        <div class="description">Shipping notifications with tracking information</div>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="order_delivered" value="1" {{ $preference->order_delivered ? 'checked' : '' }}>
                            Order Delivered
                        </label>
                        <div class="description">Confirmation when your order is delivered</div>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="order_cancelled" value="1" {{ $preference->order_cancelled ? 'checked' : '' }}>
                            Order Cancelled
                        </label>
                        <div class="description">Notifications if your order is cancelled</div>
                    </div>
                </div>

                <div class="section">
                    <h3>Marketing Communications</h3>
                    
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="marketing_emails" value="1" {{ $preference->marketing_emails ? 'checked' : '' }}>
                            Marketing Emails
                        </label>
                        <div class="description">Promotions, new products, and special offers</div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="button">Save Preferences</button>
                    <a href="{{ route('email.unsubscribe', ['token' => $preference->unsubscribe_token]) }}" class="button button-secondary" style="margin-left: 10px;">Unsubscribe from All</a>
                </div>
            </form>
        </div>
        
        <div class="footer">
            <p>&copy; {{ date('Y') }} {{ config('app.name') }}. All rights reserved.</p>
            <p><a href="{{ url('/') }}">Visit Our Store</a></p>
        </div>
    </div>
</body>
</html>
