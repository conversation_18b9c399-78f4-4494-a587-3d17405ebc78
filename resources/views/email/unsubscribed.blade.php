<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unsubscribed - {{ config('app.name') }}</title>
    <style>
        body {
            font-family: 'Figtree', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f7fafc;
            color: #2d3748;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            text-align: center;
            padding: 30px 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .content {
            padding: 30px;
            text-align: center;
        }
        .button {
            display: inline-block;
            padding: 14px 28px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            text-decoration: none;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        .button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .footer {
            background-color: #2d3748;
            color: #a0aec0;
            text-align: center;
            padding: 20px;
            font-size: 14px;
        }
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
        .email-info {
            background-color: #f7fafc;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .success-icon {
            font-size: 48px;
            color: #48bb78;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Successfully Unsubscribed</h1>
            <p>You have been removed from our mailing list</p>
        </div>
        
        <div class="content">
            <div class="success-icon">✓</div>
            
            <div class="email-info">
                <p><strong>Email:</strong> {{ $preference->email }}</p>
            </div>

            <h2>You're all set!</h2>
            <p>You have been successfully unsubscribed from all email notifications from {{ config('app.name') }}.</p>
            
            <p>We're sorry to see you go, but we understand that email preferences can change.</p>

            <h3>What this means:</h3>
            <ul style="text-align: left; display: inline-block;">
                <li>You will no longer receive order notification emails</li>
                <li>You will not receive marketing or promotional emails</li>
                <li>You can still place orders and use our website normally</li>
                <li>You can resubscribe at any time using the link below</li>
            </ul>

            <div style="margin-top: 30px;">
                <a href="{{ route('email.preferences.resubscribe', $preference->unsubscribe_token) }}" class="button">Resubscribe to Order Notifications</a>
            </div>

            <p style="margin-top: 30px; font-size: 14px; color: #718096;">
                Changed your mind? You can resubscribe or customize your email preferences at any time.
            </p>
        </div>
        
        <div class="footer">
            <p>&copy; {{ date('Y') }} {{ config('app.name') }}. All rights reserved.</p>
            <p><a href="{{ url('/') }}">Visit Our Store</a> | <a href="{{ url('/contact') }}">Contact Support</a></p>
        </div>
    </div>
</body>
</html>
