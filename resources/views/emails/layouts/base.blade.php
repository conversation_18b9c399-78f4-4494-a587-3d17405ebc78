<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Order Notification')</title>
    <style>
        body {
            font-family: 'Figtree', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f7fafc;
            color: #2d3748;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .email-wrapper {
            background-color: #f7fafc;
            padding: 20px 0;
            min-height: 100vh;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header .subtitle {
            margin: 8px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .content h2 {
            color: #2c5282;
            font-size: 24px;
            margin: 0 0 20px 0;
            font-weight: 600;
        }
        .content h3 {
            color: #4a5568;
            font-size: 18px;
            margin: 25px 0 15px 0;
            font-weight: 600;
        }
        .content p {
            margin: 0 0 16px 0;
            color: #4a5568;
        }
        .order-summary {
            background-color: #f7fafc;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        .order-details {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .order-details th,
        .order-details td {
            padding: 12px;
            border-bottom: 1px solid #e2e8f0;
            text-align: left;
        }
        .order-details th {
            background-color: #edf2f7;
            color: #4a5568;
            font-weight: 600;
        }
        .totals {
            width: 100%;
            margin-top: 20px;
        }
        .totals td {
            padding: 8px 0;
            border: none;
        }
        .totals .label {
            text-align: right;
            padding-right: 20px;
            font-weight: 600;
            color: #4a5568;
        }
        .totals .total-row {
            border-top: 2px solid #e2e8f0;
            font-weight: 700;
            font-size: 18px;
            color: #2d3748;
        }
        .button {
            display: inline-block;
            padding: 14px 28px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .button-center {
            text-align: center;
            margin: 30px 0;
        }
        .alert {
            padding: 16px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .alert-success {
            background-color: #f0fff4;
            border-left: 4px solid #48bb78;
            color: #2f855a;
        }
        .alert-warning {
            background-color: #fffbf0;
            border-left: 4px solid #ed8936;
            color: #c05621;
        }
        .alert-error {
            background-color: #fed7d7;
            border-left: 4px solid #f56565;
            color: #c53030;
        }
        .alert-info {
            background-color: #ebf8ff;
            border-left: 4px solid #4299e1;
            color: #2b6cb0;
        }
        .tracking-info {
            background-color: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 6px;
            padding: 16px;
            margin: 20px 0;
        }
        .tracking-number {
            font-family: 'Courier New', monospace;
            font-size: 18px;
            font-weight: bold;
            color: #2c7a7b;
            background-color: #ffffff;
            padding: 8px 12px;
            border-radius: 4px;
            display: inline-block;
            margin: 8px 0;
        }
        .footer {
            background-color: #2d3748;
            color: #a0aec0;
            text-align: center;
            padding: 30px 20px;
            font-size: 14px;
        }
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        .unsubscribe {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #4a5568;
        }
        
        /* Responsive styles */
        @media only screen and (max-width: 600px) {
            .container {
                margin: 0 10px;
                border-radius: 0;
            }
            .content {
                padding: 20px;
            }
            .header {
                padding: 20px;
            }
            .header h1 {
                font-size: 24px;
            }
            .order-details th,
            .order-details td {
                padding: 8px 4px;
                font-size: 14px;
            }
            .button {
                display: block;
                width: 100%;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="container">
            <div class="header">
                <h1>@yield('header-title', config('app.name'))</h1>
                <p class="subtitle">@yield('header-subtitle')</p>
            </div>
            
            <div class="content">
                @yield('content')
            </div>
            
            <div class="footer">
                <p>&copy; {{ date('Y') }} {{ config('app.name') }}. All rights reserved.</p>
                <p>
                    <a href="{{ url('/') }}">Visit Our Store</a> | 
                    <a href="{{ url('/contact') }}">Contact Support</a>
                </p>
                
                <div class="unsubscribe">
                    <p>
                        Don't want to receive these emails?
                        <a href="{{ route('email.unsubscribe', ['email' => urlencode($order->customer_email ?? '')]) }}">Unsubscribe</a> |
                        <a href="{{ \App\Models\EmailPreference::getOrCreateForEmail($order->customer_email ?? '')->getPreferencesUrl() }}">Email Preferences</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
