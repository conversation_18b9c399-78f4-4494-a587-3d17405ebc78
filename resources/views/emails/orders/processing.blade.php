@extends('emails.layouts.base')

@section('title', 'Order Processing')

@section('header-title', 'Order Processing')
@section('header-subtitle', 'Your order is being prepared')

@section('content')
    <div class="alert alert-info">
        <strong>Order Update:</strong> Your order #{{ $order->order_number }} is now being processed.
    </div>

    <h2>Hi {{ $order->customer_name }},</h2>
    
    <p>Great news! Your order has been confirmed and is now being processed by our fulfillment team. We're carefully preparing your items for shipment.</p>

    <div class="order-summary">
        <h3>Order Status</h3>
        <p><strong>Current Status:</strong> Processing</p>
        <p><strong>Estimated Processing Time:</strong> 1-2 business days</p>
        <p><strong>Expected Ship Date:</strong> {{ now()->addBusinessDays(2)->format('F d, Y') }}</p>
    </div>

    <h3>Order Details</h3>
    <p>
        <strong>Order Number:</strong> {{ $order->order_number }}<br>
        <strong>Order Date:</strong> {{ $order->created_at->format('F d, Y') }}<br>
        <strong>Payment Status:</strong> {{ $order->isPaid() ? 'Paid' : 'Pending' }}
    </p>

    <table class="order-details">
        <thead>
            <tr>
                <th>Product</th>
                <th>Quantity</th>
                <th>Price</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($order->items as $item)
                <tr>
                    <td>{{ $item->product_name }} @if($item->variant_name)({{ $item->variant_name }})@endif</td>
                    <td>{{ $item->quantity }}</td>
                    <td>{{ number_format($item->unit_price, 2) }} {{ $order->currency }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <table class="totals">
        <tr>
            <td class="label">Subtotal:</td>
            <td>{{ number_format($order->subtotal, 2) }} {{ $order->currency }}</td>
        </tr>
        @if($order->shipping_cost > 0)
        <tr>
            <td class="label">Shipping:</td>
            <td>{{ number_format($order->shipping_cost, 2) }} {{ $order->currency }}</td>
        </tr>
        @endif
        @if($order->tax_amount > 0)
        <tr>
            <td class="label">Tax:</td>
            <td>{{ number_format($order->tax_amount, 2) }} {{ $order->currency }}</td>
        </tr>
        @endif
        @if($order->discount_amount > 0)
        <tr>
            <td class="label">Discount:</td>
            <td>-{{ number_format($order->discount_amount, 2) }} {{ $order->currency }}</td>
        </tr>
        @endif
        <tr class="total-row">
            <td class="label">Total:</td>
            <td>{{ number_format($order->total, 2) }} {{ $order->currency }}</td>
        </tr>
    </table>

    @if ($order->shippingAddress)
        <h3>Shipping Address</h3>
        <p>
            {{ $order->shippingAddress->street }}<br>
            {{ $order->shippingAddress->city }}, {{ $order->shippingAddress->state }} {{ $order->shippingAddress->zip_code }}<br>
            {{ $order->shippingAddress->country }}
        </p>
    @endif

    <h3>What Happens Next?</h3>
    <p>Our team is now:</p>
    <ul>
        <li>Picking your items from our warehouse</li>
        <li>Quality checking each product</li>
        <li>Carefully packaging your order</li>
        <li>Preparing shipping labels</li>
    </ul>

    <p>You'll receive another email with tracking information once your order has been shipped.</p>

    <div class="button-center">
        <a href="{{ route('orders.show', $order->order_number) }}" class="button">Track Your Order</a>
    </div>

    <p>Thank you for your patience and for choosing {{ config('app.name') }}!</p>
@endsection
