<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation</title>
    <style>
        body {
            font-family: 'Figtree', sans-serif;
            background-color: #f7fafc;
            color: #2d3748;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            padding-bottom: 20px;
            border-bottom: 1px solid #e2e8f0;
        }
        .header h1 {
            color: #2c5282;
            font-size: 24px;
            margin: 0;
        }
        .content {
            padding: 20px 0;
        }
        .content h2 {
            color: #2c5282;
            font-size: 20px;
            margin-top: 0;
        }
        .order-details {
            width: 100%;
            border-collapse: collapse;
        }
        .order-details th, .order-details td {
            padding: 12px;
            border-bottom: 1px solid #e2e8f0;
            text-align: left;
        }
        .order-details th {
            background-color: #edf2f7;
            color: #4a5568;
        }
        .totals {
            width: 100%;
            margin-top: 20px;
        }
        .totals td {
            padding: 8px 0;
        }
        .totals .label {
            text-align: right;
            padding-right: 20px;
            font-weight: bold;
        }
        .footer {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            color: #718096;
            font-size: 14px;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #3182ce;
            color: #ffffff;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
        }
        .button:hover {
            background-color: #2c5282;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Order Confirmation</h1>
        </div>
        <div class="content">
            <h2>Hi {{ $order->customer_name }},</h2>
            <p>Thanks for your order! We've received it and will start processing it shortly.</p>

            <h3>Order Details</h3>
            <p>
                <strong>Order Number:</strong> {{ $order->order_number }}<br>
                <strong>Order Date:</strong> {{ $order->created_at->format('F d, Y') }}
            </p>

            <table class="order-details">
                <thead>
                    <tr>
                        <th>Product</th>
                        <th>Quantity</th>
                        <th>Price</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($order->items as $item)
                        <tr>
                            <td>{{ $item->product_name }} ({{ $item->variant_name }})</td>
                            <td>{{ $item->quantity }}</td>
                            <td>{{ number_format($item->unit_price, 2) }} {{ $order->currency }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>

            <table class="totals">
                <tr>
                    <td class="label">Subtotal:</td>
                    <td>{{ number_format($order->subtotal, 2) }} {{ $order->currency }}</td>
                </tr>
                <tr>
                    <td class="label">Shipping:</td>
                    <td>{{ number_format($order->shipping_cost, 2) }} {{ $order->currency }}</td>
                </tr>
                <tr>
                    <td class="label">Tax:</td>
                    <td>{{ number_format($order->tax_amount, 2) }} {{ $order->currency }}</td>
                </tr>
                <tr>
                    <td class="label">Total:</td>
                    <td>{{ number_format($order->total, 2) }} {{ $order->currency }}</td>
                </tr>
            </table>

            @if ($order->shippingAddress)
                <h3>Shipping Address</h3>
                <p>
                    {{ $order->shippingAddress->street }}<br>
                    {{ $order->shippingAddress->city }}, {{ $order->shippingAddress->state }} {{ $order->shippingAddress->zip_code }}<br>
                    {{ $order->shippingAddress->country }}
                </p>
            @endif

            <p style="text-align: center; margin-top: 30px;">
                <a href="{{ url('/') }}" class="button">Visit Our Store</a>
            </p>
        </div>
        <div class="footer">
            <p>&copy; {{ date('Y') }} {{ config('app.name') }}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
