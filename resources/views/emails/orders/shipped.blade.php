@extends('emails.layouts.base')

@section('title', 'Order Shipped')

@section('header-title', 'Order Shipped')
@section('header-subtitle', 'Your order is on its way!')

@section('content')
    <div class="alert alert-success">
        <strong>Good news!</strong> Your order #{{ $order->order_number }} has been shipped and is on its way to you.
    </div>

    <h2>Hi {{ $order->customer_name }},</h2>
    
    <p>Your order has been carefully packaged and shipped! You can expect to receive it soon.</p>

    @if($order->tracking_number || $trackingNumber)
        <div class="tracking-info">
            <h3>📦 Tracking Information</h3>
            <p><strong>Tracking Number:</strong></p>
            <div class="tracking-number">{{ $trackingNumber ?? $order->tracking_number }}</div>
            <p><strong>Shipping Method:</strong> {{ $order->shipping_method ?? 'Standard Shipping' }}</p>
            <p><strong>Estimated Delivery:</strong> {{ now()->addDays(3)->format('F d, Y') }}</p>
        </div>
    @endif

    <div class="order-summary">
        <h3>Shipment Details</h3>
        <p><strong>Ship Date:</strong> {{ now()->format('F d, Y') }}</p>
        <p><strong>Shipping Method:</strong> {{ $order->shipping_method ?? 'Standard Shipping' }}</p>
        @if($order->tracking_number || $trackingNumber)
            <p><strong>Carrier:</strong> {{ $this->getCarrierName($order->shipping_method) }}</p>
        @endif
    </div>

    <h3>Order Details</h3>
    <p>
        <strong>Order Number:</strong> {{ $order->order_number }}<br>
        <strong>Order Date:</strong> {{ $order->created_at->format('F d, Y') }}<br>
        <strong>Order Status:</strong> Shipped
    </p>

    <table class="order-details">
        <thead>
            <tr>
                <th>Product</th>
                <th>Quantity</th>
                <th>Price</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($order->items as $item)
                <tr>
                    <td>{{ $item->product_name }} @if($item->variant_name)({{ $item->variant_name }})@endif</td>
                    <td>{{ $item->quantity }}</td>
                    <td>{{ number_format($item->unit_price, 2) }} {{ $order->currency }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    @if ($order->shippingAddress)
        <h3>Shipping Address</h3>
        <p>
            {{ $order->shippingAddress->street }}<br>
            {{ $order->shippingAddress->city }}, {{ $order->shippingAddress->state }} {{ $order->shippingAddress->zip_code }}<br>
            {{ $order->shippingAddress->country }}
        </p>
    @endif

    <h3>What's Next?</h3>
    <p>Your package is now in transit. Here's what you can expect:</p>
    <ul>
        <li>You'll receive tracking updates via email</li>
        <li>The package will be delivered to your specified address</li>
        <li>You may need to sign for delivery depending on the shipping method</li>
        <li>If you're not home, the carrier will leave a delivery notice</li>
    </ul>

    @if($order->tracking_number || $trackingNumber)
        <div class="button-center">
            <a href="{{ $this->getTrackingUrl($order->shipping_method, $trackingNumber ?? $order->tracking_number) }}" class="button">Track Your Package</a>
        </div>
    @endif

    <div class="button-center">
        <a href="{{ route('orders.show', $order->order_number) }}" class="button">View Order Details</a>
    </div>

    <p>Thank you for your order! We hope you love your purchase.</p>

    <div class="alert alert-info">
        <strong>Need Help?</strong> If you have any questions about your shipment, please <a href="{{ route('contact') }}">contact our support team</a>.
    </div>
@endsection

@php
function getCarrierName($shippingMethod) {
    $carriers = [
        'ups' => 'UPS',
        'fedex' => 'FedEx',
        'usps' => 'USPS',
        'dhl' => 'DHL',
    ];
    
    foreach ($carriers as $key => $name) {
        if (str_contains(strtolower($shippingMethod ?? ''), $key)) {
            return $name;
        }
    }
    
    return 'Carrier';
}

function getTrackingUrl($shippingMethod, $trackingNumber) {
    $method = strtolower($shippingMethod ?? '');
    
    if (str_contains($method, 'ups')) {
        return "https://www.ups.com/track?tracknum={$trackingNumber}";
    } elseif (str_contains($method, 'fedex')) {
        return "https://www.fedex.com/fedextrack/?trknbr={$trackingNumber}";
    } elseif (str_contains($method, 'usps')) {
        return "https://tools.usps.com/go/TrackConfirmAction?tLabels={$trackingNumber}";
    } elseif (str_contains($method, 'dhl')) {
        return "https://www.dhl.com/en/express/tracking.html?AWB={$trackingNumber}";
    }
    
    return '#';
}
@endphp
