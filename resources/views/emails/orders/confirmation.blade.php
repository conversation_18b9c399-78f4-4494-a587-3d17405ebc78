<x-mail::message>
# Order Confirmation

Hi {{ $order->customer_name }},

Thanks for your order! We've received it and will start processing it shortly.

## Order Details

**Order Number:** {{ $order->order_number }}
**Order Date:** {{ $order->created_at->format('F d, Y') }}

<x-mail::table>
| Product | Quantity | Price |
|:--------|:---------|:------|
@foreach ($order->items as $item)
| {{ $item->product_name }} ({{ $item->variant_name }}) | {{ $item->quantity }} | {{ number_format($item->unit_price, 2) }} {{ $order->currency }} |
@endforeach
</x-mail::table>

**Subtotal:** {{ number_format($order->subtotal, 2) }} {{ $order->currency }}
**Shipping:** {{ number_format($order->shipping_cost, 2) }} {{ $order->currency }}
**Tax:** {{ number_format($order->tax_amount, 2) }} {{ $order->currency }}
**Total:** {{ number_format($order->total, 2) }} {{ $order->currency }}

@if ($order->shippingAddress)
## Shipping Address

{{ $order->shippingAddress->street }}
{{ $order->shippingAddress->city }}, {{ $order->shippingAddress->state }} {{ $order->shippingAddress->zip_code }}
{{ $order->shippingAddress->country }}
@endif

Thanks,<br>
{{ config('app.name') }}
</x-mail::message>
