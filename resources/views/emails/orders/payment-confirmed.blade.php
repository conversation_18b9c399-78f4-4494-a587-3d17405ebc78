@extends('emails.layouts.base')

@section('title', 'Payment Confirmed')

@section('header-title', 'Payment Confirmed')
@section('header-subtitle', 'Your payment has been successfully processed')

@section('content')
    <div class="alert alert-success">
        <strong>Great news!</strong> Your payment for order #{{ $order->order_number }} has been confirmed.
    </div>

    <h2>Hi {{ $order->customer_name }},</h2>
    
    <p>We're excited to let you know that your payment has been successfully processed! Your order is now being prepared for shipment.</p>

    <div class="order-summary">
        <h3>Payment Details</h3>
        <p><strong>Payment Method:</strong> {{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}</p>
        <p><strong>Transaction ID:</strong> {{ $payment->transaction_id ?? $payment->gateway_payment_id }}</p>
        <p><strong>Amount Paid:</strong> {{ number_format($payment->amount, 2) }} {{ $payment->currency }}</p>
        <p><strong>Payment Date:</strong> {{ $payment->processed_at ? $payment->processed_at->format('F d, Y \a\t g:i A') : now()->format('F d, Y \a\t g:i A') }}</p>
    </div>

    <h3>Order Summary</h3>
    <p>
        <strong>Order Number:</strong> {{ $order->order_number }}<br>
        <strong>Order Date:</strong> {{ $order->created_at->format('F d, Y') }}<br>
        <strong>Order Status:</strong> {{ ucfirst($order->status) }}
    </p>

    <table class="order-details">
        <thead>
            <tr>
                <th>Product</th>
                <th>Quantity</th>
                <th>Price</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($order->items as $item)
                <tr>
                    <td>{{ $item->product_name }} @if($item->variant_name)({{ $item->variant_name }})@endif</td>
                    <td>{{ $item->quantity }}</td>
                    <td>{{ number_format($item->unit_price, 2) }} {{ $order->currency }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <table class="totals">
        <tr>
            <td class="label">Subtotal:</td>
            <td>{{ number_format($order->subtotal, 2) }} {{ $order->currency }}</td>
        </tr>
        @if($order->shipping_cost > 0)
        <tr>
            <td class="label">Shipping:</td>
            <td>{{ number_format($order->shipping_cost, 2) }} {{ $order->currency }}</td>
        </tr>
        @endif
        @if($order->tax_amount > 0)
        <tr>
            <td class="label">Tax:</td>
            <td>{{ number_format($order->tax_amount, 2) }} {{ $order->currency }}</td>
        </tr>
        @endif
        @if($order->discount_amount > 0)
        <tr>
            <td class="label">Discount:</td>
            <td>-{{ number_format($order->discount_amount, 2) }} {{ $order->currency }}</td>
        </tr>
        @endif
        <tr class="total-row">
            <td class="label">Total Paid:</td>
            <td>{{ number_format($order->total, 2) }} {{ $order->currency }}</td>
        </tr>
    </table>

    @if ($order->shippingAddress)
        <h3>Shipping Address</h3>
        <p>
            {{ $order->shippingAddress->street }}<br>
            {{ $order->shippingAddress->city }}, {{ $order->shippingAddress->state }} {{ $order->shippingAddress->zip_code }}<br>
            {{ $order->shippingAddress->country }}
        </p>
    @endif

    <p>We'll send you another email once your order has been shipped with tracking information.</p>

    <div class="button-center">
        <a href="{{ route('orders.show', $order->order_number) }}" class="button">View Order Details</a>
    </div>

    <p>Thank you for your business!</p>
@endsection
