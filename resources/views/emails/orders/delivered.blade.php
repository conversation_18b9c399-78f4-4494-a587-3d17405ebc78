@extends('emails.layouts.base')

@section('title', 'Order Delivered')

@section('header-title', 'Order Delivered')
@section('header-subtitle', 'Your order has been successfully delivered!')

@section('content')
    <div class="alert alert-success">
        <strong>Delivery Confirmed!</strong> Your order #{{ $order->order_number }} has been delivered.
    </div>

    <h2>Hi {{ $order->customer_name }},</h2>
    
    <p>Great news! Your order has been successfully delivered. We hope you're thrilled with your purchase!</p>

    <div class="order-summary">
        <h3>Delivery Information</h3>
        <p><strong>Delivery Date:</strong> {{ now()->format('F d, Y \a\t g:i A') }}</p>
        <p><strong>Delivery Address:</strong> 
            @if($order->shippingAddress)
                {{ $order->shippingAddress->street }}, {{ $order->shippingAddress->city }}, {{ $order->shippingAddress->state }}
            @else
                {{ $order->billingAddress->street }}, {{ $order->billingAddress->city }}, {{ $order->billingAddress->state }}
            @endif
        </p>
        @if($order->tracking_number)
            <p><strong>Tracking Number:</strong> {{ $order->tracking_number }}</p>
        @endif
    </div>

    <h3>Order Summary</h3>
    <p>
        <strong>Order Number:</strong> {{ $order->order_number }}<br>
        <strong>Order Date:</strong> {{ $order->created_at->format('F d, Y') }}<br>
        <strong>Order Status:</strong> Delivered
    </p>

    <table class="order-details">
        <thead>
            <tr>
                <th>Product</th>
                <th>Quantity</th>
                <th>Price</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($order->items as $item)
                <tr>
                    <td>{{ $item->product_name }} @if($item->variant_name)({{ $item->variant_name }})@endif</td>
                    <td>{{ $item->quantity }}</td>
                    <td>{{ number_format($item->unit_price, 2) }} {{ $order->currency }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <table class="totals">
        <tr>
            <td class="label">Subtotal:</td>
            <td>{{ number_format($order->subtotal, 2) }} {{ $order->currency }}</td>
        </tr>
        @if($order->shipping_cost > 0)
        <tr>
            <td class="label">Shipping:</td>
            <td>{{ number_format($order->shipping_cost, 2) }} {{ $order->currency }}</td>
        </tr>
        @endif
        @if($order->tax_amount > 0)
        <tr>
            <td class="label">Tax:</td>
            <td>{{ number_format($order->tax_amount, 2) }} {{ $order->currency }}</td>
        </tr>
        @endif
        @if($order->discount_amount > 0)
        <tr>
            <td class="label">Discount:</td>
            <td>-{{ number_format($order->discount_amount, 2) }} {{ $order->currency }}</td>
        </tr>
        @endif
        <tr class="total-row">
            <td class="label">Total:</td>
            <td>{{ number_format($order->total, 2) }} {{ $order->currency }}</td>
        </tr>
    </table>

    <h3>What's Next?</h3>
    <p>Now that your order has been delivered:</p>
    <ul>
        <li>Check your package and ensure everything is as expected</li>
        <li>If you have any issues, contact us within 30 days</li>
        <li>Consider leaving a review to help other customers</li>
        <li>Keep your receipt for warranty purposes</li>
    </ul>

    <div class="alert alert-info">
        <strong>Love your purchase?</strong> We'd appreciate it if you could take a moment to leave a review and share your experience with other customers.
    </div>

    <div class="button-center">
        <a href="{{ route('orders.show', $order->order_number) }}" class="button">View Order Details</a>
    </div>

    <h3>Need Support?</h3>
    <p>If you have any questions about your order or need assistance with your products, our support team is here to help:</p>
    <ul>
        <li><a href="{{ route('contact') }}">Contact Support</a></li>
        <li><a href="{{ url('/returns') }}">Return Policy</a></li>
        <li><a href="{{ url('/warranty') }}">Warranty Information</a></li>
    </ul>

    <p>Thank you for choosing {{ config('app.name') }}! We appreciate your business and hope to serve you again soon.</p>

    <div class="alert alert-success">
        <strong>Satisfied with your purchase?</strong> Don't forget to follow us on social media for the latest updates, promotions, and new product announcements!
    </div>
@endsection
