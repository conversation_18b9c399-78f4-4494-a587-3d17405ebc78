@extends('emails.layouts.base')

@section('title', 'Payment Failed')

@section('header-title', 'Payment Failed')
@section('header-subtitle', 'There was an issue processing your payment')

@section('content')
    <div class="alert alert-error">
        <strong>Payment Failed:</strong> We were unable to process your payment for order #{{ $order->order_number }}.
    </div>

    <h2>Hi {{ $order->customer_name }},</h2>
    
    <p>We encountered an issue while processing your payment. Don't worry - your order is still reserved and you can try again.</p>

    @if($reason)
        <div class="alert alert-warning">
            <strong>Reason:</strong> {{ $reason }}
        </div>
    @endif

    <div class="order-summary">
        <h3>Payment Details</h3>
        <p><strong>Payment Method:</strong> {{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}</p>
        <p><strong>Amount:</strong> {{ number_format($payment->amount, 2) }} {{ $payment->currency }}</p>
        <p><strong>Status:</strong> Failed</p>
        @if($payment->transaction_id)
            <p><strong>Reference:</strong> {{ $payment->transaction_id }}</p>
        @endif
    </div>

    <h3>Order Summary</h3>
    <p>
        <strong>Order Number:</strong> {{ $order->order_number }}<br>
        <strong>Order Date:</strong> {{ $order->created_at->format('F d, Y') }}<br>
        <strong>Order Status:</strong> Payment Required
    </p>

    <table class="order-details">
        <thead>
            <tr>
                <th>Product</th>
                <th>Quantity</th>
                <th>Price</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($order->items as $item)
                <tr>
                    <td>{{ $item->product_name }} @if($item->variant_name)({{ $item->variant_name }})@endif</td>
                    <td>{{ $item->quantity }}</td>
                    <td>{{ number_format($item->unit_price, 2) }} {{ $order->currency }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <table class="totals">
        <tr>
            <td class="label">Subtotal:</td>
            <td>{{ number_format($order->subtotal, 2) }} {{ $order->currency }}</td>
        </tr>
        @if($order->shipping_cost > 0)
        <tr>
            <td class="label">Shipping:</td>
            <td>{{ number_format($order->shipping_cost, 2) }} {{ $order->currency }}</td>
        </tr>
        @endif
        @if($order->tax_amount > 0)
        <tr>
            <td class="label">Tax:</td>
            <td>{{ number_format($order->tax_amount, 2) }} {{ $order->currency }}</td>
        </tr>
        @endif
        @if($order->discount_amount > 0)
        <tr>
            <td class="label">Discount:</td>
            <td>-{{ number_format($order->discount_amount, 2) }} {{ $order->currency }}</td>
        </tr>
        @endif
        <tr class="total-row">
            <td class="label">Total Due:</td>
            <td>{{ number_format($order->total, 2) }} {{ $order->currency }}</td>
        </tr>
    </table>

    <h3>What's Next?</h3>
    <p>You can try processing your payment again using the button below. If you continue to experience issues, please try:</p>
    <ul>
        <li>Using a different payment method</li>
        <li>Checking with your bank or card issuer</li>
        <li>Contacting our support team for assistance</li>
    </ul>

    <div class="button-center">
        <a href="{{ route('checkout.payment', ['order' => $order->order_number]) }}" class="button">Try Payment Again</a>
    </div>

    <p>If you need help, please don't hesitate to <a href="{{ route('contact') }}">contact our support team</a>.</p>
@endsection
