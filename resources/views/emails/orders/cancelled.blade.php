@extends('emails.layouts.base')

@section('title', 'Order Cancelled')

@section('header-title', 'Order Cancelled')
@section('header-subtitle', 'Your order has been cancelled')

@section('content')
    <div class="alert alert-warning">
        <strong>Order Cancelled:</strong> Your order #{{ $order->order_number }} has been cancelled.
    </div>

    <h2>Hi {{ $order->customer_name }},</h2>
    
    <p>We're writing to inform you that your order has been cancelled. We apologize for any inconvenience this may cause.</p>

    @if($reason)
        <div class="order-summary">
            <h3>Cancellation Reason</h3>
            <p>{{ $reason }}</p>
        </div>
    @endif

    <h3>Order Details</h3>
    <p>
        <strong>Order Number:</strong> {{ $order->order_number }}<br>
        <strong>Order Date:</strong> {{ $order->created_at->format('F d, Y') }}<br>
        <strong>Cancellation Date:</strong> {{ now()->format('F d, Y') }}<br>
        <strong>Order Status:</strong> Cancelled
    </p>

    <table class="order-details">
        <thead>
            <tr>
                <th>Product</th>
                <th>Quantity</th>
                <th>Price</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($order->items as $item)
                <tr>
                    <td>{{ $item->product_name }} @if($item->variant_name)({{ $item->variant_name }})@endif</td>
                    <td>{{ $item->quantity }}</td>
                    <td>{{ number_format($item->unit_price, 2) }} {{ $order->currency }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <table class="totals">
        <tr>
            <td class="label">Subtotal:</td>
            <td>{{ number_format($order->subtotal, 2) }} {{ $order->currency }}</td>
        </tr>
        @if($order->shipping_cost > 0)
        <tr>
            <td class="label">Shipping:</td>
            <td>{{ number_format($order->shipping_cost, 2) }} {{ $order->currency }}</td>
        </tr>
        @endif
        @if($order->tax_amount > 0)
        <tr>
            <td class="label">Tax:</td>
            <td>{{ number_format($order->tax_amount, 2) }} {{ $order->currency }}</td>
        </tr>
        @endif
        @if($order->discount_amount > 0)
        <tr>
            <td class="label">Discount:</td>
            <td>-{{ number_format($order->discount_amount, 2) }} {{ $order->currency }}</td>
        </tr>
        @endif
        <tr class="total-row">
            <td class="label">Cancelled Amount:</td>
            <td>{{ number_format($order->total, 2) }} {{ $order->currency }}</td>
        </tr>
    </table>

    @if($order->isPaid())
        <div class="alert alert-info">
            <strong>Refund Information:</strong> Since your order was already paid, we will process a full refund to your original payment method. Please allow 3-5 business days for the refund to appear in your account.
        </div>

        <div class="order-summary">
            <h3>Refund Details</h3>
            <p><strong>Refund Amount:</strong> {{ number_format($order->total, 2) }} {{ $order->currency }}</p>
            <p><strong>Refund Method:</strong> Original payment method</p>
            <p><strong>Processing Time:</strong> 3-5 business days</p>
        </div>
    @endif

    <h3>What Happens Next?</h3>
    <ul>
        @if($order->isPaid())
            <li>Your refund will be processed within 1-2 business days</li>
            <li>You'll receive a confirmation email once the refund is issued</li>
            <li>The refund will appear in your account within 3-5 business days</li>
        @endif
        <li>All reserved inventory has been released</li>
        <li>You can place a new order anytime if you wish</li>
        <li>Your account remains active and unaffected</li>
    </ul>

    <h3>Still Interested?</h3>
    <p>If you'd like to place a new order for these or similar items, you can:</p>
    <ul>
        <li>Browse our current selection</li>
        <li>Check for any ongoing promotions</li>
        <li>Contact our support team for assistance</li>
    </ul>

    <div class="button-center">
        <a href="{{ url('/') }}" class="button">Continue Shopping</a>
    </div>

    <h3>Need Help?</h3>
    <p>If you have any questions about this cancellation or need assistance with a new order, please don't hesitate to contact us:</p>
    <ul>
        <li><a href="{{ route('contact') }}">Contact Support</a></li>
        <li>Email: {{ config('mail.from.address') }}</li>
        <li>Phone: {{ config('app.phone', '1-800-SUPPORT') }}</li>
    </ul>

    <p>We apologize again for any inconvenience and appreciate your understanding. We hope to serve you better in the future.</p>

    <div class="alert alert-info">
        <strong>Feedback Welcome:</strong> If you have any feedback about your experience, we'd love to hear from you. Your input helps us improve our service.
    </div>
@endsection
