<x-admin-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                <i class="fas fa-star-half-alt me-2"></i>{{ __('Review Management') }}
            </h2>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <!-- Filter Form -->
                    <div class="mb-8 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg shadow">
                        <form method="GET" action="{{ route('admin.reviews.index') }}" id="filter-form" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 items-end">
                            <!-- Search -->
                            <div>
                                <x-input-label for="search" :value="__('Search')" />
                                <x-text-input id="search" name="search" type="text" class="mt-1 block w-full" placeholder="Review title, product, user..." value="{{ request('search') }}" />
                            </div>

                            <!-- Status -->
                            <div>
                                <x-input-label for="status" :value="__('Status')" />
                                <select id="status" name="status" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm">
                                    <option value="">All Reviews</option>
                                    <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>{{ __('Pending') }}</option>
                                    <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>{{ __('Approved') }}</option>
                                    <option value="featured" {{ request('status') === 'featured' ? 'selected' : '' }}>{{ __('Featured') }}</option>
                                </select>
                            </div>

                            <!-- Sort By -->
                            <div>
                                <x-input-label for="sort_by" :value="__('Sort By')" />
                                <select id="sort_by" name="sort_by" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm">
                                    <option value="newest" {{ request('sort_by', 'newest') === 'newest' ? 'selected' : '' }}>{{ __('Newest First') }}</option>
                                    <option value="oldest" {{ request('sort_by') === 'oldest' ? 'selected' : '' }}>{{ __('Oldest First') }}</option>
                                    <option value="rating_high" {{ request('sort_by') === 'rating_high' ? 'selected' : '' }}>{{ __('Highest Rating') }}</option>
                                    <option value="rating_low" {{ request('sort_by') === 'rating_low' ? 'selected' : '' }}>{{ __('Lowest Rating') }}</option>
                                    <option value="helpful" {{ request('sort_by') === 'helpful' ? 'selected' : '' }}>{{ __('Most Helpful') }}</option>
                                </select>
                            </div>

                            <!-- Submit & Clear -->
                            <div class="flex items-center space-x-2 col-span-1 sm:col-span-2 md:col-span-1 xl:col-span-2 self-end pb-1">
                                <x-primary-button type="submit" class="w-full sm:w-auto">
                                    <i class="fas fa-filter mr-2"></i>{{ __('Filter') }}
                                </x-primary-button>

                                @if(request('search') || request('status') || request('sort_by'))
                                    <a href="{{ route('admin.reviews.index') }}" class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 bg-gray-200 dark:bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-gray-700 dark:text-gray-300 uppercase tracking-widest hover:bg-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                        <i class="fas fa-times mr-2"></i>{{ __('Clear') }}
                                    </a>
                                @endif
                            </div>
                        </form>
                    </div>

                    <!-- Review Cards -->
                    @if($reviews->count() > 0)
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($reviews as $review)
                                <div class="bg-white dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700/50 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col">
                                    <!-- Card Header: Review Title, Product, ID -->
                                    <div class="p-4 border-b border-gray-200 dark:border-gray-700/50">
                                        <div class="flex justify-between items-start">
                                            <h3 class="text-lg font-semibold text-indigo-600 dark:text-indigo-400 leading-tight">
                                                {{ Str::limit($review->title, 40) }}
                                            </h3>
                                            <span class="text-xs text-gray-400 dark:text-gray-500 ml-2">#{{ $review->id }}</span>
                                        </div>
                                        <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                                            For: <a href="{{ route('products.show', $review->product->slug) }}" class="hover:underline" target="_blank">
                                                {{ Str::limit($review->product->name, 35) }}
                                            </a>
                                        </p>
                                    </div>

                                    <!-- Card Body: User, Rating, Date -->
                                    <div class="p-4 grow">
                                        <div class="flex items-center justify-between mb-2">
                                            <p class="text-sm text-gray-700 dark:text-gray-300">
                                                <i class="fas fa-user mr-1 text-gray-400 dark:text-gray-500"></i> {{ $review->user->name }}
                                            </p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                <i class="fas fa-calendar-alt mr-1 text-gray-400 dark:text-gray-500"></i> {{ $review->created_at->format('M d, Y') }}
                                            </p>
                                        </div>

                                        <div class="flex items-center mb-3">
                                            <div class="text-yellow-400 star-rating">{!! $review->star_rating !!}</div>
                                            <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">({{ $review->rating }}/5)</span>
                                        </div>

                                        <!-- Statuses -->
                                        <div class="flex items-center space-x-2 mb-3">
                                            @if($review->is_approved)
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                                    <i class="fas fa-check-circle mr-1"></i>{{ __('Approved') }}
                                                </span>
                                            @else
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                                                    <i class="fas fa-hourglass-half mr-1"></i>{{ __('Pending') }}
                                                </span>
                                            @endif

                                            @if($review->is_featured)
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                                    <i class="fas fa-star mr-1"></i>{{ __('Featured') }}
                                                </span>
                                            @endif
                                        </div>
                                        
                                        {{-- Optional: Display a snippet of the review body if available --}}
                                        {{--
                                        @if($review->body)
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-2 italic">
                                            "{{ Str::limit($review->body, 100) }}"
                                        </p>
                                        @endif
                                        --}}
                                    </div>

                                    <!-- Card Footer: Actions -->
                                    <div class="p-4 bg-gray-50 dark:bg-gray-900/30 border-t border-gray-200 dark:border-gray-700/50 rounded-b-lg">
                                        <div class="flex justify-end items-center space-x-1 sm:space-x-2">
                                            <a href="{{ route('admin.reviews.edit', $review) }}"
                                               class="p-1.5 rounded-md text-indigo-600 dark:text-indigo-400 hover:bg-indigo-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                               title="{{ __('View Details') }}">
                                                <i class="fas fa-eye text-sm sm:mr-1"></i><span class="hidden sm:inline text-xs">{{ __('View') }}</span>
                                            </a>

                                            @if(!$review->is_approved)
                                                <button type="button"
                                                        class="p-1.5 rounded-md text-green-600 dark:text-green-400 hover:bg-green-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 approve-review"
                                                        data-id="{{ $review->id }}" title="{{ __('Approve') }}">
                                                    <i class="fas fa-check text-sm sm:mr-1"></i><span class="hidden sm:inline text-xs">{{ __('Approve') }}</span>
                                                </button>
                                            @else
                                                <button type="button"
                                                        class="p-1.5 rounded-md text-yellow-600 dark:text-yellow-400 hover:bg-yellow-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 reject-review"
                                                        data-id="{{ $review->id }}" title="{{ __('Reject') }}">
                                                    <i class="fas fa-times text-sm sm:mr-1"></i><span class="hidden sm:inline text-xs">{{ __('Reject') }}</span>
                                                </button>
                                            @endif

                                            <button type="button"
                                                    class="p-1.5 rounded-md toggle-featured 
                                                           {{ $review->is_featured ? 'text-yellow-500 dark:text-yellow-400 hover:bg-yellow-100' : 'text-gray-400 dark:text-gray-500 hover:bg-gray-200' }} 
                                                           dark:hover:bg-gray-700 focus:outline-none focus:ring-2 {{ $review->is_featured ? 'focus:ring-yellow-500' : 'focus:ring-gray-500' }}"
                                                    data-id="{{ $review->id }}"
                                                    title="{{ $review->is_featured ? __('Remove from Featured') : __('Mark as Featured') }}">
                                                <i class="fas fa-star text-sm sm:mr-1"></i><span class="hidden sm:inline text-xs">{{ $review->is_featured ? __('Unfeature') : __('Feature') }}</span>
                                            </button>

                                            <button type="button"
                                                    class="p-1.5 rounded-md text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-red-500 delete-review"
                                                    data-id="{{ $review->id }}" title="{{ __('Delete') }}">
                                                <i class="fas fa-trash text-sm sm:mr-1"></i><span class="hidden sm:inline text-xs">{{ __('Delete') }}</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path vector-effect="non-scaling-stroke" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">{{ __('No reviews found.') }}</h3>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                {{ __('Try adjusting your search or filter criteria.') }}
                            </p>
                        </div>
                    @endif

                    <!-- Pagination -->
                    @if ($reviews->hasPages())
                    <div class="mt-8">
                        {{ $reviews->appends(request()->query())->links() }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Reusable Confirmation Modal (Keep as is) -->
    <div 
        x-data="{ 
            isOpen: false, 
            actionUrl: null, 
            message: null, 
            title: null, 
            actionMethod: 'POST' 
        }"
        x-show="isOpen" 
        x-on:open-confirm-modal.window="isOpen = true; actionUrl = $event.detail.actionUrl; message = $event.detail.message; title = $event.detail.title; actionMethod = $event.detail.actionMethod || 'POST'"
        x-cloak
        class="fixed inset-0 overflow-y-auto z-50"
        aria-labelledby="modal-title" role="dialog" aria-modal="true"
    >
        <!-- Overlay -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75 transition-opacity" @click="isOpen = false"></div>

        <!-- Modal Content -->
        <div class="relative z-10 mx-auto mt-10 max-w-lg px-4 sm:px-0">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="px-6 py-4">
                    <div class="flex items-start">
                        <div class="mt-0 text-left">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100" id="modal-title" x-text="title"></h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-600 dark:text-gray-400" x-text="message"></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700/50 sm:flex sm:flex-row-reverse">
                    <form :action="actionUrl" method="POST" class="inline-block">
                        @csrf
                        <input type="hidden" name="_method" :value="actionMethod">
                        <button 
                            type="submit" 
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                            Confirm
                        </button>
                    </form>
                    <button 
                        type="button" 
                        class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm"
                        @click="isOpen = false"
                    >
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>


    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Filter form auto-submit on select change
            document.querySelectorAll('#filter-form #status, #filter-form #sort_by').forEach(select => {
                select.addEventListener('change', function() {
                    document.getElementById('filter-form').submit();
                });
            });
            
            // Debounce search input if desired
            // let searchTimeout;
            // document.getElementById('search').addEventListener('input', function() {
            //     clearTimeout(searchTimeout);
            //     searchTimeout = setTimeout(() => {
            //         document.getElementById('filter-form').submit();
            //     }, 500); // Adjust delay as needed
            // });


            // Button actions (keep as is, they target classes/data-attributes)
            document.querySelectorAll('.approve-review').forEach(button => {
                button.addEventListener('click', function () {
                    const reviewId = this.getAttribute('data-id');
                    // Ensure the URL is correctly formed, assuming your routes are set up
                    const actionUrl = `{{ url('admin/reviews') }}/${reviewId}/approve`; 
                    const title = 'Confirm Approval';
                    const message = 'Are you sure you want to approve this review? This action cannot be undone.';

                    window.dispatchEvent(new CustomEvent('open-confirm-modal', {
                        detail: {
                            title,
                            message,
                            actionUrl,
                            actionMethod: 'PUT'
                        }
                    }));
                });
            });

            document.querySelectorAll('.reject-review').forEach(button => {
                button.addEventListener('click', function () {
                    const reviewId = this.getAttribute('data-id');
                    const actionUrl = `{{ url('admin/reviews') }}/${reviewId}/reject`;
                    const title = 'Confirm Rejection';
                    const message = 'Are you sure you want to reject this review? This action cannot be undone.';

                    window.dispatchEvent(new CustomEvent('open-confirm-modal', {
                        detail: {
                            title,
                            message,
                            actionUrl,
                            actionMethod: 'PUT'
                        }
                    }));
                });
            });

            document.querySelectorAll('.toggle-featured').forEach(button => {
                button.addEventListener('click', function () {
                    const reviewId = this.getAttribute('data-id');
                    const actionUrl = `{{ url('admin/reviews') }}/${reviewId}/toggle-featured`;
                    const title = 'Confirm Toggle Featured';
                    const message = 'Are you sure you want to change the featured status of this review?';

                    window.dispatchEvent(new CustomEvent('open-confirm-modal', {
                        detail: {
                            title,
                            message,
                            actionUrl,
                            actionMethod: 'PUT'
                        }
                    }));
                });
            });

            document.querySelectorAll('.delete-review').forEach(button => {
                button.addEventListener('click', function () {
                    const reviewId = this.getAttribute('data-id');
                    const actionUrl = `{{ url('admin/reviews') }}/${reviewId}`;
                    const title = 'Confirm Deletion';
                    const message = 'Are you sure you want to delete this review? This action cannot be undone.';

                    window.dispatchEvent(new CustomEvent('open-confirm-modal', {
                        detail: {
                            title,
                            message,
                            actionUrl,
                            actionMethod: 'DELETE'
                        }
                    }));
                });
            });
        });
    </script>

    <style>
        /* Optional: Custom styles for star rating (if not already globally defined) */
        .star-rating svg {
            width: 1em; /* Adjust size as needed */
            height: 1em;
            display: inline-block;
            fill: currentColor; /* color is set by text-yellow-400 */
        }
        /* Ensure modal is above other content */
        .fixed.inset-0.overflow-y-auto.z-50 {
            z-index: 50;
        }
    </style>
</x-admin-layout>