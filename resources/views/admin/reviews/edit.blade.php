<x-admin-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <div>
                <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200 leading-tight">
                    <i class="fas fa-star-half-alt me-2 text-indigo-500"></i>{{ __('Review Details') }}
                </h2>
                <p class="text-sm text-gray-500 dark:text-gray-400">Viewing review #{{ $review->id }}</p>
            </div>
            <a href="{{ route('admin.reviews.index') }}" class="mt-3 sm:mt-0 inline-flex items-center rounded-full py-2 px-5 text-sm bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-widest hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                <i class="fas fa-arrow-left me-2"></i> {{ __('Back to All Reviews') }}
            </a>
        </div>
    </x-slot>

    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">

                <!-- Left Column: Review Info & Actions -->
                <div class="lg:col-span-2 space-y-6">
                    <div class="bg-white dark:bg-gray-800 shadow-xl rounded-lg overflow-hidden">
                        <div class="p-6">
                            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
                                <h3 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">{{ Str::limit($review->title, 70) }}</h3>
                                <div class="flex gap-2 mt-2 sm:mt-0">
                                    @if($review->is_approved)
                                        <span class="inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-700 dark:bg-green-700 dark:text-green-100">
                                            <i class="fas fa-check-circle me-1.5"></i>Approved
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-700 dark:bg-yellow-700 dark:text-yellow-100">
                                            <i class="fas fa-hourglass-half me-1.5"></i>Pending
                                        </span>
                                    @endif
                                    @if($review->is_featured)
                                        <span class="inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-700 dark:bg-blue-700 dark:text-blue-100">
                                            <i class="fas fa-star me-1.5"></i>Featured
                                        </span>
                                    @endif
                                </div>
                            </div>

                            <div class="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-5">
                                <div class="text-yellow-400 flex items-center">
                                    {!! $review->star_rating !!} <span class="ms-1 text-gray-500 dark:text-gray-400">({{ $review->rating }}/5)</span>
                                </div>
                                <span class="flex items-center"><i class="fas fa-user me-1.5 text-gray-400 dark:text-gray-500"></i>{{ $review->user->name }}</span>
                                <span class="flex items-center"><i class="fas fa-calendar-alt me-1.5 text-gray-400 dark:text-gray-500"></i>{{ $review->created_at->format('F j, Y \a\t g:i A') }}</span>
                                @if($review->verified_purchase)
                                    <span class="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded bg-teal-100 text-teal-700 dark:bg-teal-700 dark:text-teal-100">
                                        <i class="fas fa-badge-check me-1"></i>Verified Purchase
                                    </span>
                                @endif
                            </div>

                            <div class="mb-6">
                                <h5 class="font-semibold text-gray-700 dark:text-gray-300 mb-2">Review Content:</h5>
                                <div class="prose dark:prose-invert max-w-none p-4 bg-gray-50 dark:bg-gray-700/50 rounded-md border border-gray-200 dark:border-gray-600/50 text-gray-700 dark:text-gray-300">
                                    {!! nl2br(e($review->content)) !!}
                                </div>
                            </div>

                            @if($review->images && count($review->images) > 0)
                                <div class="mb-6">
                                    <h5 class="font-semibold text-gray-700 dark:text-gray-300 mb-3">Attached Images:</h5>
                                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                                        @foreach($review->images as $image)
                                            <a href="{{ Storage::url($image) }}" data-fslightbox="review-gallery-{{$review->id}}" class="block group rounded-md overflow-hidden border-2 border-transparent hover:border-indigo-500 transition duration-150">
                                                <img src="{{ Storage::url($image) }}" alt="Review Image" class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300" loading="lazy">
                                            </a>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>

                        <!-- Action Buttons Footer -->
                        <div class="px-6 py-4 bg-gray-50 dark:bg-gray-800/60 border-t border-gray-200 dark:border-gray-700">
                            <h4 class="text-md font-semibold text-gray-700 dark:text-gray-300 mb-3">Manage Review:</h4>
                            <div class="flex flex-wrap gap-3">
                                @if(!$review->is_approved)
                                    <button type="button"
                                            class="action-button approve-review rounded-full py-2 px-5 text-sm
                                                bg-green-100 text-green-700 hover:bg-green-200 focus:ring-green-500
                                                dark:bg-green-500/20 dark:text-green-300 dark:hover:bg-green-500/30"
                                            data-id="{{ $review->id }}">
                                        <i class="fas fa-check me-2"></i> Approve
                                    </button>
                                @else
                                    <button type="button"
                                            class="action-button reject-review rounded-full py-2 px-5 text-sm
                                                bg-yellow-100 text-yellow-700 hover:bg-yellow-200 focus:ring-yellow-500
                                                dark:bg-yellow-500/20 dark:text-yellow-300 dark:hover:bg-yellow-500/30"
                                            data-id="{{ $review->id }}">
                                        <i class="fas fa-times me-2"></i> Reject
                                    </button>
                                @endif

                                <button type="button"
                                        class="action-button toggle-featured rounded-full py-2 px-5 text-sm
                                            @if($review->is_featured)
                                                bg-orange-100 text-orange-700 hover:bg-orange-200 focus:ring-orange-500
                                                dark:bg-orange-500/20 dark:text-orange-300 dark:hover:bg-orange-500/30
                                            @else
                                                bg-blue-100 text-blue-700 hover:bg-blue-200 focus:ring-blue-500
                                                dark:bg-blue-500/20 dark:text-blue-300 dark:hover:bg-blue-500/30
                                            @endif"
                                        data-id="{{ $review->id }}">
                                    <i class="fas fa-star me-2"></i> {{ $review->is_featured ? 'Unfeature' : 'Feature' }}
                                </button>

                                <button type="button"
                                        class="action-button delete-review rounded-full py-2 px-5 text-sm
                                            bg-red-100 text-red-700 hover:bg-red-200 focus:ring-red-500
                                            dark:bg-red-500/20 dark:text-red-300 dark:hover:bg-red-500/30"
                                        data-id="{{ $review->id }}">
                                    <i class="fas fa-trash me-2"></i> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Product & Reviewer Info -->
                <div class="space-y-6">
                    <!-- Product Information Card -->
                    <div class="bg-white dark:bg-gray-800 shadow-xl rounded-lg overflow-hidden">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b dark:border-gray-700 pb-3">
                                <i class="fas fa-box-open me-2 text-indigo-500"></i>Product Information
                            </h3>
                            @if($review->product->getFirstMediaUrl('thumbnail'))
                            <div class="mb-4">
                                <a href="{{ route('products.show', $review->product->slug) }}" target="_blank">
                                    <img src="{{ $review->product->getFirstMediaUrl('thumbnail') }}"
                                         alt="{{ $review->product->name }}"
                                         class="rounded-md w-full h-48 object-contain mx-auto hover:opacity-90 transition"
                                         loading="lazy">
                                </a>
                            </div>
                            @endif
                            <h5 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-1">
                                <a href="{{ route('products.show', $review->product->slug) }}" target="_blank" class="hover:text-indigo-600 dark:hover:text-indigo-400 hover:underline">
                                    {{ $review->product->name }}
                                </a>
                            </h5>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-3">SKU: {{ $review->product->code ?? 'N/A' }}</p>

                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between"><span class="text-gray-600 dark:text-gray-400">Price:</span> <span class="font-medium text-gray-800 dark:text-gray-200">{{ $review->product->formatted_price }}</span></div>
                                <div class="flex justify-between"><span class="text-gray-600 dark:text-gray-400">Category:</span> <span class="text-gray-800 dark:text-gray-200">{{ $review->product->category->name ?? 'N/A' }}</span></div>
                                <div class="flex justify-between"><span class="text-gray-600 dark:text-gray-400">Total Reviews:</span> <span class="text-gray-800 dark:text-gray-200">{{ $review->product->reviews_count }}</span></div>
                                <div class="flex justify-between items-center"><span class="text-gray-600 dark:text-gray-400">Avg. Rating:</span>
                                    <span class="flex items-center text-yellow-400">{!! $review->product->star_rating !!} <span class="ms-1 text-gray-500 dark:text-gray-400">({{ number_format($review->product->average_rating, 1) }})</span></span>
                                </div>
                            </div>
                            <a href="{{ route('products.show', $review->product->slug) }}"
                               target="_blank"
                               class="block w-full mt-6 text-center rounded-full py-2 px-5 text-sm bg-indigo-600 text-white font-semibold hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition">
                                <i class="fas fa-external-link-alt me-2"></i>View Product Page
                            </a>
                        </div>
                    </div>

                    <!-- Reviewer Information Card -->
                    <div class="bg-white dark:bg-gray-800 shadow-xl rounded-lg overflow-hidden">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b dark:border-gray-700 pb-3">
                                <i class="fas fa-user-circle me-2 text-indigo-500"></i>Reviewer Information
                            </h3>
                            <div class="flex items-center mb-4">
                                <img src="{{ $review->user->profile_photo_url }}"
                                     alt="{{ $review->user->name }}"
                                     class="rounded-full w-16 h-16 object-cover me-4 border-2 border-gray-200 dark:border-gray-700"
                                     loading="lazy">
                                <div>
                                    <h5 class="text-lg font-medium text-gray-800 dark:text-gray-200">{{ $review->user->name }}</h5>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $review->user->email }}</p>
                                </div>
                            </div>

                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between"><span class="text-gray-600 dark:text-gray-400">Member Since:</span> <span class="text-gray-800 dark:text-gray-200">{{ $review->user->created_at->format('M d, Y') }}</span></div>
                                <div class="flex justify-between"><span class="text-gray-600 dark:text-gray-400">Total Reviews by User:</span> <span class="text-gray-800 dark:text-gray-200">{{ $review->user->reviews_count }}</span></div>
                                {{-- <div class="flex justify-between"><span class="text-gray-600 dark:text-gray-400">Verified Purchases:</span> <span>{{ $review->user->verified_purchases_count }}</span></div> --}}
                            </div>
                             <a href="mailto:{{ $review->user->email }}"
                               class="block w-full mt-6 text-center rounded-full py-2 px-5 text-sm bg-gray-600 dark:bg-gray-700 text-white font-semibold hover:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition">
                                <i class="fas fa-envelope me-2"></i>Contact Reviewer
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reusable Confirmation Modal (Alpine.js) - Same as refined one from previous interaction -->
    <div x-data="{
            isOpen: false,
            actionUrl: '',
            message: '',
            title: '',
            actionMethod: 'POST',
            showReasonInput: false,
            rejectionReason: ''
         }"
         x-show="isOpen"
         x-on:open-confirm-modal.window="
             isOpen = true;
             actionUrl = $event.detail.actionUrl;
             message = $event.detail.message;
             title = $event.detail.title;
             actionMethod = $event.detail.actionMethod || 'POST';
             showReasonInput = $event.detail.showReasonInput || false;
             rejectionReason = ''; // Clear reason on modal open
             $nextTick(() => { if (showReasonInput) $refs.reasonInputModal.focus(); });
         "
         x-cloak
         class="fixed inset-0 z-[999] flex items-center justify-center p-4"
         role="dialog" aria-modal="true" :aria-labelledby="'modal-title-' + actionUrl.split('/').pop()"
    >
        <!-- Overlay -->
        <div class="fixed inset-0 bg-black/50 dark:bg-black/70 transition-opacity" @click="isOpen = false"></div>

        <!-- Modal Content -->
        <div class="relative z-10 w-full max-w-md bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden transform transition-all sm:my-8">
            <form @submit.prevent="
                const form = $el;
                const formData = new FormData(form);
                if (actionMethod !== 'GET') formData.append('_method', actionMethod);

                fetch(actionUrl, {
                    method: 'POST', // Always POST for FormData, _method handles actual verb
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name=\'csrf-token\']').getAttribute('content'),
                        'Accept': 'application/json', // Important for Laravel to know it's an AJAX request
                    },
                    body: formData
                })
                .then(response => response.json().then(data => ({ status: response.status, body: data })))
                .then(({ status, body }) => {
                    isOpen = false;
                    if (status >= 200 && status < 300) {
                        // Success: reload page or show success message
                        window.location.reload(); // Or use a toast notification
                        // Example: dispatchToast('Success!', body.message || 'Action completed.', 'success');
                    } else {
                        // Error: show error message
                        console.error('Error:', body);
                        alert(body.message || 'An error occurred. Please try again.');
                        // Example: dispatchToast('Error!', body.message || 'Action failed.', 'error');
                    }
                })
                .catch(error => {
                    isOpen = false;
                    console.error('Fetch Error:', error);
                    alert('A network error occurred. Please try again.');
                    // Example: dispatchToast('Network Error!', 'Could not connect to server.', 'error');
                });
            ">
                @csrf {{-- This is for Blade, not directly used by fetch but good practice --}}

                <div class="p-6">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/50 sm:mx-0 sm:h-10 sm:w-10"
                             x-show="actionMethod === 'DELETE' || title.toLowerCase().includes('reject') || title.toLowerCase().includes('delete')">
                            <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                        </div>
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 dark:bg-green-900/50 sm:mx-0 sm:h-10 sm:w-10"
                             x-show="title.toLowerCase().includes('approve')">
                            <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                        </div>
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900/50 sm:mx-0 sm:h-10 sm:w-10"
                             x-show="title.toLowerCase().includes('feature')">
                            <i class="fas fa-star text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ms-4 sm:text-left">
                            <h3 class="text-lg font-semibold leading-6 text-gray-900 dark:text-gray-100" :id="'modal-title-' + actionUrl.split('/').pop()" x-text="title"></h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-600 dark:text-gray-400" x-html="message"></p>
                            </div>
                        </div>
                    </div>

                    <div x-show="showReasonInput" class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <label for="rejection_reason_modal_detail" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Rejection Reason (Optional)</label>
                        <textarea x-ref="reasonInputModal" id="rejection_reason_modal_detail" name="rejection_reason" rows="3"
                                  class="block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 dark:bg-gray-700 dark:text-gray-200"
                                  x-model="rejectionReason"></textarea>
                    </div>
                </div>

                <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700/60 sm:flex sm:flex-row-reverse sm:px-6">
                    <button type="submit"
                            class="inline-flex w-full justify-center rounded-md px-3 py-2 text-sm font-semibold text-white shadow-sm sm:ms-3 sm:w-auto"
                            :class="{
                                'bg-red-600 hover:bg-red-500 focus-visible:outline-red-600': actionMethod === 'DELETE' || title.toLowerCase().includes('reject') || title.toLowerCase().includes('delete'),
                                'bg-green-600 hover:bg-green-500 focus-visible:outline-green-600': title.toLowerCase().includes('approve'),
                                'bg-blue-600 hover:bg-blue-500 focus-visible:outline-blue-600': title.toLowerCase().includes('feature') && !title.toLowerCase().includes('unfeature'),
                                'bg-orange-600 hover:bg-orange-500 focus-visible:outline-orange-600': title.toLowerCase().includes('unfeature'),
                                'bg-indigo-600 hover:bg-indigo-500 focus-visible:outline-indigo-600': !(actionMethod === 'DELETE' || title.toLowerCase().includes('reject') || title.toLowerCase().includes('delete') || title.toLowerCase().includes('approve') || title.toLowerCase().includes('feature'))
                            }">
                        Confirm
                    </button>
                    <button type="button" @click="isOpen = false"
                            class="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-gray-600 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-200 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-500 hover:bg-gray-50 dark:hover:bg-gray-500 sm:mt-0 sm:w-auto">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add CSRF Token for JavaScript Fetch -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <script src="https://cdn.jsdelivr.net/npm/fslightbox@5.3.0/index.min.js"></script> {{-- Updated FSLightbox CDN --}}
    <script>
    document.addEventListener('DOMContentLoaded', function () {
        // FSLightbox: The UMD version usually auto-initializes.
        // If you have multiple galleries and need specific control:
        // const reviewGallery = new FSLightbox();
        // document.querySelectorAll('[data-fslightbox^="review-gallery-"]').forEach(el => {
        //     reviewGallery.props.sources.push(el.href);
        // });
        // Or, if each review has its own gallery, ensure data-fslightbox has unique values.
        // The `data-fslightbox="review-gallery-{{$review->id}}"` approach is usually fine.

        // Action buttons for Approve, Reject, Toggle Featured, Delete
        document.querySelectorAll('.action-button').forEach(button => {
            button.addEventListener('click', function () {
                const reviewId = this.dataset.id;
                let actionType = '';
                let actionEndpoint = '';
                let httpMethod = 'PUT'; // Default for most actions
                let modalTitle = 'Confirm Action';
                let modalMessage = 'Are you sure you want to proceed?';
                let showReasonField = false;

                const isCurrentlyFeatured = this.classList.contains('toggle-featured') && this.textContent.trim().includes('Unfeature');

                if (this.classList.contains('approve-review')) {
                    actionType = 'approve';
                    actionEndpoint = `${reviewId}/approve`;
                    modalTitle = 'Approve Review';
                    modalMessage = 'Are you sure you want to approve this review? It will become publicly visible.';
                } else if (this.classList.contains('reject-review')) {
                    actionType = 'reject';
                    actionEndpoint = `${reviewId}/reject`;
                    modalTitle = 'Reject Review';
                    modalMessage = 'Are you sure you want to reject this review? It will be hidden. You can provide a reason below (optional).';
                    showReasonField = true;
                } else if (this.classList.contains('toggle-featured')) {
                    actionType = 'toggle-featured';
                    actionEndpoint = `${reviewId}/toggle-featured`;
                    modalTitle = isCurrentlyFeatured ? 'Unfeature Review' : 'Feature Review';
                    modalMessage = `Are you sure you want to ${isCurrentlyFeatured ? 'remove this review from featured' : 'mark this review as featured'}?`;
                } else if (this.classList.contains('delete-review')) {
                    actionType = 'delete';
                    actionEndpoint = `${reviewId}`;
                    httpMethod = 'DELETE';
                    modalTitle = 'Delete Review';
                    modalMessage = '<strong>Warning:</strong> This review will be permanently deleted. This action cannot be undone.';
                }

                const fullActionUrl = `{{ url('admin/reviews') }}/${actionEndpoint.replace(/^\//, '')}`; // Ensure no double slashes

                window.dispatchEvent(new CustomEvent('open-confirm-modal', {
                    detail: {
                        title: modalTitle,
                        message: modalMessage,
                        actionUrl: fullActionUrl,
                        actionMethod: httpMethod,
                        showReasonInput: showReasonField,
                        // reviewId: reviewId // Not strictly needed by modal JS anymore
                    }
                }));
            });
        });
    });
    </script>


</x-admin-layout>