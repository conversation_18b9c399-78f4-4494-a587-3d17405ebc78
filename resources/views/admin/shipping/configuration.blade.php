<x-admin-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Shipping Configuration') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">

                    @if (session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    @if (session('info'))
                        <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <span class="block sm:inline">{{ session('info') }}</span>
                        </div>
                    @endif

                    @if ($errors->any())
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <strong class="font-bold">{{ __('Please fix the following errors:') }}</strong>
                            <ul class="mt-2 list-disc list-inside">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('admin.shipping.configuration.update') }}" method="POST" class="space-y-6">
                        @csrf
                        @method('PUT')

                        <!-- Fallback Behavior -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg border border-gray-200 dark:border-gray-600">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">{{ __('Fallback Behavior') }}</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
                                {{ __('Choose what happens when a customer\'s address is not covered by any specific shipping zone.') }}
                            </p>

                            <div class="space-y-4">
                                @foreach($fallbackOptions as $value => $label)
                                    <div class="flex items-start">
                                        <input type="radio"
                                               id="fallback_{{ $value }}"
                                               name="fallback_behavior"
                                               value="{{ $value }}"
                                               {{ $config['fallback_behavior'] === $value ? 'checked' : '' }}
                                               class="mt-1 h-4 w-4 text-indigo-600 border-gray-300 dark:border-gray-600 dark:bg-gray-900 focus:ring-indigo-500 dark:focus:ring-indigo-600 dark:focus:ring-offset-gray-800">
                                        <div class="ml-3">
                                            <label for="fallback_{{ $value }}" class="text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer">
                                                {{ $label }}
                                            </label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <x-input-error :messages="$errors->get('fallback_behavior')" class="mt-2" />
                        </div>

                        <!-- Dynamic Calculations -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg border border-gray-200 dark:border-gray-600">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">{{ __('Calculation Settings') }}</h3>

                            <div class="space-y-4">
                                <div class="flex items-center">
                                    <input type="checkbox"
                                           id="enable_dynamic_calculations"
                                           name="enable_dynamic_calculations"
                                           value="1"
                                           {{ $config['enable_dynamic_calculations'] ? 'checked' : '' }}
                                           class="rounded dark:bg-gray-900 border-gray-300 dark:border-gray-700 text-indigo-600 shadow-sm focus:ring-indigo-500 dark:focus:ring-indigo-600 dark:focus:ring-offset-gray-800">
                                    <label for="enable_dynamic_calculations" class="ml-2 text-sm text-gray-900 dark:text-gray-100">
                                        {{ __('Enable dynamic calculations for catch-all zones') }}
                                    </label>
                                </div>
                                <x-input-error :messages="$errors->get('enable_dynamic_calculations')" class="mt-1" />

                                <div class="flex items-center">
                                    <input type="checkbox"
                                           id="prioritize_specific_zones"
                                           name="prioritize_specific_zones"
                                           value="1"
                                           {{ $config['prioritize_specific_zones'] ? 'checked' : '' }}
                                           class="rounded dark:bg-gray-900 border-gray-300 dark:border-gray-700 text-indigo-600 shadow-sm focus:ring-indigo-500 dark:focus:ring-indigo-600 dark:focus:ring-offset-gray-800">
                                    <label for="prioritize_specific_zones" class="ml-2 text-sm text-gray-900 dark:text-gray-100">
                                        {{ __('Prioritize specific zones over catch-all zones') }}
                                    </label>
                                </div>
                                <x-input-error :messages="$errors->get('prioritize_specific_zones')" class="mt-1" />

                                <div class="flex items-center">
                                    <input type="checkbox"
                                           id="require_shipping_address"
                                           name="require_shipping_address"
                                           value="1"
                                           {{ $config['require_shipping_address'] ? 'checked' : '' }}
                                           class="rounded dark:bg-gray-900 border-gray-300 dark:border-gray-700 text-indigo-600 shadow-sm focus:ring-indigo-500 dark:focus:ring-indigo-600 dark:focus:ring-offset-gray-800">
                                    <label for="require_shipping_address" class="ml-2 text-sm text-gray-900 dark:text-gray-100">
                                        {{ __('Require shipping address for calculations') }}
                                    </label>
                                </div>
                                <x-input-error :messages="$errors->get('require_shipping_address')" class="mt-1" />

                                <div class="flex items-center">
                                    <input type="checkbox"
                                           id="log_shipping_calculations"
                                           name="log_shipping_calculations"
                                           value="1"
                                           {{ $config['log_shipping_calculations'] ? 'checked' : '' }}
                                           class="rounded dark:bg-gray-900 border-gray-300 dark:border-gray-700 text-indigo-600 shadow-sm focus:ring-indigo-500 dark:focus:ring-indigo-600 dark:focus:ring-offset-gray-800">
                                    <label for="log_shipping_calculations" class="ml-2 text-sm text-gray-900 dark:text-gray-100">
                                        {{ __('Log shipping calculations for debugging') }}
                                    </label>
                                </div>
                                <x-input-error :messages="$errors->get('log_shipping_calculations')" class="mt-1" />
                            </div>
                        </div>

                        <!-- Default Fallback Methods -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg border border-gray-200 dark:border-gray-600">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">{{ __('Default Fallback Methods') }}</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
                                {{ __('These methods are used when no dynamic calculations are available.') }}
                            </p>

                            <div id="fallback-methods" class="space-y-6">
                                @foreach($config['default_fallback_methods'] as $code => $method)
                                    <div class="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 p-6 rounded-lg shadow-sm">
                                        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4 capitalize">{{ $code }} Method</h4>

                                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                            <div>
                                                <x-input-label for="method_code_{{ $code }}" :value="__('Method Code')" />
                                                <x-text-input id="method_code_{{ $code }}"
                                                              name="default_fallback_methods[{{ $code }}][code]"
                                                              type="text"
                                                              class="mt-1 block w-full bg-gray-100 dark:bg-gray-600"
                                                              value="{{ $code }}"
                                                              readonly />
                                            </div>

                                            <div>
                                                <x-input-label for="method_name_{{ $code }}" :value="__('Name')" />
                                                <x-text-input id="method_name_{{ $code }}"
                                                              name="default_fallback_methods[{{ $code }}][name]"
                                                              type="text"
                                                              class="mt-1 block w-full"
                                                              value="{{ $method['name'] }}"
                                                              required />
                                                <x-input-error :messages="$errors->get('default_fallback_methods.'.$code.'.name')" class="mt-2" />
                                            </div>

                                            <div>
                                                <x-input-label for="method_price_{{ $code }}" :value="__('Price')" />
                                                <x-text-input id="method_price_{{ $code }}"
                                                              name="default_fallback_methods[{{ $code }}][price]"
                                                              type="number"
                                                              class="mt-1 block w-full"
                                                              value="{{ $method['price'] }}"
                                                              step="0.01"
                                                              min="0"
                                                              required />
                                                <x-input-error :messages="$errors->get('default_fallback_methods.'.$code.'.price')" class="mt-2" />
                                            </div>

                                            <div>
                                                <x-input-label for="method_type_{{ $code }}" :value="__('Type')" />
                                                <select id="method_type_{{ $code }}"
                                                        name="default_fallback_methods[{{ $code }}][method_type]"
                                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                                                        required>
                                                    <option value="flat_rate" {{ $method['method_type'] === 'flat_rate' ? 'selected' : '' }}>{{ __('Flat Rate') }}</option>
                                                    <option value="weight_based" {{ $method['method_type'] === 'weight_based' ? 'selected' : '' }}>{{ __('Weight Based') }}</option>
                                                    <option value="price_based" {{ $method['method_type'] === 'price_based' ? 'selected' : '' }}>{{ __('Price Based') }}</option>
                                                    <option value="item_based" {{ $method['method_type'] === 'item_based' ? 'selected' : '' }}>{{ __('Item Based') }}</option>
                                                </select>
                                                <x-input-error :messages="$errors->get('default_fallback_methods.'.$code.'.method_type')" class="mt-2" />
                                            </div>
                                        </div>

                                        <div class="mt-4">
                                            <x-input-label for="method_description_{{ $code }}" :value="__('Description')" />
                                            <x-text-input id="method_description_{{ $code }}"
                                                          name="default_fallback_methods[{{ $code }}][description]"
                                                          type="text"
                                                          class="mt-1 block w-full"
                                                          value="{{ $method['description'] ?? '' }}" />
                                            <x-input-error :messages="$errors->get('default_fallback_methods.'.$code.'.description')" class="mt-2" />
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0 pt-6">
                            <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                                <x-primary-button type="submit">
                                    {{ __('Save Configuration') }}
                                </x-primary-button>

                                <a href="{{ route('admin.shipping.configuration.reset') }}"
                                   onclick="return confirm('{{ __('Are you sure you want to reset to defaults?') }}')"
                                   class="inline-flex items-center px-4 py-2 bg-gray-200 dark:bg-gray-700 border border-transparent rounded-md font-semibold text-xs text-gray-800 dark:text-gray-200 uppercase tracking-widest hover:bg-gray-300 dark:hover:bg-gray-600 active:bg-gray-400 dark:active:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150">
                                    {{ __('Reset to Defaults') }}
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Test Configuration -->
                    <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-600">
                        <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg border border-gray-200 dark:border-gray-600">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">{{ __('Test Configuration') }}</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
                                {{ __('Test your shipping configuration with a sample address to see which methods would be available.') }}
                            </p>

                            <form action="{{ route('admin.shipping.configuration.test') }}" method="POST" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                @csrf

                                <div>
                                    <x-input-label for="test_country" :value="__('Country Code')" />
                                    <x-text-input id="test_country"
                                                  name="test_country"
                                                  type="text"
                                                  class="mt-1 block w-full"
                                                  placeholder="US"
                                                  maxlength="2"
                                                  required />
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ __('2-letter ISO code') }}</p>
                                </div>

                                <div>
                                    <x-input-label for="test_region" :value="__('Region/State')" />
                                    <x-text-input id="test_region"
                                                  name="test_region"
                                                  type="text"
                                                  class="mt-1 block w-full"
                                                  placeholder="CA" />
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ __('Optional') }}</p>
                                </div>

                                <div>
                                    <x-input-label for="test_postal_code" :value="__('Postal Code')" />
                                    <x-text-input id="test_postal_code"
                                                  name="test_postal_code"
                                                  type="text"
                                                  class="mt-1 block w-full"
                                                  placeholder="90210" />
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ __('Optional') }}</p>
                                </div>

                                <div class="flex items-end">
                                    <button type="submit"
                                            class="w-full inline-flex items-center px-4 py-2 bg-green-600 dark:bg-green-500 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 dark:hover:bg-green-600 active:bg-green-900 dark:active:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150 justify-center">
                                        {{ __('Test Configuration') }}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-admin-layout>
