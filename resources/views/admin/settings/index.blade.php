<x-admin-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Site Settings') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <form action="{{ route('admin.settings.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Tabs -->
                        <div x-data="{ activeTab: 'general' }">
                            <div class="border-b border-gray-200 dark:border-gray-700 mb-6">
                                <nav class="-mb-px flex space-x-6">
                                    <button type="button" @click="activeTab = 'general'" :class="{ 'border-indigo-500 text-indigo-600 dark:text-indigo-400': activeTab === 'general', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300': activeTab !== 'general' }" class="py-4 px-1 border-b-2 font-medium text-sm">
                                        {{ __('General') }}
                                    </button>
                                    <button type="button" @click="activeTab = 'contact'" :class="{ 'border-indigo-500 text-indigo-600 dark:text-indigo-400': activeTab === 'contact', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300': activeTab !== 'contact' }" class="py-4 px-1 border-b-2 font-medium text-sm">
                                        {{ __('Contact') }}
                                    </button>
                                    <button type="button" @click="activeTab = 'email'" :class="{ 'border-indigo-500 text-indigo-600 dark:text-indigo-400': activeTab === 'email', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300': activeTab !== 'email' }" class="py-4 px-1 border-b-2 font-medium text-sm">
                                        {{ __('Email') }}
                                    </button>
                                    <button type="button" @click="activeTab = 'social'" :class="{ 'border-indigo-500 text-indigo-600 dark:text-indigo-400': activeTab === 'social', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300': activeTab !== 'social' }" class="py-4 px-1 border-b-2 font-medium text-sm">
                                        {{ __('Social Media') }}
                                    </button>
                                    <button type="button" @click="activeTab = 'payments'" :class="{ 'border-indigo-500 text-indigo-600 dark:text-indigo-400': activeTab === 'payments', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300': activeTab !== 'payments' }" class="py-4 px-1 border-b-2 font-medium text-sm">
                                        {{ __('Payments') }}
                                    </button>
                                </nav>
                            </div>

                            <!-- General Settings -->
                            <div x-show="activeTab === 'general'" class="space-y-6">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">{{ __('General Settings') }}</h3>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Company Name -->
                                    <div>
                                        <x-input-label for="company_name" :value="__('Company Name')" />
                                        <x-text-input id="company_name" class="block mt-1 w-full" type="text" name="company_name" :value="old('company_name', \App\Models\Setting::getValue('company_name'))" required />
                                        <x-input-error :messages="$errors->get('company_name')" class="mt-2" />
                                    </div>

                                    <!-- Site Name -->
                                    <div>
                                        <x-input-label for="site_name" :value="__('Site Name')" />
                                        <x-text-input id="site_name" class="block mt-1 w-full" type="text" name="site_name" :value="old('site_name', \App\Models\Setting::getValue('site_name'))" required />
                                        <x-input-error :messages="$errors->get('site_name')" class="mt-2" />
                                    </div>

                                    <!-- Site Description -->
                                    <div>
                                        <x-input-label for="site_description" :value="__('Site Description')" />
                                        <textarea id="site_description" name="site_description" class="block mt-1 w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm" rows="3">{{ old('site_description', \App\Models\Setting::getValue('site_description')) }}</textarea>
                                        <x-input-error :messages="$errors->get('site_description')" class="mt-2" />
                                    </div>

                                    <!-- Primary Color -->
                                    <div>
                                        <x-input-label for="primary_color" :value="__('Primary Color')" />
                                        <div class="flex mt-1">
                                            <input type="color" id="primary_color_picker" class="h-10 w-10 rounded-l-md border-r-0 border-gray-300 dark:border-gray-700" value="{{ old('primary_color', \App\Models\Setting::getValue('primary_color', '#4f46e5')) }}" oninput="document.getElementById('primary_color').value = this.value">
                                            <x-text-input id="primary_color" class="block w-full rounded-l-none" type="text" name="primary_color" :value="old('primary_color', \App\Models\Setting::getValue('primary_color', '#4f46e5'))" required />
                                        </div>
                                        <x-input-error :messages="$errors->get('primary_color')" class="mt-2" />
                                    </div>

                                    <!-- Secondary Color -->
                                    <div>
                                        <x-input-label for="secondary_color" :value="__('Secondary Color')" />
                                        <div class="flex mt-1">
                                            <input type="color" id="secondary_color_picker" class="h-10 w-10 rounded-l-md border-r-0 border-gray-300 dark:border-gray-700" value="{{ old('secondary_color', \App\Models\Setting::getValue('secondary_color', '#7c3aed')) }}" oninput="document.getElementById('secondary_color').value = this.value">
                                            <x-text-input id="secondary_color" class="block w-full rounded-l-none" type="text" name="secondary_color" :value="old('secondary_color', \App\Models\Setting::getValue('secondary_color', '#7c3aed'))" required />
                                        </div>
                                        <x-input-error :messages="$errors->get('secondary_color')" class="mt-2" />
                                    </div>

                                    <!-- Default Language -->
                                    <div>
                                        <x-input-label for="default_language" :value="__('Default Language')" />
                                        <select id="default_language" name="default_language" class="block mt-1 w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm">
                                            <option value="en" {{ old('default_language', \App\Models\Setting::getValue('default_language', 'en')) === 'en' ? 'selected' : '' }}>{{ __('English') }}</option>
                                            <option value="fr" {{ old('default_language', \App\Models\Setting::getValue('default_language', 'en')) === 'fr' ? 'selected' : '' }}>{{ __('French') }}</option>
                                            <option value="es" {{ old('default_language', \App\Models\Setting::getValue('default_language', 'en')) === 'es' ? 'selected' : '' }}>{{ __('Spanish') }}</option>
                                        </select>
                                        <x-input-error :messages="$errors->get('default_language')" class="mt-2" />
                                    </div>

                                    <!-- Default Currency -->
                                    <div>
                                        <x-input-label for="default_currency" :value="__('Default Currency')" />
                                        <select id="default_currency" name="default_currency" class="block mt-1 w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm">
                                            <option value="USD" {{ old('default_currency', \App\Models\Setting::getValue('default_currency', 'USD')) === 'USD' ? 'selected' : '' }}>{{ __('US Dollar (USD)') }}</option>
                                            <option value="EUR" {{ old('default_currency', \App\Models\Setting::getValue('default_currency', 'USD')) === 'EUR' ? 'selected' : '' }}>{{ __('Euro (EUR)') }}</option>
                                            <option value="GBP" {{ old('default_currency', \App\Models\Setting::getValue('default_currency', 'USD')) === 'GBP' ? 'selected' : '' }}>{{ __('British Pound (GBP)') }}</option>
                                        </select>
                                        <x-input-error :messages="$errors->get('default_currency')" class="mt-2" />
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Settings -->
                            <div x-show="activeTab === 'contact'" class="space-y-6" style="display: none;">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">{{ __('Contact Information') }}</h3>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Contact Email -->
                                    <div>
                                        <x-input-label for="contact_email" :value="__('Contact Email')" />
                                        <x-text-input id="contact_email" class="block mt-1 w-full" type="email" name="contact_email" :value="old('contact_email', \App\Models\Setting::getValue('contact_email'))" required />
                                        <x-input-error :messages="$errors->get('contact_email')" class="mt-2" />
                                    </div>

                                    <!-- Contact Phone -->
                                    <div>
                                        <x-input-label for="contact_phone" :value="__('Contact Phone')" />
                                        <x-text-input id="contact_phone" class="block mt-1 w-full" type="text" name="contact_phone" :value="old('contact_phone', \App\Models\Setting::getValue('contact_phone'))" required />
                                        <x-input-error :messages="$errors->get('contact_phone')" class="mt-2" />
                                    </div>

                                    <!-- Secondary Contact Phone -->
                                    <div>
                                        <x-input-label for="contact_phone_secondary" :value="__('Secondary Phone (Optional)')" />
                                        <x-text-input id="contact_phone_secondary" class="block mt-1 w-full" type="text" name="contact_phone_secondary" :value="old('contact_phone_secondary', \App\Models\Setting::getValue('contact_phone_secondary'))" />
                                        <x-input-error :messages="$errors->get('contact_phone_secondary')" class="mt-2" />
                                    </div>

                                    <!-- Contact Address -->
                                    <div class="md:col-span-2">
                                        <x-input-label for="contact_address" :value="__('Contact Address')" />
                                        <textarea id="contact_address" name="contact_address" class="block mt-1 w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm" rows="3">{{ old('contact_address', \App\Models\Setting::getValue('contact_address')) }}</textarea>
                                        <x-input-error :messages="$errors->get('contact_address')" class="mt-2" />
                                    </div>

                                    <!-- Google Maps Embed -->
                                    <div class="md:col-span-2">
                                        <x-input-label for="google_maps_embed" :value="__('Google Maps Embed Code')" />
                                        <textarea id="google_maps_embed" name="google_maps_embed" class="block mt-1 w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm" rows="5">{{ old('google_maps_embed', \App\Models\Setting::getValue('google_maps_embed', '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2624.9916256937595!2d2.292292615509614!3d48.85837007928746!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x47e66e2964e34e2d%3A0x8ddca9ee380ef7e0!2sEiffel%20Tower!5e0!3m2!1sen!2sus!4v1619712826145!5m2!1sen!2sus" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy"></iframe>')) }}</textarea>
                                        <x-input-error :messages="$errors->get('google_maps_embed')" class="mt-2" />
                                    </div>
                                </div>
                            </div>

                            <!-- Email Settings -->
                            <div x-show="activeTab === 'email'" class="space-y-6" style="display: none;">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">{{ __('Email Configuration') }}</h3>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Mail From Address -->
                                    <div>
                                        <x-input-label for="mail_from_address" :value="__('From Address')" />
                                        <x-text-input id="mail_from_address" class="block mt-1 w-full" type="email" name="mail_from_address" :value="old('mail_from_address', \App\Models\Setting::getValue('mail_from_address'))" required />
                                        <x-input-error :messages="$errors->get('mail_from_address')" class="mt-2" />
                                    </div>

                                    <!-- Mail From Name -->
                                    <div>
                                        <x-input-label for="mail_from_name" :value="__('From Name')" />
                                        <x-text-input id="mail_from_name" class="block mt-1 w-full" type="text" name="mail_from_name" :value="old('mail_from_name', \App\Models\Setting::getValue('mail_from_name'))" required />
                                        <x-input-error :messages="$errors->get('mail_from_name')" class="mt-2" />
                                    </div>

                                    <!-- Mail Driver -->
                                    <div>
                                        <x-input-label for="mail_driver" :value="__('Mail Driver')" />
                                        <select id="mail_driver" name="mail_driver" class="block mt-1 w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm">
                                            <option value="smtp" {{ old('mail_driver', \App\Models\Setting::getValue('mail_driver', 'smtp')) === 'smtp' ? 'selected' : '' }}>{{ __('SMTP') }}</option>
                                            <option value="sendmail" {{ old('mail_driver', \App\Models\Setting::getValue('mail_driver', 'smtp')) === 'sendmail' ? 'selected' : '' }}>{{ __('Sendmail') }}</option>
                                            <option value="mailgun" {{ old('mail_driver', \App\Models\Setting::getValue('mail_driver', 'smtp')) === 'mailgun' ? 'selected' : '' }}>{{ __('Mailgun') }}</option>
                                        </select>
                                        <x-input-error :messages="$errors->get('mail_driver')" class="mt-2" />
                                    </div>

                                    <!-- Mail Host -->
                                    <div>
                                        <x-input-label for="mail_host" :value="__('Mail Host')" />
                                        <x-text-input id="mail_host" class="block mt-1 w-full" type="text" name="mail_host" :value="old('mail_host', \App\Models\Setting::getValue('mail_host', 'smtp.mailtrap.io'))" />
                                        <x-input-error :messages="$errors->get('mail_host')" class="mt-2" />
                                    </div>

                                    <!-- Mail Port -->
                                    <div>
                                        <x-input-label for="mail_port" :value="__('Mail Port')" />
                                        <x-text-input id="mail_port" class="block mt-1 w-full" type="text" name="mail_port" :value="old('mail_port', \App\Models\Setting::getValue('mail_port', '2525'))" />
                                        <x-input-error :messages="$errors->get('mail_port')" class="mt-2" />
                                    </div>

                                    <!-- Mail Encryption -->
                                    <div>
                                        <x-input-label for="mail_encryption" :value="__('Mail Encryption')" />
                                        <select id="mail_encryption" name="mail_encryption" class="block mt-1 w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm">
                                            <option value="tls" {{ old('mail_encryption', \App\Models\Setting::getValue('mail_encryption', 'tls')) === 'tls' ? 'selected' : '' }}>{{ __('TLS') }}</option>
                                            <option value="ssl" {{ old('mail_encryption', \App\Models\Setting::getValue('mail_encryption', 'tls')) === 'ssl' ? 'selected' : '' }}>{{ __('SSL') }}</option>
                                            <option value="none" {{ old('mail_encryption', \App\Models\Setting::getValue('mail_encryption', 'tls')) === 'none' ? 'selected' : '' }}>{{ __('None') }}</option>
                                        </select>
                                        <x-input-error :messages="$errors->get('mail_encryption')" class="mt-2" />
                                    </div>

                                    <!-- Mail Username -->
                                    <div>
                                        <x-input-label for="mail_username" :value="__('Mail Username')" />
                                        <x-text-input id="mail_username" class="block mt-1 w-full" type="text" name="mail_username" :value="old('mail_username', \App\Models\Setting::getValue('mail_username', 'your-username'))" />
                                        <x-input-error :messages="$errors->get('mail_username')" class="mt-2" />
                                    </div>

                                    <!-- Mail Password -->
                                    <div>
                                        <x-input-label for="mail_password" :value="__('Mail Password')" />
                                        <x-text-input id="mail_password" class="block mt-1 w-full" type="password" name="mail_password" :value="old('mail_password', \App\Models\Setting::getValue('mail_password', 'your-password'))" />
                                        <x-input-error :messages="$errors->get('mail_password')" class="mt-2" />
                                    </div>
                                </div>
                            </div>

                            <!-- Social Media Settings -->
                            <div x-show="activeTab === 'social'" class="space-y-6" style="display: none;">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">{{ __('Social Media Links') }}</h3>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Facebook URL -->
                                    <div>
                                        <x-input-label for="facebook_url" :value="__('Facebook URL')" />
                                        <x-text-input id="facebook_url" class="block mt-1 w-full" type="url" name="facebook_url" :value="old('facebook_url', \App\Models\Setting::getValue('facebook_url', 'https://facebook.com/wisdomtechno'))" />
                                        <x-input-error :messages="$errors->get('facebook_url')" class="mt-2" />
                                    </div>

                                    <!-- Twitter URL -->
                                    <div>
                                        <x-input-label for="twitter_url" :value="__('Twitter URL')" />
                                        <x-text-input id="twitter_url" class="block mt-1 w-full" type="url" name="twitter_url" :value="old('twitter_url', \App\Models\Setting::getValue('twitter_url', 'https://twitter.com/wisdomtechno'))" />
                                        <x-input-error :messages="$errors->get('twitter_url')" class="mt-2" />
                                    </div>

                                    <!-- Instagram URL -->
                                    <div>
                                        <x-input-label for="instagram_url" :value="__('Instagram URL')" />
                                        <x-text-input id="instagram_url" class="block mt-1 w-full" type="url" name="instagram_url" :value="old('instagram_url', \App\Models\Setting::getValue('instagram_url', 'https://instagram.com/wisdomtechno'))" />
                                        <x-input-error :messages="$errors->get('instagram_url')" class="mt-2" />
                                    </div>

                                    <!-- LinkedIn URL -->
                                    <div>
                                        <x-input-label for="linkedin_url" :value="__('LinkedIn URL')" />
                                        <x-text-input id="linkedin_url" class="block mt-1 w-full" type="url" name="linkedin_url" :value="old('linkedin_url', \App\Models\Setting::getValue('linkedin_url'))" />
                                        <x-input-error :messages="$errors->get('linkedin_url')" class="mt-2" />
                                    </div>

                                    <!-- YouTube URL -->
                                    <div>
                                        <x-input-label for="youtube_url" :value="__('YouTube URL')" />
                                        <x-text-input id="youtube_url" class="block mt-1 w-full" type="url" name="youtube_url" :value="old('youtube_url', \App\Models\Setting::getValue('youtube_url', 'https://youtube.com/c/wisdomtechno'))" />
                                        <x-input-error :messages="$errors->get('youtube_url')" class="mt-2" />
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Settings -->
                            <div x-show="activeTab === 'payments'" class="space-y-6" style="display: none;">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">{{ __('Payment Configuration') }}</h3>

                                <!-- Partial Payments Section -->
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                    <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">{{ __('Partial Payments') }}</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
                                        {{ __('Configure whether customers can make partial payments for their orders. When disabled, customers must pay the full amount.') }}
                                    </p>

                                    <div class="space-y-6">
                                        <!-- Enable Partial Payments -->
                                        <div class="flex items-center justify-between">
                                            <div class="flex-1">
                                                <label for="payments.partial_payments_enabled" class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                    {{ __('Enable Partial Payments') }}
                                                </label>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ __('Allow customers to pay a portion of their order total') }}
                                                </p>
                                            </div>
                                            <div class="ml-4">
                                                <!-- Hidden input to ensure the field is always submitted -->
                                                <input type="hidden" name="payments.partial_payments_enabled" value="false">

                                                <label for="payments.partial_payments_enabled" class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox"
                                                           id="payments.partial_payments_enabled"
                                                           name="payments.partial_payments_enabled"
                                                           value="true"
                                                           class="sr-only peer"
                                                           {{ setting('payments.partial_payments_enabled', false) ? 'checked' : '' }}>
                                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                                                </label>
                                            </div>
                                        </div>

                                        <!-- Partial Payment Settings (shown when enabled) -->
                                        <div id="partial-payment-settings" class="space-y-4 {{ setting('payments.partial_payments_enabled', false) ? '' : 'opacity-50 pointer-events-none' }}">
                                            <!-- Minimum Percentage -->
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div>
                                                    <x-input-label for="payments.partial_payment_minimum_percentage" :value="__('Minimum Payment Percentage')" />
                                                    <div class="mt-1 relative">
                                                        <x-text-input
                                                            id="payments.partial_payment_minimum_percentage"
                                                            class="block w-full pr-8"
                                                            type="number"
                                                            name="payments.partial_payment_minimum_percentage"
                                                            :value="old('payments.partial_payment_minimum_percentage', setting('payments.partial_payment_minimum_percentage', 50))"
                                                            min="1"
                                                            max="99"
                                                            required />
                                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                                            <span class="text-gray-500 dark:text-gray-400 text-sm">%</span>
                                                        </div>
                                                    </div>
                                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                                        {{ __('Minimum percentage of total amount required for partial payments (1-99%)') }}
                                                    </p>
                                                    <x-input-error :messages="$errors->get('payments.partial_payment_minimum_percentage')" class="mt-2" />
                                                </div>

                                                <!-- Grace Period -->
                                                <div>
                                                    <x-input-label for="payments.partial_payment_grace_period_days" :value="__('Grace Period (Days)')" />
                                                    <x-text-input
                                                        id="payments.partial_payment_grace_period_days"
                                                        class="block mt-1 w-full"
                                                        type="number"
                                                        name="payments.partial_payment_grace_period_days"
                                                        :value="old('payments.partial_payment_grace_period_days', setting('payments.partial_payment_grace_period_days', 7))"
                                                        min="1"
                                                        max="365"
                                                        required />
                                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                                        {{ __('Number of days customers have to complete remaining payment (1-365 days)') }}
                                                    </p>
                                                    <x-input-error :messages="$errors->get('payments.partial_payment_grace_period_days')" class="mt-2" />
                                                </div>
                                            </div>

                                            <!-- Example Calculation -->
                                            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                                                <h5 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">{{ __('Example') }}</h5>
                                                <p class="text-sm text-blue-800 dark:text-blue-200">
                                                    {{ __('For a $100 order with') }} <span id="example-percentage">{{ setting('payments.partial_payment_minimum_percentage', 50) }}%</span> {{ __('minimum:') }}
                                                </p>
                                                <ul class="mt-2 text-sm text-blue-700 dark:text-blue-300 space-y-1">
                                                    <li>• {{ __('Minimum payment required:') }} $<span id="example-amount">{{ number_format(100 * setting('payments.partial_payment_minimum_percentage', 50) / 100, 2) }}</span></li>
                                                    <li>• {{ __('Customer has') }} <span id="example-days">{{ setting('payments.partial_payment_grace_period_days', 7) }}</span> {{ __('days to pay remaining amount') }}</li>
                                                    <li>• {{ __('Order status will be "Awaiting Payment" until completed') }}</li>
                                                </ul>
                                            </div>

                                            <!-- Warning Notice -->
                                            <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
                                                <div class="flex">
                                                    <div class="flex-shrink-0">
                                                        <svg class="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                        </svg>
                                                    </div>
                                                    <div class="ml-3">
                                                        <h5 class="text-sm font-medium text-amber-800 dark:text-amber-200">{{ __('Important Notice') }}</h5>
                                                        <p class="mt-1 text-sm text-amber-700 dark:text-amber-300">
                                                            {{ __('Partial payments add complexity to order management. Ensure your fulfillment process can handle orders with partial payments appropriately.') }}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center justify-end mt-6">
                            <x-primary-button>
                                {{ __('Save Settings') }}
                            </x-primary-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle partial payment settings visibility
        function togglePartialPaymentSettings(enabled) {
            const settingsDiv = document.getElementById('partial-payment-settings');
            if (enabled) {
                settingsDiv.classList.remove('opacity-50', 'pointer-events-none');
            } else {
                settingsDiv.classList.add('opacity-50', 'pointer-events-none');
            }
        }

        // Update example calculations in real-time
        function updateExample() {
            const percentage = document.getElementById('payments.partial_payment_minimum_percentage').value || 50;
            const days = document.getElementById('payments.partial_payment_grace_period_days').value || 7;

            document.getElementById('example-percentage').textContent = percentage + '%';
            document.getElementById('example-amount').textContent = (100 * percentage / 100).toFixed(2);
            document.getElementById('example-days').textContent = days;
        }

        // Add event listeners when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Update example when inputs change
            const percentageInput = document.getElementById('payments.partial_payment_minimum_percentage');
            const daysInput = document.getElementById('payments.partial_payment_grace_period_days');

            if (percentageInput) {
                percentageInput.addEventListener('input', updateExample);
            }

            if (daysInput) {
                daysInput.addEventListener('input', updateExample);
            }

            // Handle checkbox state for partial payments
            const checkbox = document.getElementById('payments.partial_payments_enabled');
            const toggleDiv = checkbox ? checkbox.nextElementSibling : null;

            if (checkbox && toggleDiv) {
                // Set initial state
                togglePartialPaymentSettings(checkbox.checked);

                // Handle changes via the checkbox
                checkbox.addEventListener('change', function() {
                    togglePartialPaymentSettings(this.checked);
                });

                // Also handle clicks on the visual toggle div
                toggleDiv.addEventListener('click', function(e) {
                    e.preventDefault();
                    checkbox.checked = !checkbox.checked;
                    togglePartialPaymentSettings(checkbox.checked);

                    // Trigger change event
                    checkbox.dispatchEvent(new Event('change'));
                });
            }
        });
    </script>
</x-admin-layout>
