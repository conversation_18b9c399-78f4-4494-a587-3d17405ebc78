# Company Name Centralization Implementation

## Overview
This implementation centralizes static company information across the Laravel application views using the existing `\App\Models\Setting` system and `ContentBlock` facade for better maintainability and configurability.

## Changes Made

### 1. Database Migration
- **File**: `database/migrations/2025_05_24_104617_add_company_name_setting.php`
- **Purpose**: Adds `company_name` setting to the database if it doesn't exist
- **Default Value**: "WisdomTechno"

### 2. Settings Seeder Update
- **File**: `database/seeders/SettingSeeder.php`
- **Change**: Added `company_name` setting as the first setting in the general category
- **Value**: "WisdomTechno"

### 3. Terms Page Refactoring
- **File**: `resources/views/pages/terms.blade.php`
- **Changes**: Replaced 6 hardcoded "WisdomTechno" references with `\App\Models\Setting::getValue('company_name', 'WisdomTechno')`
- **Locations**:
  - Introduction section: "Welcome to {company_name}"
  - Definitions: "Refers to {company_name}"
  - Services definition: "offered by {company_name}"
  - Intellectual Property: "property of {company_name}"
  - Limitation of Liability: "{company_name} shall not be liable"
  - Indemnification: "hold harmless {company_name}"

### 4. About Page Enhancement
- **File**: `resources/views/pages/about.blade.php`
- **Changes**: Updated ContentBlock defaults to use company_name setting
- **Locations**:
  - Page title: `'About ' . \App\Models\Setting::getValue('company_name', 'WisdomTechno')`
  - Hero description: Company name integrated into default text
  - Approach intro: "At {company_name}, we take..."

### 5. Admin Settings Form Updates
- **File**: `resources/views/admin/settings/index.blade.php`
- **Changes**:
  - Added `company_name` field to General Settings tab
  - Removed hardcoded defaults from all setting fields
  - Contact settings now use `\App\Models\Setting::getValue()` without defaults
  - Email settings cleaned up
  - Social media settings cleaned up

### 6. Checkout Confirmation Updates
- **File**: `resources/views/checkout/confirmation.blade.php`
- **Changes**:
  - Support email: `mailto:{{ \App\Models\Setting::getValue('contact_email', '<EMAIL>') }}`
  - Support phone: `tel:{{ \App\Models\Setting::getValue('contact_phone', '+1234567890') }}`

### 7. Footer Component Refactoring
- **File**: `resources/views/components/footer.blade.php`
- **Changes**:
  - Company name in header: `{{ \App\Models\Setting::getValue('company_name', 'WisdomTechno') }}`
  - Site description: `{{ \App\Models\Setting::getValue('site_description', '...') }}`
  - About section: Company name integrated into description
  - Contact email: Uses `contact_email` setting with mailto link
  - Contact phone: Uses `contact_phone` setting
  - Secondary phone: Uses `contact_phone_secondary` setting (optional, only shows if set)
  - Contact address: Uses `contact_address` setting
  - Copyright: `{{ \App\Models\Setting::getValue('company_name', 'WisdomTechno') }}`

## Benefits

### 1. Centralized Management
- All company information is now managed from a single location (admin settings)
- No need to edit multiple template files when company details change

### 2. Backward Compatibility
- All changes include fallback defaults to maintain functionality
- Existing installations will continue to work without issues

### 3. Production Ready
- Proper error handling with fallback values
- Database migration ensures setting exists
- Comprehensive test coverage

### 4. Maintainability
- DRY principle: No data duplication across templates
- Consistent styling and design patterns maintained
- Easy to extend with additional company settings

## Usage

### Admin Interface
1. Navigate to Admin → Settings
2. Go to "General" tab
3. Update "Company Name" field
4. Changes will reflect across all pages immediately

### Programmatic Access
```php
// Get company name with fallback
$companyName = \App\Models\Setting::getValue('company_name', 'Default Company');

// Set company name
\App\Models\Setting::setValue('company_name', 'New Company Name', 'general', 'text');
```

## Testing
Comprehensive test suite created in:
- `tests/Feature/CompanyNameSettingTest.php`
- `tests/Feature/ContentBlockCompanyIntegrationTest.php`
- `tests/Feature/TermsPageCompanyNameTest.php`
- `tests/Feature/FooterCompanyNameTest.php`

Tests cover:
- Setting creation, retrieval, and updates
- Admin form functionality
- Page rendering with dynamic company names
- ContentBlock integration
- Edge cases and error handling

## Migration Instructions

### For New Installations
1. Run `php artisan migrate` - the migration will create the setting automatically
2. Run `php artisan db:seed` - the seeder will populate the default value

### For Existing Installations
1. Run `php artisan migrate` - will add company_name setting if not exists
2. Update company name via admin interface if needed

## Files Modified
1. `database/seeders/SettingSeeder.php`
2. `database/migrations/2025_05_24_104617_add_company_name_setting.php` (new)
3. `resources/views/pages/terms.blade.php`
4. `resources/views/pages/about.blade.php`
5. `resources/views/admin/settings/index.blade.php`
6. `resources/views/checkout/confirmation.blade.php`
7. `resources/views/components/footer.blade.php`
8. Test files (new)

## Future Enhancements
- Add more company settings (logo, tagline, etc.)
- Implement caching for frequently accessed settings
- Add validation rules for company information
- Create API endpoints for external access to company data
